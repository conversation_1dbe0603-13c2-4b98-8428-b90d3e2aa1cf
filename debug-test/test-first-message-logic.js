const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// 复制isFirstUserMessage函数进行独立测试
async function isFirstUserMessage(sessionId) {
    console.log(`🔍 检查会话 ${sessionId} 是否为第一句用户消息...`);
    
    const supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_ANON_KEY
    );

    try {
        const { data, error } = await supabase
            .from('chat_messages')
            .select('id')
            .eq('session_id', sessionId)
            .eq('sender', 'user');

        if (error) {
            console.error('❌ 数据库查询错误:', error);
            return false;
        }

        console.log(`📊 查询结果: 找到 ${data.length} 条用户消息`);
        console.log('📝 详细数据:', data);
        
        const isFirst = data.length === 0;
        console.log(`✅ 判断结果: ${isFirst ? '是第一句' : '不是第一句'}`);
        
        return isFirst;
    } catch (error) {
        console.error('❌ 函数执行错误:', error);
        return false;
    }
}

// 测试不同场景
async function testFirstMessageLogic() {
    console.log('🧪 测试 isFirstUserMessage 函数逻辑...\n');

    // 1. 测试现有会话
    const supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_ANON_KEY
    );

    const { data: sessions } = await supabase
        .from('chat_sessions')
        .select('id')
        .limit(3);

    if (sessions && sessions.length > 0) {
        console.log('📋 测试现有会话:');
        for (const session of sessions) {
            await isFirstUserMessage(session.id);
            console.log('---');
        }
    }

    // 2. 测试新会话ID
    console.log('\n🆕 测试新会话ID:');
    const newSessionId = 'test-new-session-' + Date.now();
    await isFirstUserMessage(newSessionId);

    // 3. 测试UUID格式的新会话ID
    console.log('\n🔗 测试UUID格式的新会话ID:');
    const uuidSessionId = '550e8400-e29b-41d4-a716-************';
    await isFirstUserMessage(uuidSessionId);
}

// 运行测试
testFirstMessageLogic().catch(console.error);
