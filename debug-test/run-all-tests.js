// 运行所有调试测试
async function runAllTests() {
    console.log('🚀 开始运行所有调试测试...\n');
    console.log('=' * 50);

    try {
        // 1. 数据库状态检查
        console.log('📊 1. 数据库状态检查');
        console.log('=' * 30);
        await require('./test-database-state.js');
        
        console.log('\n' + '=' * 50);
        
        // 2. 第一句消息逻辑测试
        console.log('🧠 2. 第一句消息逻辑测试');
        console.log('=' * 30);
        await require('./test-first-message-logic.js');
        
        console.log('\n' + '=' * 50);
        
        // 3. 完整API流程测试
        console.log('🌐 3. 完整API流程测试');
        console.log('=' * 30);
        await require('./test-api-flow.js');
        
        console.log('\n' + '=' * 50);
        console.log('✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试执行错误:', error);
    }
}

// 运行
runAllTests();
