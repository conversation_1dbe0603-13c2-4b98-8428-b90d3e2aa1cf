const axios = require('axios');

// 测试完整的API流程
async function testAPIFlow() {
    console.log('🌐 测试完整API流程...\n');

    const baseURL = 'http://localhost:3000';

    // 1. 健康检查
    console.log('❤️ 1. 健康检查:');
    try {
        const healthResponse = await axios.get(`${baseURL}/api/health`);
        console.log('✅ 健康检查成功:', healthResponse.data);
    } catch (error) {
        console.error('❌ 健康检查失败:', error.message);
        return;
    }

    // 2. 测试第一句消息
    console.log('\n💬 2. 测试第一句消息:');
    const testCases = [
        {
            name: '基础问候',
            message: '你好',
            sessionId: 'test-first-' + Date.now(),
            userEmail: '<EMAIL>'
        },
        {
            name: '职位询问',
            message: '有什么职位推荐吗',
            sessionId: 'test-job-' + Date.now(),
            userEmail: '<EMAIL>'
        },
        {
            name: 'UUID会话',
            message: '你好',
            sessionId: '550e8400-e29b-41d4-a716-' + Date.now().toString().slice(-12),
            userEmail: '<EMAIL>'
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n🧪 测试: ${testCase.name}`);
        console.log(`📝 输入: "${testCase.message}"`);
        console.log(`🔗 会话ID: ${testCase.sessionId}`);
        
        try {
            const response = await axios.post(`${baseURL}/api/chat`, testCase);
            console.log('✅ API响应成功:');
            console.log('📊 响应数据:', JSON.stringify(response.data, null, 2));
            
            // 检查是否是第一句回复
            if (response.data.responseType) {
                console.log(`🎯 响应类型: ${response.data.responseType}`);
                if (response.data.responseType.includes('first')) {
                    console.log('✅ 正确识别为第一句');
                } else {
                    console.log('❌ 未识别为第一句');
                }
            }
        } catch (error) {
            console.error('❌ API请求失败:', error.response?.data || error.message);
        }
        
        console.log('---');
    }
}

// 运行测试
testAPIFlow().catch(console.error);
