# 数据库结构参考手册

**生成时间**: 2025 年 7 月 27 日
**用途**: 开发时快速查阅数据库表结构，避免字段名错误

---

## 🔍 核心表结构

### 1. users (用户表)

- **记录数**: 379 ⬆️ (增长 166%)
- **字段**:
  - `id` (integer) - 主键
  - `email` (text) - 邮箱
  - `user_type` (text) - 用户类型
  - `created_at` (timestamp) - 创建时间
  - `last_login_at` (nullable) - 最后登录时间
  - `is_active` (boolean) - 是否激活

### 2. chat_sessions (会话表)

- **记录数**: 566 ⬆️ (增长 153%)
- **字段**:
  - `id` (integer) - 主键
  - `user_id` (integer) - 用户 ID
  - `session_uuid` (uuid) - 会话 UUID（外部标识）
  - `entry_source_url` (text) - 入口来源
  - `initial_intent` (text) - 初始意图
  - `current_interaction_context` (json) - 当前交互上下文
  - `last_active_at` (timestamp) - 最后活跃时间
  - `created_at` (timestamp) - 创建时间

### 3. chat_messages (消息表) ⭐ 重要

- **记录数**: 2618 ⬆️ (增长 135%)
- **字段**:
  - `id` (integer) - 主键
  - `session_id` (integer) - 会话 ID（关联 chat_sessions.id）
  - `message_type` (text) - 消息类型（user/assistant）
  - `message_content` (text) - 消息内容
  - `metadata_json` (json) - 元数据
  - `timestamp` (timestamp) - 时间戳

### 4. candidate_profiles (候选人档案表)

- **记录数**: 157 ⬆️ (增长 99%)
- **关键字段**:
  - `id`, `user_id`
  - `current_company_name_raw`, `current_company_id`
  - `candidate_level_raw`, `candidate_standard_level_min/max`
  - `primary_tech_direction_id`, `candidate_tech_direction_raw`
  - `primary_business_scenario_id`, `candidate_business_scenario_raw` 🆕
  - `expected_compensation_min/max`
  - `profile_completeness_score`

### 5. companies (公司表)

- **记录数**: 79
- **字段**:
  - `id`, `company_name`, `company_type`
  - `industry`, `description`, `website`
  - `logo_url`, `is_blocked`

### 6. job_listings (职位表)

- **记录数**: 50 (稳定)
- **关键字段**:
  - `id`, `job_title`, `company_id`
  - `salary_min/max`, `experience_required`
  - `primary_tech_direction_id`
  - `primary_business_scenario_id` 🆕
  - `job_level_raw`, `job_standard_level_min/max`
  - `job_description`, `requirements`, `benefits`
  - `is_active`, `priority_level`

### 7. tech_tree (技术树表) 🔥 核心

- **记录数**: 648 (稳定)
- **层级结构**: 3 级技术树 (35 个 1 级 + 165 个 2 级 + 448 个 3 级)
- **字段**:
  - `id`, `tech_name`, `category`
  - `parent_tech_id`, `level`
  - `description`, `keywords`

### 8. business_scenarios (业务场景表) 🔥 核心

- **记录数**: 115 ⬆️ (增长 423%)
- **层级结构**: 3 级业务场景 (15 个 1 级 + 30 个 2 级 + 70 个 3 级)
- **字段**:
  - `id`, `scenario_name`, `category`
  - `parent_scenario_id`, `level`
  - `description`, `keywords`, `business_value`

---

## 🔧 常用查询模式

### 获取对话历史

```sql
SELECT * FROM chat_messages
WHERE session_id = ?
ORDER BY timestamp DESC
LIMIT 10;
```

### 获取会话信息

```sql
SELECT * FROM chat_sessions
WHERE session_uuid = ?;
```

### 查询匹配职位

```sql
SELECT jl.*, c.company_name, c.company_type
FROM job_listings jl
JOIN companies c ON jl.company_id = c.id
WHERE jl.primary_tech_direction_id = ?
AND jl.is_active = true;
```

---

## ⚠️ 重要注意事项

1. **会话管理**:

   - 外部使用 `session_uuid` (UUID 格式)
   - 内部关联使用 `session_id` (整数)

2. **消息存储**:

   - 使用 `session_id` 而不是 `user_id`
   - `metadata_json` 存储结构化数据

3. **字段命名**:

   - 数据库使用下划线命名 (`session_id`)
   - 不要使用驼峰命名 (`sessionId`)

4. **常见错误**:
   - ❌ `chat_messages.user_id` (不存在)
   - ✅ `chat_messages.session_id` (正确)
   - ❌ `TABLES.CHAT_MESSAGES` (可能过时)
   - ✅ `'chat_messages'` (直接使用表名)

---

## 📊 数据统计 (更新于 2025-07-27)

| 表名               | 记录数 | 变化     | 状态 |
| ------------------ | ------ | -------- | ---- |
| users              | 379    | ⬆️ +236  | ✅   |
| chat_sessions      | 566    | ⬆️ +342  | ✅   |
| chat_messages      | 2618   | ⬆️ +1504 | ✅   |
| candidate_profiles | 157    | ⬆️ +78   | ✅   |
| companies          | 79     | ➡️ 稳定  | ✅   |
| job_listings       | 50     | ➡️ 稳定  | ✅   |
| tech_tree          | 648    | ➡️ 稳定  | ✅   |
| business_scenarios | 115    | ⬆️ +93   | ✅   |

## 🆕 新增功能

### 业务场景映射系统

- **job_listings** 表新增 `primary_business_scenario_id` 字段
- **candidate_profiles** 表新增 `primary_business_scenario_id` 和 `candidate_business_scenario_raw` 字段
- **business_scenarios** 表扩展为 115 个场景，支持 3 级层级结构

---

## 🎯 开发建议

1. **优先使用实际字段名**，避免依赖常量文件
2. **会话管理**已修复，可以进行连续对话测试
3. **数据库结构稳定**，可以开始完整功能测试
4. **字段映射**参考上面的结构，避免查询错误

---

_此文件基于实际数据库结构生成，请在开发时优先参考此文档_
