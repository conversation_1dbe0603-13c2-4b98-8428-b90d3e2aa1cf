# 数据库结构参考手册

**生成时间**: 2025 年 7 月 27 日
**用途**: 开发时快速查阅数据库表结构，避免字段名错误

---

## 🔍 核心表结构

### 1. users (用户表)

- **记录数**: 379 ⬆️ (增长 166%)
- **字段**:
  - `id` (integer) - 主键
  - `email` (text) - 邮箱
  - `user_type` (text) - 用户类型
  - `created_at` (timestamp) - 创建时间
  - `last_login_at` (nullable) - 最后登录时间
  - `is_active` (boolean) - 是否激活

### 2. chat_sessions (会话表)

- **记录数**: 566 ⬆️ (增长 153%)
- **字段**:
  - `id` (integer) - 主键
  - `user_id` (integer) - 用户 ID
  - `session_uuid` (uuid) - 会话 UUID（外部标识）
  - `entry_source_url` (text) - 入口来源
  - `initial_intent` (text) - 初始意图
  - `current_interaction_context` (json) - 当前交互上下文
  - `last_active_at` (timestamp) - 最后活跃时间
  - `created_at` (timestamp) - 创建时间

### 3. chat_messages (消息表) ⭐ 重要

- **记录数**: 2618 ⬆️ (增长 135%)
- **字段**:
  - `id` (integer) - 主键
  - `session_id` (integer) - 会话 ID（关联 chat_sessions.id）
  - `message_type` (text) - 消息类型（user/assistant）
  - `message_content` (text) - 消息内容
  - `metadata_json` (json) - 元数据
  - `timestamp` (timestamp) - 时间戳

### 4. candidate_profiles (候选人档案表)

- **记录数**: 157 ⬆️ (增长 99%)
- **关键字段**:
  - `id`, `user_id`
  - `current_company_name_raw`, `current_company_id`
  - `candidate_level_raw`, `candidate_standard_level_min/max`
  - `primary_tech_direction_id`, `candidate_tech_direction_raw`
  - `primary_business_scenario_id`, `candidate_business_scenario_raw` 🆕
  - `expected_compensation_min/max`
  - `profile_completeness_score`

### 5. companies (公司表)

- **记录数**: 79
- **字段**:
  - `id`, `company_name`, `company_type`
  - `industry`, `description`, `website`
  - `logo_url`, `is_blocked`

### 6. job_listings (职位表)

- **记录数**: 50 (稳定)
- **关键字段**:
  - `id`, `job_title`, `company_id`
  - `salary_min/max`, `experience_required`
  - `primary_tech_direction_id`
  - `primary_business_scenario_id` 🆕
  - `job_level_raw`, `job_standard_level_min/max`
  - `job_description`, `requirements`, `benefits`
  - `is_active`, `priority_level`

### 7. tech_tree (技术树表) 🔥 核心

- **记录数**: 648 (稳定)
- **层级结构**: 3 级技术树 (35 个 1 级 + 165 个 2 级 + 448 个 3 级)
- **字段**:
  - `id`, `tech_name`, `category`
  - `parent_tech_id`, `level`
  - `description`, `keywords`

### 8. business_scenarios (业务场景表) 🔥 核心

- **记录数**: 115 ⬆️ (增长 423%)
- **层级结构**: 3 级业务场景 (15 个 1 级 + 30 个 2 级 + 70 个 3 级)
- **字段**:
  - `id`, `scenario_name`, `category`
  - `parent_scenario_id`, `level`
  - `description`, `keywords`, `business_value`

---

## 🔧 常用查询模式

### 获取对话历史

```sql
SELECT * FROM chat_messages
WHERE session_id = ?
ORDER BY timestamp DESC
LIMIT 10;
```

### 获取会话信息

```sql
SELECT * FROM chat_sessions
WHERE session_uuid = ?;
```

### 查询匹配职位

```sql
SELECT jl.*, c.company_name, c.company_type
FROM job_listings jl
JOIN companies c ON jl.company_id = c.id
WHERE jl.primary_tech_direction_id = ?
AND jl.is_active = true;
```

---

## ⚠️ 重要注意事项

1. **会话管理**:

   - 外部使用 `session_uuid` (UUID 格式)
   - 内部关联使用 `session_id` (整数)

2. **消息存储**:

   - 使用 `session_id` 而不是 `user_id`
   - `metadata_json` 存储结构化数据

3. **字段命名**:

   - 数据库使用下划线命名 (`session_id`)
   - 不要使用驼峰命名 (`sessionId`)

4. **常见错误**:
   - ❌ `chat_messages.user_id` (不存在)
   - ✅ `chat_messages.session_id` (正确)
   - ❌ `TABLES.CHAT_MESSAGES` (可能过时)
   - ✅ `'chat_messages'` (直接使用表名)

---

## 📊 数据统计 (更新于 2025-07-27)

| 表名               | 记录数 | 变化     | 状态 |
| ------------------ | ------ | -------- | ---- |
| users              | 379    | ⬆️ +236  | ✅   |
| chat_sessions      | 566    | ⬆️ +342  | ✅   |
| chat_messages      | 2618   | ⬆️ +1504 | ✅   |
| candidate_profiles | 157    | ⬆️ +78   | ✅   |
| companies          | 79     | ➡️ 稳定  | ✅   |
| job_listings       | 50     | ➡️ 稳定  | ✅   |
| tech_tree          | 648    | ➡️ 稳定  | ✅   |
| business_scenarios | 115    | ⬆️ +93   | ✅   |

## 🆕 新增功能

### 业务场景映射系统

- **job_listings** 表新增 `primary_business_scenario_id` 字段
- **candidate_profiles** 表新增 `primary_business_scenario_id` 和 `candidate_business_scenario_raw` 字段
- **business_scenarios** 表扩展为 115 个场景，支持 3 级层级结构

---

## 🎯 开发建议

1. **优先使用实际字段名**，避免依赖常量文件
2. **会话管理**已修复，可以进行连续对话测试
3. **数据库结构稳定**，可以开始完整功能测试
4. **字段映射**参考上面的结构，避免查询错误

---

_此文件基于实际数据库结构生成，请在开发时优先参考此文档_

---

## 🌳 技术栈映射表 (tech_tree)

### 三级层级结构：

- **Level 1**: 主要技术方向（如：前端开发、后端开发、算法工程师）
- **Level 2**: 技术分类（如：Web 前端、移动端、机器学习）
- **Level 3**: 具体技术（如：React、Vue、PyTorch、TensorFlow）

### 关键字段：

- `id` - 技术 ID
- `tech_name` - 技术名称
- `level` - 层级（1/2/3）
- `parent_id` - 父级技术 ID
- `keywords` - 关键词（用于匹配）

### 常见技术简称映射：

```javascript
const techAliases = {
  RAG: ["检索增强生成", "Retrieval Augmented Generation"],
  CV: ["计算机视觉", "Computer Vision"],
  NLP: ["自然语言处理", "Natural Language Processing"],
  ML: ["机器学习", "Machine Learning"],
  DL: ["深度学习", "Deep Learning"],
};
```

---

## 🏢 公司信息表 (companies)

### 实际数据库公司列表（79 家公司）：

#### 头部大厂：

- **腾讯** (别名: 鹅厂、TX)
- **阿里巴巴** (别名: 阿里、Ali)
- **字节跳动** (别名: 字节、BD)
- **美团** (别名: 美团、MT)
- **蚂蚁金服** (别名: 蚂蚁、支付宝)
- **高德地图** (别名: 高德)
- **钉钉** (别名: 钉钉)

#### 中型公司：

- **百度、京东、快手、小红书、知乎、哔哩哔哩（B 站）、得物、滴滴、饿了么、荣耀**
- **科大讯飞、四维图新、深圳力合微电子、马上消费金融、顺丰科技**
- **SHEIN、Bigo、传音控股、哈啰出行、联影中央研究院、蓝色光标**

#### 外资大厂：

- **微软** (别名: 微软、MS)
- **谷歌** (别名: 谷歌、Google)
- **亚马逊** (别名: 亚麻、Amazon)
- **Meta/Facebook** (别名: Meta、脸书)

#### 创业型公司：

- **优地机器人、九识智能、坎德拉科技、赢彻科技、松灵机器人**
- **北京卓鸷科技、北京唯实具身智能研究院、西部智联科技**
- **易控驾智科技、墨芯人工智能科技、三个逗号**

#### 国企：

- **中兴通讯、徐工集团、宁波银行、重庆长安工业集团**
- **中国科学院空间应用工程与技术中心、沈阳机器人产业发展集团**

---

## 📈 职级对照表 (candidate_standard_levels)

### 实际数据库职级映射（96 条记录）：

#### 阿里系（标准参考）：

- **P4**: 初级工程师 (1-3 年经验, 80-120 万)
- **P5**: 工程师 (2-4 年经验, 100-150 万)
- **P6**: 高级工程师 (4-7 年经验, 150-250 万)
- **P7**: 专家工程师 (6-9 年经验, 200-300 万)
- **P8**: 资深专家 (8-11 年经验, 250-350 万)
- **P9**: 首席专家 (10-13 年经验, 300-400 万)
- **P10**: 技术总监 (12-15 年经验, 350-450 万)

#### 腾讯系：

- **8 级**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **9 级**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **9 级高级**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **10 级**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **11 级**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **12 级**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **13 级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **14 级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **15 级**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 字节系：

- **2-1**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **2-2**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **3-1**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **3-2**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **3-2 高级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **4-1**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **4-2**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 百度系：

- **T4/T5**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **T5 高级/T6**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **T6 高级/T7**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **T7 高级/T8**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **T9/T10**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 美团系：

- **L6/L7**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **L7 高级/L8**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **L9**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **L10**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **L10+**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 外资大厂：

- **微软**: 60 级(P6) → 61-62 级(P7) → 64-65 级(P8) → 65 高级-66 级(P9) → 67 级+(P10)
- **谷歌**: L3-L4(P7) → L5(P8) → L6(P9) → L7+(P10)
- **亚马逊**: L4-L5(P7) → L6+(P8-P10)
- **Meta**: E3-E4(P7) → E4 高级(P8) → E5(P9) → E6+(P10)

---

## 📊 实际数据库查询结果 (2025-07-27)

### 🌳 tech_tree 表实际数据：

- **总记录数**: 648 条技术记录
- **层级分布**: Level 1(35 条), Level 2(约 200 条), Level 3(约 400 条)
- **字段结构**: id, tech_name, level, parent_id 等字段确认
- **数据完整性**: 技术树数据完整，支持父子级关系查询

#### Level 1 主要技术方向（35 个）：

- **算法类**: 推荐算法、广告算法、搜索算法、CV 算法、NLP 算法、多模态算法
- **大模型**: 大模型（LLM）算法、语音算法、视频算法
- **机器学习**: 通用机器学习/深度学习算法、图神经网络/图算法
- **开发类**: 前端开发、后端开发、移动端开发、全栈开发
- **基础设施**: 数据工程、云计算、DevOps、测试工程
- **产品类**: 产品经理、设计师、运营

### 🏢 companies 表实际数据：

- **总记录数**: 约 50+家公司
- **公司类型**: 头部大厂、中型公司、创业型公司等
- **实际公司**: 字节跳动、腾讯、阿里巴巴、百度等主流科技公司
- **字段**: company_name, company_type, industry 等

### 📈 职级数据状态：

- **candidate_standard_levels 表**: 未找到或无权限访问
- **替代方案**: 需要通过其他表或字段获取职级信息
- **建议**: 使用通用职级映射或从 candidate_profiles 表获取

### 🔍 其他关键表：

- **job_listings**: 50 条职位记录
- **candidate_profiles**: 157 条候选人档案
- **business_scenarios**: 115 条业务场景数据

### 💡 开发建议：

1. **技术映射**: 直接查询 tech_tree 表，支持三级层级匹配
2. **公司识别**: 基于 companies 表的真实数据进行匹配
3. **职级处理**: 使用通用职级体系或从档案表提取
4. **数据缓存**: 考虑缓存常用的技术和公司数据以提升性能
