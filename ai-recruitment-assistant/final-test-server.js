// final-test-server.js - 最终测试服务器
// 简单分类 + 硬编码 + 少量API = 完美解决方案

const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const FinalMessageProcessor = require('./core/回复引擎/final-message-processor');

const app = express();
const PORT = 8080;

// 初始化处理器
const processor = new FinalMessageProcessor();

// 中间件
app.use(cors());
app.use(express.json());

// 主页
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI招聘助手 - 最终版本</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
            .header { text-align: center; margin-bottom: 30px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .result { margin: 15px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
            .success { background: #d4edda; border-left-color: #28a745; }
            .error { background: #f8d7da; border-left-color: #dc3545; }
            button { padding: 12px 20px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; }
            button:hover { background: #0056b3; }
            button.secondary { background: #6c757d; }
            button.secondary:hover { background: #545b62; }
            input { padding: 12px; margin: 8px; width: 400px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .stat-card { padding: 15px; background: #e9ecef; border-radius: 8px; text-align: center; }
            .category-tag { display: inline-block; padding: 4px 8px; margin: 2px; background: #007bff; color: white; border-radius: 4px; font-size: 12px; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 AI招聘助手 - 最终版本</h1>
            <p>简单分类 + 硬编码回复 + 少量API调用 = 完美解决方案</p>
        </div>
        
        <div class="test-section">
            <h2>🎯 核心场景测试</h2>
            <p>测试第一句话的分类和回复效果：</p>
            <button onclick="testMessage('你好')">你好</button>
            <button onclick="testMessage('有什么职位')">有什么职位</button>
            <button onclick="testMessage('你是谁')">你是谁</button>
            <button onclick="testMessage('薪资多少')">薪资多少</button>
            <button onclick="testMessage('我是前端工程师')">我是前端工程师</button>
        </div>

        <div class="test-section">
            <h2>🧪 复杂场景测试</h2>
            <p>测试需要API推理的复杂消息：</p>
            <button onclick="testMessage('我是前端工程师，3年经验，想找大厂的职位')">复杂需求1</button>
            <button onclick="testMessage('薪资多少？需要什么技术栈？有什么公司推荐？')">复杂需求2</button>
            <button onclick="testMessage('hello，我想了解一下你们的招聘情况，主要是技术类的职位')">复杂需求3</button>
        </div>

        <div class="test-section">
            <h2>✏️ 自定义测试</h2>
            <input type="text" id="customMessage" placeholder="输入任何消息测试..." />
            <button onclick="testCustomMessage()">发送测试</button>
            <button class="secondary" onclick="clearResults()">清空结果</button>
        </div>

        <div class="test-section">
            <h2>📊 系统状态</h2>
            <button onclick="getStats()">获取统计信息</button>
            <button class="secondary" onclick="testFollowUp()">测试后续对话</button>
        </div>

        <div id="results"></div>

        <script>
            let currentSessionId = null;

            async function testMessage(message) {
                currentSessionId = 'test-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                const userEmail = '<EMAIL>';
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message, sessionId: currentSessionId, userEmail })
                    });
                    
                    const result = await response.json();
                    displayResult(message, result, true);
                } catch (error) {
                    displayError('测试失败: ' + error.message);
                }
            }

            async function testCustomMessage() {
                const message = document.getElementById('customMessage').value;
                if (!message.trim()) {
                    alert('请输入测试消息');
                    return;
                }
                await testMessage(message);
                document.getElementById('customMessage').value = '';
            }

            async function testFollowUp() {
                if (!currentSessionId) {
                    alert('请先发送一条第一句消息');
                    return;
                }
                
                const followUpMessage = '我是前端开发工程师';
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                            message: followUpMessage, 
                            sessionId: currentSessionId, 
                            userEmail: '<EMAIL>' 
                        })
                    });
                    
                    const result = await response.json();
                    displayResult(followUpMessage, result, false);
                } catch (error) {
                    displayError('后续对话测试失败: ' + error.message);
                }
            }

            async function getStats() {
                try {
                    const response = await fetch('/api/stats');
                    const stats = await response.json();
                    displayStats(stats);
                } catch (error) {
                    displayError('获取统计失败: ' + error.message);
                }
            }

            function displayResult(message, result, isFirst) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
                
                const classification = result.debug?.classification;
                const isFirstMsg = result.debug?.isFirstMessage;
                
                resultDiv.innerHTML = \`
                    <h3>\${isFirst ? '🎯' : '🔄'} \${isFirstMsg ? '第一句' : '后续'}: "\${message}"</h3>
                    <div class="stats">
                        <div class="stat-card">
                            <strong>回复类型</strong><br>
                            <span class="category-tag">\${result.response?.type || 'N/A'}</span>
                        </div>
                        \${classification ? \`
                        <div class="stat-card">
                            <strong>分类结果</strong><br>
                            <span class="category-tag">\${classification.category}</span>
                        </div>
                        <div class="stat-card">
                            <strong>处理方式</strong><br>
                            <span class="category-tag">\${classification.method}</span>
                        </div>
                        <div class="stat-card">
                            <strong>置信度</strong><br>
                            <span class="category-tag">\${(classification.confidence * 100).toFixed(0)}%</span>
                        </div>
                        \` : ''}
                    </div>
                    <p><strong>💬 回复内容:</strong></p>
                    <p style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">\${result.response?.content || 'N/A'}</p>
                    \${result.response?.suggestions ? '<p><strong>🎯 建议选项:</strong> ' + result.response.suggestions.map(s => '<span class="category-tag">' + s + '</span>').join(' ') + '</p>' : ''}
                    <details style="margin-top: 10px;">
                        <summary>📋 完整响应数据</summary>
                        <pre>\${JSON.stringify(result, null, 2)}</pre>
                    </details>
                \`;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            function displayStats(stats) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                
                resultDiv.innerHTML = \`
                    <h3>📊 系统统计信息</h3>
                    <div class="stats">
                        <div class="stat-card">
                            <strong>总会话数</strong><br>
                            <span style="font-size: 24px; color: #007bff;">\${stats.totalSessions}</span>
                        </div>
                        <div class="stat-card">
                            <strong>活跃会话</strong><br>
                            <span style="font-size: 24px; color: #28a745;">\${stats.activeSessions}</span>
                        </div>
                        <div class="stat-card">
                            <strong>分类规则</strong><br>
                            <span style="font-size: 24px; color: #17a2b8;">\${stats.classifier.totalRules}</span>
                        </div>
                        <div class="stat-card">
                            <strong>关键词数</strong><br>
                            <span style="font-size: 24px; color: #ffc107;">\${stats.classifier.totalKeywords}</span>
                        </div>
                    </div>
                    <p><strong>🏷️ 分类类别:</strong></p>
                    <p>\${stats.classifier.categories.map(c => '<span class="category-tag">' + c + '</span>').join(' ')}</p>
                    <p><strong>🤖 API支持:</strong> \${stats.classifier.hasAPIFallback ? '✅ 已启用' : '❌ 未启用'}</p>
                    <details>
                        <summary>📋 完整统计数据</summary>
                        <pre>\${JSON.stringify(stats, null, 2)}</pre>
                    </details>
                \`;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            function displayError(message) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = \`<h3>❌ 错误</h3><p>\${message}</p>\`;
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            function clearResults() {
                document.getElementById('results').innerHTML = '';
            }

            // 回车键发送
            document.getElementById('customMessage').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    testCustomMessage();
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 聊天接口
app.post('/api/chat', async (req, res) => {
  try {
    const { message, sessionId, userEmail } = req.body;
    
    if (!message || !message.trim()) {
      return res.status(400).json({
        success: false,
        error: '消息不能为空'
      });
    }

    const finalSessionId = sessionId || uuidv4();
    const finalUserEmail = userEmail || '<EMAIL>';

    console.log(`\n📨 收到请求: "${message}" | 会话: ${finalSessionId}`);

    const result = await processor.processMessage({
      message: message.trim(),
      sessionId: finalSessionId,
      userEmail: finalUserEmail
    });

    console.log(`✅ 处理完成: ${result.response?.type}`);
    res.json(result);

  } catch (error) {
    console.error('❌ 接口错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器错误',
      message: error.message
    });
  }
});

// 统计接口
app.get('/api/stats', (req, res) => {
  try {
    const stats = processor.getStats();
    res.json(stats);
  } catch (error) {
    console.error('❌ 统计错误:', error);
    res.status(500).json({ success: false, error: '获取统计失败' });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`\n🚀 AI招聘助手最终版本启动成功！`);
  console.log(`📱 测试地址: http://localhost:${PORT}`);
  console.log(`💬 API接口: http://localhost:${PORT}/api/chat`);
  console.log(`📊 统计接口: http://localhost:${PORT}/api/stats`);
  console.log(`\n🎯 功能特点:`);
  console.log(`✅ 简单分类 - 5大类别，快速匹配`);
  console.log(`✅ 硬编码回复 - 准确可靠，响应迅速`);
  console.log(`✅ API推理 - 处理复杂消息，智能补充`);
  console.log(`✅ 会话管理 - 支持多轮对话`);
  console.log(`\n🧪 测试说明:`);
  console.log(`1. 打开 http://localhost:${PORT} 进行可视化测试`);
  console.log(`2. 测试各种第一句话场景`);
  console.log(`3. 验证分类准确性和回复质量`);
  console.log(`4. 检查复杂消息的API推理效果`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

module.exports = app;
