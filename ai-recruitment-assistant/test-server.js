/**
 * AI招聘助手测试服务器
 * 用于测试第一句回复处理功能
 */

require("dotenv").config({ path: "../.env.local" });
const express = require("express");
const cors = require("cors");
const path = require("path");

// 导入核心模块
const MessageProcessor = require("./core/系统核心/message-processor");
const DatabaseManager = require("./core/数据管理/database-manager");

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, "test-frontend")));

// 全局变量
let messageProcessor = null;
let databaseManager = null;

// 初始化系统
async function initializeSystem() {
  try {
    console.log("🚀 正在初始化AI招聘助手测试服务器...");

    // 创建简化的配置对象
    const config = {
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
      deepseekApiKey: process.env.DEEPSEEK_API_KEY,
      deepseekEndpoint:
        process.env.LLM_API_ENDPOINT || "https://api.deepseek.com/v1",
      maxTokens: 2000,
      temperature: 0.7,
      timeout: 30000,
      // 添加MessageProcessor需要的方法
      getAIConfig: function () {
        return {
          deepseekApiKey: this.deepseekApiKey,
          deepseekEndpoint: this.deepseekEndpoint,
          maxTokens: this.maxTokens,
          temperature: this.temperature,
          timeout: this.timeout,
        };
      },
      getDatabaseConfig: function () {
        return {
          url: this.supabaseUrl,
          key: this.supabaseKey,
        };
      },
      getBusinessConfig: function () {
        return {
          maxRecommendations: 10,
          sessionTimeoutDays: 14,
          cacheTimeout: 300000,
        };
      },
    };

    // 初始化数据库管理器
    databaseManager = new DatabaseManager(config);
    await databaseManager.connect();

    // 初始化消息处理器
    messageProcessor = new MessageProcessor({
      database: databaseManager,
      userManager: null, // 暂时不需要用户管理器
      config: config,
    });
    await messageProcessor.initialize();

    console.log("✅ 系统初始化完成");
  } catch (error) {
    console.error("❌ 系统初始化失败:", error);
    process.exit(1);
  }
}

// API路由

// 健康检查
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    message: "AI招聘助手测试服务器运行正常",
  });
});

// 聊天接口
app.post("/api/chat", async (req, res) => {
  try {
    console.log("\n📨 收到聊天请求:", req.body);

    const { message, sessionId } = req.body;

    if (!message || typeof message !== "string") {
      return res.status(400).json({
        success: false,
        error: "消息内容不能为空",
      });
    }

    // 构造消息数据
    const messageData = {
      message: message.trim(),
      sessionId: sessionId || null,
      timestamp: new Date().toISOString(),
      source: "test_frontend",
    };

    console.log("🔄 开始处理消息...");

    // 处理消息
    const result = await messageProcessor.processMessage(messageData);

    console.log("✅ 消息处理完成:", {
      success: result.success,
      intent: result.intent,
      responseType: result.response?.type,
      sessionId: result.sessionId,
    });

    res.json(result);
  } catch (error) {
    console.error("❌ 处理聊天请求失败:", error);

    res.status(500).json({
      success: false,
      error: "服务器内部错误",
      details: error.message,
    });
  }
});

// 获取会话信息
app.get("/api/session/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    // 这里可以添加获取会话信息的逻辑
    res.json({
      success: true,
      sessionId: sessionId,
      status: "active",
    });
  } catch (error) {
    console.error("❌ 获取会话信息失败:", error);
    res.status(500).json({
      success: false,
      error: "获取会话信息失败",
    });
  }
});

// 静态文件服务 - 测试页面
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "test-frontend", "index.html"));
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error("❌ 服务器错误:", error);
  res.status(500).json({
    success: false,
    error: "服务器内部错误",
    details: error.message,
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: "接口不存在",
  });
});

// 启动服务器
async function startServer() {
  try {
    // 先初始化系统
    await initializeSystem();

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`\n🌐 测试服务器已启动:`);
      console.log(`   - 服务器地址: http://localhost:${PORT}`);
      console.log(`   - 测试页面: http://localhost:${PORT}`);
      console.log(`   - API接口: http://localhost:${PORT}/api/chat`);
      console.log(`   - 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`\n💡 现在可以打开浏览器访问测试页面进行测试！`);
    });
  } catch (error) {
    console.error("❌ 启动服务器失败:", error);
    process.exit(1);
  }
}

// 优雅关闭
process.on("SIGINT", async () => {
  console.log("\n🛑 正在关闭服务器...");

  try {
    if (databaseManager) {
      await databaseManager.close();
    }
    console.log("✅ 服务器已安全关闭");
    process.exit(0);
  } catch (error) {
    console.error("❌ 关闭服务器时出错:", error);
    process.exit(1);
  }
});

// 启动服务器
startServer();
