/**
 * 详细数据库查询脚本（只读操作）
 * 绝对不修改任何数据库内容
 */

require("dotenv").config({ path: "../.env.local" });
const { createClient } = require("@supabase/supabase-js");

async function detailedDatabaseQuery() {
  console.log("🔍 开始详细查询数据库数据...\n");

  // 创建Supabase客户端
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // 1. 详细查询公司数据
    console.log("🏢 详细查询公司数据 (companies):");
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('*')
      .order('company_name');
      
    if (companyError) {
      console.error('❌ 读取公司数据失败:', companyError);
    } else if (companies && companies.length > 0) {
      console.log(`  总计: ${companies.length} 家公司`);
      console.log(`  公司列表:`);
      companies.forEach((company, index) => {
        console.log(`    ${index + 1}. ${company.company_name} (类型: ${company.company_type || 'N/A'}, 行业: ${company.industry || 'N/A'})`);
      });
    } else {
      console.log('  📝 companies 表为空');
    }

    // 2. 详细查询技术树数据
    console.log("\n🌳 详细查询技术树数据 (tech_tree):");
    const { data: techTree, error: techError } = await supabase
      .from('tech_tree')
      .select('*')
      .order('level', { ascending: true })
      .order('id', { ascending: true });
      
    if (techError) {
      console.error('❌ 读取技术树失败:', techError);
    } else if (techTree && techTree.length > 0) {
      console.log(`  总计: ${techTree.length} 条技术记录`);
      
      // 按层级分组显示
      const levels = {};
      techTree.forEach(tech => {
        const level = tech.level || 'unknown';
        if (!levels[level]) levels[level] = [];
        levels[level].push(tech);
      });
      
      Object.keys(levels).sort().forEach(level => {
        console.log(`\n  Level ${level} (${levels[level].length}条):`);
        levels[level].slice(0, 15).forEach(tech => {
          const name = tech.tech_name || tech.name || 'N/A';
          const parentId = tech.parent_id ? `parent:${tech.parent_id}` : 'root';
          console.log(`    - ID:${tech.id} ${name} (${parentId})`);
        });
        if (levels[level].length > 15) {
          console.log(`    ... 还有 ${levels[level].length - 15} 条记录`);
        }
      });
    } else {
      console.log('  📝 tech_tree 表为空');
    }

    // 3. 尝试查询职级相关数据
    console.log("\n📈 详细查询职级数据:");
    const possibleLevelTables = [
      'candidate_standard_levels', 
      'standard_levels', 
      'levels',
      'job_levels',
      'candidate_levels',
      'level_mappings'
    ];
    
    let foundLevelData = false;
    for (const tableName of possibleLevelTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .order('id');
          
        if (!error && data && data.length > 0) {
          console.log(`  ✅ 找到职级表: ${tableName}`);
          console.log(`  总计: ${data.length} 条记录`);
          data.forEach((level, index) => {
            const fields = Object.keys(level).map(key => `${key}: ${level[key]}`).join(', ');
            console.log(`    ${index + 1}. ${fields}`);
          });
          foundLevelData = true;
          break;
        }
      } catch (e) {
        // 表不存在，继续尝试下一个
      }
    }
    
    if (!foundLevelData) {
      console.log('  ❌ 未找到职级相关表');
      
      // 尝试从candidate_profiles表中查看职级相关字段
      console.log('\n  🔍 从candidate_profiles表查看职级相关字段:');
      try {
        const { data: profiles, error: profileError } = await supabase
          .from('candidate_profiles')
          .select('*')
          .limit(5);
          
        if (!profileError && profiles && profiles.length > 0) {
          console.log(`  样例数据字段:`);
          const sampleProfile = profiles[0];
          Object.keys(sampleProfile).forEach(key => {
            if (key.toLowerCase().includes('level') || key.toLowerCase().includes('职级')) {
              console.log(`    - ${key}: ${sampleProfile[key]}`);
            }
          });
        }
      } catch (e) {
        console.log('  ❌ 无法访问candidate_profiles表');
      }
    }

    // 4. 查询其他相关表的详细信息
    console.log("\n🔍 查询其他相关表:");
    const otherTables = [
      'job_listings',
      'candidate_profiles', 
      'business_scenarios',
      'chat_sessions',
      'chat_messages'
    ];
    
    for (const tableName of otherTables) {
      try {
        const { count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });
        console.log(`  ✅ ${tableName} - ${count || 0} 条记录`);
        
        // 获取表结构样例
        if (count > 0) {
          const { data: sample } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          if (sample && sample.length > 0) {
            const fields = Object.keys(sample[0]).join(', ');
            console.log(`    字段: ${fields}`);
          }
        }
      } catch (e) {
        console.log(`  ❌ ${tableName} - 不存在或无权限`);
      }
    }

    console.log('\n✅ 详细数据库查询完成!');

  } catch (error) {
    console.error('❌ 数据库查询失败:', error);
  }
}

// 运行查询
detailedDatabaseQuery();
