// test-final-system.js - 最终系统测试
// 验证所有功能是否正常工作

const FinalMessageProcessor = require('./core/回复引擎/final-message-processor');

async function testFinalSystem() {
  console.log('🚀 最终系统测试开始\n');
  
  const processor = new FinalMessageProcessor();
  
  // 测试用例
  const testCases = [
    {
      name: "简单问候",
      message: "你好",
      expectedCategory: "GREETING_SIMPLE",
      expectedType: "first_greeting_simple",
      expectedContent: "您有兴趣看看新机会吗"
    },
    {
      name: "职位询问",
      message: "有什么职位",
      expectedCategory: "JOB_INQUIRY", 
      expectedType: "first_job_inquiry",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "身份确认",
      message: "你是谁",
      expectedCategory: "IDENTITY_CHECK",
      expectedType: "first_greeting_full",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "技术询问",
      message: "需要什么技术栈",
      expectedCategory: "TECH_INQUIRY",
      expectedType: "first_tech_inquiry", 
      expectedContent: "我们有各种技术栈的职位"
    },
    {
      name: "薪资询问",
      message: "薪资多少",
      expectedCategory: "SALARY_INQUIRY",
      expectedType: "first_salary_inquiry",
      expectedContent: "薪资范围根据技术栈"
    },
    {
      name: "复杂消息(需要API)",
      message: "我是前端工程师，3年经验，想找大厂的职位，薪资期望25k",
      expectedCategory: "NEEDS_API",
      expectedType: "first_job_inquiry_api",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "未知消息",
      message: "随便说点什么",
      expectedCategory: "DEFAULT",
      expectedType: "first_greeting_default",
      expectedContent: "我们合作的公司挺多的"
    }
  ];
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  console.log(`📋 开始测试 ${totalTests} 个场景...\n`);
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`${i + 1}. 测试: ${testCase.name}`);
    console.log(`   输入: "${testCase.message}"`);
    
    try {
      const result = await processor.processMessage({
        message: testCase.message,
        sessionId: `test-${i}-${Date.now()}`,
        userEmail: '<EMAIL>'
      });
      
      if (!result.success) {
        console.log(`   ❌ 处理失败: ${result.error}`);
        continue;
      }
      
      const classification = result.debug?.classification;
      const response = result.response;
      
      // 检查分类
      const categoryMatch = classification?.category === testCase.expectedCategory;
      // 检查回复类型
      const typeMatch = response?.type === testCase.expectedType;
      // 检查回复内容
      const contentMatch = response?.content?.includes(testCase.expectedContent);
      
      if (categoryMatch && typeMatch && contentMatch) {
        console.log(`   ✅ 通过`);
        console.log(`   📋 分类: ${classification.category} (${classification.method})`);
        console.log(`   📤 类型: ${response.type}`);
        console.log(`   💬 内容: ${response.content.substring(0, 50)}...`);
        passedTests++;
      } else {
        console.log(`   ❌ 失败`);
        if (!categoryMatch) {
          console.log(`   期望分类: ${testCase.expectedCategory}, 实际: ${classification?.category}`);
        }
        if (!typeMatch) {
          console.log(`   期望类型: ${testCase.expectedType}, 实际: ${response?.type}`);
        }
        if (!contentMatch) {
          console.log(`   期望内容包含: "${testCase.expectedContent}"`);
          console.log(`   实际内容: ${response?.content?.substring(0, 100)}...`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 异常: ${error.message}`);
    }
    
    console.log('');
  }
  
  // 测试后续对话
  console.log('🔄 测试后续对话功能...\n');
  
  try {
    const sessionId = 'follow-up-test-' + Date.now();
    
    // 第一句
    const firstResult = await processor.processMessage({
      message: '你好',
      sessionId: sessionId,
      userEmail: '<EMAIL>'
    });
    
    if (firstResult.success) {
      console.log('✅ 第一句处理成功');
      
      // 后续消息
      const followUpResult = await processor.processMessage({
        message: '我是前端开发工程师',
        sessionId: sessionId,
        userEmail: '<EMAIL>'
      });
      
      if (followUpResult.success && !followUpResult.debug.isFirstMessage) {
        console.log('✅ 后续对话处理成功');
        console.log(`   回复类型: ${followUpResult.response.type}`);
        passedTests += 0.5; // 额外加分
      } else {
        console.log('❌ 后续对话处理失败');
      }
    }
  } catch (error) {
    console.log(`❌ 后续对话测试异常: ${error.message}`);
  }
  
  console.log('\n📊 测试结果统计:');
  console.log(`   通过: ${passedTests}/${totalTests + 0.5}`);
  console.log(`   成功率: ${(passedTests/(totalTests + 0.5)*100).toFixed(1)}%`);
  
  if (passedTests >= totalTests) {
    console.log('🎉 所有核心测试通过！系统工作正常。');
  } else {
    console.log('⚠️ 部分测试失败，需要检查配置。');
  }
  
  // 显示系统统计
  console.log('\n📈 系统统计:');
  const stats = processor.getStats();
  console.log(`   总会话数: ${stats.totalSessions}`);
  console.log(`   活跃会话: ${stats.activeSessions}`);
  console.log(`   分类规则: ${stats.classifier.totalRules}`);
  console.log(`   关键词数: ${stats.classifier.totalKeywords}`);
  console.log(`   API支持: ${stats.classifier.hasAPIFallback ? '✅' : '❌'}`);
  console.log(`   分类类别: ${stats.classifier.categories.join(', ')}`);
  
  return passedTests >= totalTests;
}

// 运行测试
if (require.main === module) {
  testFinalSystem().then(success => {
    console.log(`\n🏁 测试完成，结果: ${success ? '成功' : '失败'}`);
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ 测试异常:', error);
    process.exit(1);
  });
}

module.exports = testFinalSystem;
