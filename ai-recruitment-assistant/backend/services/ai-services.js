/**
 * AI招聘助手系统 - AI服务模块
 * 
 * 核心职责：
 * - DeepSeek API 集成
 * - 智能分析和生成
 * - 上下文理解
 * - 信息提取
 * 
 * 预计代码量：1600行
 */

const axios = require('axios');

class AIServices {
  constructor(config) {
    this.config = config;
    this.client = null;
    this.isInitialized = false;

    // API配置
    this.apiEndpoint = config.deepseekEndpoint;
    this.apiKey = config.deepseekApiKey;
    this.maxTokens = config.maxTokens;
    this.temperature = config.temperature;
    this.timeout = config.timeout;

    // 高并发优化配置
    this.requestQueue = [];
    this.processing = false;
    this.maxRetries = 3;
    this.rateLimitDelay = 1000;
    this.maxConcurrentRequests = 5;
    this.currentRequests = 0;

    // 请求统计
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0
    };
  }

  /**
   * 初始化AI服务
   */
  async initialize() {
    try {
      // 配置HTTP客户端
      this.client = axios.create({
        baseURL: this.apiEndpoint,
        timeout: this.timeout,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      // 测试连接
      await this.testConnection();
      
      this.isInitialized = true;
      console.log('🤖 AI服务初始化完成');
      
    } catch (error) {
      console.error('❌ AI服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'user', content: 'Hello' }
        ],
        max_tokens: 10
      });
      
      if (response.status === 200) {
        console.log('✅ AI服务连接测试成功');
      }
      
    } catch (error) {
      console.error('❌ AI服务连接测试失败:', error);
      throw error;
    }
  }

  /**
   * 分析用户意图（带队列处理）
   */
  async analyzeUserIntent(message, context = {}) {
    return this.queueRequest(async () => {
      const prompt = this.buildIntentAnalysisPrompt(message, context);

      const response = await this.makeAPIRequest('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: this.getIntentAnalysisSystemPrompt() },
          { role: 'user', content: prompt }
        ],
        max_tokens: 500,
        temperature: 0.3
      });

      const result = response.data.choices[0].message.content;
      return this.parseIntentAnalysisResult(result);
    }, {
      fallback: {
        type: 'unknown',
        confidence: 0.1,
        entities: {}
      }
    });
  }

  /**
   * 请求队列处理
   */
  async queueRequest(requestFn, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        requestFn,
        resolve,
        reject,
        options,
        retries: 0,
        timestamp: Date.now()
      });

      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) {
      return;
    }

    if (this.currentRequests >= this.maxConcurrentRequests) {
      return;
    }

    this.processing = true;
    const request = this.requestQueue.shift();
    this.currentRequests++;

    try {
      const startTime = Date.now();
      const result = await request.requestFn();

      // 更新统计信息
      this.updateStats(true, Date.now() - startTime);

      request.resolve(result);

    } catch (error) {
      console.error('❌ AI请求失败:', error);

      // 检查是否需要重试
      if (request.retries < this.maxRetries && this.shouldRetry(error)) {
        request.retries++;
        this.requestQueue.unshift(request); // 重新加入队列头部

        // 如果是限流错误，延迟处理
        if (this.isRateLimitError(error)) {
          this.stats.rateLimitHits++;
          await this.delay(this.rateLimitDelay);
        }
      } else {
        // 重试次数用完或不可重试错误，返回降级结果
        this.updateStats(false, 0);

        if (request.options.fallback) {
          request.resolve(request.options.fallback);
        } else {
          request.reject(error);
        }
      }
    } finally {
      this.currentRequests--;
      this.processing = false;

      // 继续处理队列
      setTimeout(() => this.processQueue(), 10);
    }
  }

  /**
   * 发起API请求（带重试和限流处理）
   */
  async makeAPIRequest(endpoint, data) {
    try {
      const response = await this.client.post(endpoint, data);
      return response;

    } catch (error) {
      // 处理特定错误类型
      if (error.response?.status === 429) {
        // 限流错误
        throw new Error('RATE_LIMIT');
      } else if (error.response?.status >= 500) {
        // 服务器错误，可重试
        throw new Error('SERVER_ERROR');
      } else {
        // 客户端错误，不重试
        throw error;
      }
    }
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = ['RATE_LIMIT', 'SERVER_ERROR', 'TIMEOUT'];
    return retryableErrors.includes(error.message) ||
           error.code === 'ECONNRESET' ||
           error.code === 'ETIMEDOUT';
  }

  /**
   * 判断是否是限流错误
   */
  isRateLimitError(error) {
    return error.message === 'RATE_LIMIT' ||
           error.response?.status === 429;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新统计信息
   */
  updateStats(success, responseTime) {
    this.stats.totalRequests++;

    if (success) {
      this.stats.successfulRequests++;

      // 计算平均响应时间
      const totalTime = this.stats.averageResponseTime * (this.stats.successfulRequests - 1) + responseTime;
      this.stats.averageResponseTime = totalTime / this.stats.successfulRequests;
    } else {
      this.stats.failedRequests++;
    }
  }

  /**
   * 构建意图分析提示词
   */
  buildIntentAnalysisPrompt(message, context) {
    let prompt = `用户消息: "${message}"\n\n`;
    
    if (context.sessionContext) {
      prompt += `会话上下文: ${JSON.stringify(context.sessionContext)}\n\n`;
    }
    
    if (context.messageHistory && context.messageHistory.length > 0) {
      prompt += `最近对话历史:\n`;
      context.messageHistory.forEach((msg, index) => {
        prompt += `${msg.message_type}: ${msg.message_content}\n`;
      });
      prompt += '\n';
    }
    
    prompt += `请分析用户的意图类型、置信度和相关实体信息。`;
    
    return prompt;
  }

  /**
   * 获取意图分析系统提示词
   */
  getIntentAnalysisSystemPrompt() {
    return `你是一个专业的招聘助手意图分析器。请分析用户消息的意图类型。

可能的意图类型包括：
- greeting: 问候语
- profile_update: 更新个人档案
- job_search: 搜索职位
- recommendation_request: 请求推荐
- tech_direction_inquiry: 技术方向询问
- salary_inquiry: 薪资询问
- company_inquiry: 公司询问
- location_inquiry: 地点询问
- experience_inquiry: 经验询问
- resume_upload: 简历上传
- unknown: 未知意图

请以JSON格式返回分析结果：
{
  "type": "意图类型",
  "confidence": 0.0-1.0的置信度,
  "entities": {
    "技术栈": ["提取的技术"],
    "经验年限": "提取的年限",
    "期望薪资": "提取的薪资",
    "工作地点": "提取的地点"
  },
  "context": {
    "需要澄清": true/false,
    "澄清问题": "需要澄清的具体问题"
  }
}`;
  }

  /**
   * 解析意图分析结果
   */
  parseIntentAnalysisResult(result) {
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(result);
      
      return {
        type: parsed.type || 'unknown',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || {},
        context: parsed.context || {}
      };
      
    } catch (error) {
      console.error('❌ 解析意图分析结果失败:', error);
      
      // 回退到简单解析
      return {
        type: 'unknown',
        confidence: 0.3,
        entities: {}
      };
    }
  }

  /**
   * 生成对话回复（带队列处理）
   */
  async generateResponse(prompt, context = {}) {
    return this.queueRequest(async () => {
      const messages = [
        { role: 'system', content: this.getConversationSystemPrompt() },
        { role: 'user', content: prompt }
      ];

      // 添加上下文消息
      if (context.messageHistory) {
        const historyMessages = context.messageHistory.slice(-5).map(msg => ({
          role: msg.message_type === 'user' ? 'user' : 'assistant',
          content: msg.message_content
        }));

        messages.splice(1, 0, ...historyMessages);
      }

      const response = await this.makeAPIRequest('/chat/completions', {
        model: 'deepseek-chat',
        messages: messages,
        max_tokens: this.maxTokens,
        temperature: this.temperature
      });

      return response.data.choices[0].message.content;
    }, {
      fallback: '抱歉，我现在无法生成回复。请稍后再试。'
    });
  }

  /**
   * 获取对话系统提示词
   */
  getConversationSystemPrompt() {
    return `你是Katrina，一个专业的AI招聘助手。你的任务是帮助求职者找到合适的工作机会。

你的特点：
- 专业、友好、耐心
- 了解技术行业和招聘市场
- 能够提供个性化的职业建议
- 善于引导用户完善个人档案

你的能力：
- 分析用户的技能和经验
- 推荐合适的职位
- 提供薪资行情信息
- 解答职业发展问题
- 帮助优化简历

请始终保持专业和友好的语调，提供有价值的建议。`;
  }

  /**
   * 提取个人档案信息
   */
  async extractProfileInfo(message, existingProfile = {}) {
    try {
      const prompt = this.buildProfileExtractionPrompt(message, existingProfile);
      
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: this.getProfileExtractionSystemPrompt() },
          { role: 'user', content: prompt }
        ],
        max_tokens: 800,
        temperature: 0.2
      });
      
      const result = response.data.choices[0].message.content;
      return this.parseProfileExtractionResult(result);
      
    } catch (error) {
      console.error('❌ 提取档案信息失败:', error);
      return {};
    }
  }

  /**
   * 构建档案提取提示词
   */
  buildProfileExtractionPrompt(message, existingProfile) {
    let prompt = `用户消息: "${message}"\n\n`;
    
    if (Object.keys(existingProfile).length > 0) {
      prompt += `现有档案信息: ${JSON.stringify(existingProfile, null, 2)}\n\n`;
    }
    
    prompt += `请从用户消息中提取或更新个人档案信息。`;
    
    return prompt;
  }

  /**
   * 获取档案提取系统提示词
   */
  getProfileExtractionSystemPrompt() {
    return `你是一个专业的个人档案信息提取器。请从用户消息中提取相关的职业信息。

需要提取的信息包括：
- 技术方向/职位类型
- 工作经验年限
- 技能栈
- 期望薪资
- 工作地点偏好
- 当前公司
- 教育背景
- 其他相关信息

请以JSON格式返回提取结果：
{
  "techDirection": "技术方向",
  "experienceYears": "经验年限",
  "skills": ["技能1", "技能2"],
  "expectedSalary": {
    "min": 最低薪资,
    "max": 最高薪资
  },
  "location": "期望工作地点",
  "currentCompany": "当前公司",
  "education": "教育背景",
  "confidence": 0.0-1.0的提取置信度
}

如果某些信息无法从消息中提取，请设置为null。`;
  }

  /**
   * 解析档案提取结果
   */
  parseProfileExtractionResult(result) {
    try {
      return JSON.parse(result);
    } catch (error) {
      console.error('❌ 解析档案提取结果失败:', error);
      return {};
    }
  }

  /**
   * 检查服务状态
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      initialized: this.isInitialized,
      endpoint: this.apiEndpoint,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      queue: {
        pending: this.requestQueue.length,
        processing: this.currentRequests,
        maxConcurrent: this.maxConcurrentRequests
      },
      performance: {
        ...this.stats,
        successRate: this.stats.totalRequests > 0 ?
          (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
      }
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0
    };
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      pending: this.requestQueue.length,
      processing: this.currentRequests,
      maxConcurrent: this.maxConcurrentRequests,
      isHealthy: this.requestQueue.length < 100 && this.currentRequests < this.maxConcurrentRequests
    };
  }
}

module.exports = AIServices;
