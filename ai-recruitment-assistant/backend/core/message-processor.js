/**
 * AI招聘助手系统 - 消息处理器（主路由）
 * 
 * 核心职责：
 * - 消息路由和意图识别
 * - 业务流程编排
 * - 上下文管理
 * - 错误处理和回退机制
 * 
 * 预计代码量：2000行
 */

const { v4: uuidv4 } = require('uuid');

// 导入业务模块
const AIServices = require('./ai-services');
const PassiveRecommender = require('./passive-recommender');
const ActiveRecommender = require('./active-recommender');
const TechMapper = require('./tech-mapper');
const Utilities = require('./utilities');
const Validators = require('./validators');

class MessageProcessor {
  constructor({ database, userManager, config }) {
    this.database = database;
    this.userManager = userManager;
    this.config = config;
    
    // 业务模块
    this.aiServices = null;
    this.passiveRecommender = null;
    this.activeRecommender = null;
    this.techMapper = null;
    
    // 工具模块
    this.utilities = new Utilities();
    this.validators = new Validators();
    
    // 意图类型定义
    this.intentTypes = {
      GREETING: 'greeting',
      PROFILE_UPDATE: 'profile_update',
      JOB_SEARCH: 'job_search',
      RECOMMENDATION_REQUEST: 'recommendation_request',
      TECH_DIRECTION_INQUIRY: 'tech_direction_inquiry',
      SALARY_INQUIRY: 'salary_inquiry',
      COMPANY_INQUIRY: 'company_inquiry',
      LOCATION_INQUIRY: 'location_inquiry',
      EXPERIENCE_INQUIRY: 'experience_inquiry',
      RESUME_UPLOAD: 'resume_upload',
      UNKNOWN: 'unknown'
    };
    
    // 上下文状态
    this.contextStates = {
      INITIAL: 'initial',
      PROFILE_BUILDING: 'profile_building',
      JOB_SEARCHING: 'job_searching',
      RECOMMENDATION_VIEWING: 'recommendation_viewing',
      CLARIFICATION_NEEDED: 'clarification_needed'
    };
  }

  /**
   * 初始化消息处理器
   */
  async initialize() {
    try {
      // 初始化AI服务
      this.aiServices = new AIServices(this.config.getAIConfig());
      await this.aiServices.initialize();
      
      // 初始化推荐引擎
      this.passiveRecommender = new PassiveRecommender(this.database, this.config);
      await this.passiveRecommender.initialize();
      
      this.activeRecommender = new ActiveRecommender(this.database, this.config);
      await this.activeRecommender.initialize();
      
      // 初始化技术映射器
      this.techMapper = new TechMapper(this.database, this.config);
      await this.techMapper.initialize();
      
      console.log('🧠 消息处理器初始化完成');
      
    } catch (error) {
      console.error('❌ 消息处理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理用户消息 - 主入口
   */
  async processMessage(messageData) {
    try {
      // 1. 验证输入数据
      const validatedData = this.validators.validateMessageInput(messageData);
      
      // 2. 获取或创建会话
      const session = await this.getOrCreateSession(validatedData);
      
      // 3. 保存用户消息
      await this.saveUserMessage(session.id, validatedData.message);
      
      // 4. 分析用户意图
      const intent = await this.analyzeUserIntent(validatedData.message, session);
      
      // 5. 根据意图路由到相应处理器
      const response = await this.routeToHandler(intent, validatedData, session);
      
      // 6. 保存助手回复
      await this.saveAssistantMessage(session.id, response);
      
      // 7. 更新会话状态
      await this.updateSessionContext(session.id, intent, response);
      
      return {
        success: true,
        sessionId: session.session_uuid,
        response: response,
        intent: intent.type,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 消息处理失败:', error);
      
      // 返回错误回退响应
      return {
        success: false,
        error: error.message,
        response: this.getErrorFallbackResponse(),
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取或创建会话
   */
  async getOrCreateSession(messageData) {
    try {
      let session = null;
      
      // 如果提供了sessionId，尝试获取现有会话
      if (messageData.sessionId) {
        session = await this.database.getSessionByUuid(messageData.sessionId);
      }
      
      // 如果没有找到会话，创建新会话
      if (!session) {
        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(messageData.userEmail);
        
        // 创建新会话
        session = await this.database.createChatSession({
          userId: user.id,
          sessionUuid: messageData.sessionId || uuidv4(),
          entrySourceUrl: messageData.entrySource || '',
          initialIntent: '',
          context: {
            state: this.contextStates.INITIAL,
            lastIntent: null,
            profileCompleteness: 0
          }
        });
      }
      
      return session;
      
    } catch (error) {
      console.error('❌ 获取或创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 分析用户意图
   */
  async analyzeUserIntent(message, session) {
    try {
      // 使用AI服务分析意图
      const aiAnalysis = await this.aiServices.analyzeUserIntent(message, {
        sessionContext: session.current_interaction_context,
        messageHistory: await this.getRecentMessages(session.id, 5)
      });
      
      // 结合规则引擎进行意图识别
      const ruleBasedIntent = this.identifyIntentByRules(message);
      
      // 合并分析结果
      const finalIntent = this.mergeIntentAnalysis(aiAnalysis, ruleBasedIntent);
      
      return {
        type: finalIntent.type,
        confidence: finalIntent.confidence,
        entities: finalIntent.entities || {},
        context: finalIntent.context || {}
      };
      
    } catch (error) {
      console.error('❌ 意图分析失败:', error);
      
      // 回退到规则引擎
      return this.identifyIntentByRules(message);
    }
  }

  /**
   * 基于规则的意图识别
   */
  identifyIntentByRules(message) {
    const lowerMessage = message.toLowerCase();
    
    // 问候语识别
    if (this.utilities.containsAny(lowerMessage, ['你好', 'hello', '嗨', '开始'])) {
      return {
        type: this.intentTypes.GREETING,
        confidence: 0.9,
        entities: {}
      };
    }
    
    // 职位搜索识别
    if (this.utilities.containsAny(lowerMessage, ['找工作', '职位', '岗位', '招聘'])) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        confidence: 0.8,
        entities: {}
      };
    }
    
    // 推荐请求识别
    if (this.utilities.containsAny(lowerMessage, ['推荐', '建议', '合适的'])) {
      return {
        type: this.intentTypes.RECOMMENDATION_REQUEST,
        confidence: 0.8,
        entities: {}
      };
    }
    
    // 技术方向询问
    if (this.utilities.containsAny(lowerMessage, ['技术', '开发', '前端', '后端', '算法'])) {
      return {
        type: this.intentTypes.TECH_DIRECTION_INQUIRY,
        confidence: 0.7,
        entities: {}
      };
    }
    
    // 薪资询问
    if (this.utilities.containsAny(lowerMessage, ['薪资', '工资', '薪水', '待遇', '收入'])) {
      return {
        type: this.intentTypes.SALARY_INQUIRY,
        confidence: 0.8,
        entities: {}
      };
    }
    
    // 默认未知意图
    return {
      type: this.intentTypes.UNKNOWN,
      confidence: 0.3,
      entities: {}
    };
  }

  /**
   * 合并AI和规则引擎的意图分析结果
   */
  mergeIntentAnalysis(aiAnalysis, ruleAnalysis) {
    // 如果AI分析置信度高，优先使用AI结果
    if (aiAnalysis.confidence > 0.8) {
      return aiAnalysis;
    }
    
    // 如果规则引擎置信度高，使用规则结果
    if (ruleAnalysis.confidence > 0.7) {
      return ruleAnalysis;
    }
    
    // 否则选择置信度更高的结果
    return aiAnalysis.confidence >= ruleAnalysis.confidence ? aiAnalysis : ruleAnalysis;
  }

  /**
   * 根据意图路由到相应处理器
   */
  async routeToHandler(intent, messageData, session) {
    try {
      switch (intent.type) {
        case this.intentTypes.GREETING:
          return await this.handleGreeting(messageData, session);
          
        case this.intentTypes.PROFILE_UPDATE:
          return await this.handleProfileUpdate(intent, messageData, session);
          
        case this.intentTypes.JOB_SEARCH:
          return await this.handleJobSearch(intent, messageData, session);
          
        case this.intentTypes.RECOMMENDATION_REQUEST:
          return await this.handleRecommendationRequest(intent, messageData, session);
          
        case this.intentTypes.TECH_DIRECTION_INQUIRY:
          return await this.handleTechDirectionInquiry(intent, messageData, session);
          
        case this.intentTypes.SALARY_INQUIRY:
          return await this.handleSalaryInquiry(intent, messageData, session);
          
        case this.intentTypes.COMPANY_INQUIRY:
          return await this.handleCompanyInquiry(intent, messageData, session);
          
        default:
          return await this.handleUnknownIntent(messageData, session);
      }
      
    } catch (error) {
      console.error('❌ 意图处理失败:', error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 处理问候语
   */
  async handleGreeting(messageData, session) {
    try {
      // 检查用户档案完整性
      const user = await this.userManager.getUserBySession(session);
      const profile = await this.database.getCandidateProfile(user.id);
      
      if (!profile) {
        return {
          type: 'greeting_new_user',
          content: '你好！欢迎使用AI招聘助手。我是Katrina，很高兴为您服务！\n\n为了给您推荐最合适的职位，我需要了解一些基本信息。请告诉我您的技术方向和工作经验？',
          suggestions: [
            '我是前端开发工程师',
            '我做后端开发',
            '我是全栈工程师',
            '我想了解更多'
          ]
        };
      } else {
        return {
          type: 'greeting_returning_user',
          content: `欢迎回来！我记得您是${profile.candidate_tech_direction_raw || '技术'}方向的。\n\n今天想要什么帮助吗？我可以为您推荐新的职位机会。`,
          suggestions: [
            '推荐职位给我',
            '更新我的信息',
            '查看薪资行情',
            '了解公司信息'
          ]
        };
      }
      
    } catch (error) {
      console.error('❌ 处理问候语失败:', error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 获取最近消息
   */
  async getRecentMessages(sessionId, limit = 10) {
    try {
      return await this.database.getSessionMessages(sessionId, limit);
    } catch (error) {
      console.error('❌ 获取最近消息失败:', error);
      return [];
    }
  }

  /**
   * 保存用户消息
   */
  async saveUserMessage(sessionId, message) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: 'user',
        content: message,
        metadata: {
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('❌ 保存用户消息失败:', error);
    }
  }

  /**
   * 保存助手消息
   */
  async saveAssistantMessage(sessionId, response) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: 'assistant',
        content: typeof response === 'string' ? response : response.content,
        metadata: {
          responseType: response.type || 'text',
          suggestions: response.suggestions || [],
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('❌ 保存助手消息失败:', error);
    }
  }

  /**
   * 更新会话上下文
   */
  async updateSessionContext(sessionId, intent, response) {
    try {
      const newContext = {
        lastIntent: intent.type,
        lastResponse: response.type || 'text',
        timestamp: new Date().toISOString()
      };
      
      await this.database.updateSessionActivity(sessionId, newContext);
    } catch (error) {
      console.error('❌ 更新会话上下文失败:', error);
    }
  }

  /**
   * 获取错误回退响应
   */
  getErrorFallbackResponse() {
    return {
      type: 'error_fallback',
      content: '抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。',
      suggestions: [
        '重新开始',
        '联系客服',
        '查看帮助'
      ]
    };
  }

  // 占位符方法 - 将在后续实现
  async handleProfileUpdate(intent, messageData, session) {
    return { type: 'placeholder', content: '档案更新功能开发中...' };
  }

  async handleJobSearch(intent, messageData, session) {
    return { type: 'placeholder', content: '职位搜索功能开发中...' };
  }

  async handleRecommendationRequest(intent, messageData, session) {
    return { type: 'placeholder', content: '推荐功能开发中...' };
  }

  async handleTechDirectionInquiry(intent, messageData, session) {
    return { type: 'placeholder', content: '技术方向询问功能开发中...' };
  }

  async handleSalaryInquiry(intent, messageData, session) {
    return { type: 'placeholder', content: '薪资询问功能开发中...' };
  }

  async handleCompanyInquiry(intent, messageData, session) {
    return { type: 'placeholder', content: '公司询问功能开发中...' };
  }

  async handleUnknownIntent(messageData, session) {
    return {
      type: 'unknown_intent',
      content: '我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：',
      suggestions: [
        '我想找工作',
        '推荐职位给我',
        '更新个人信息',
        '了解薪资行情'
      ]
    };
  }
}

module.exports = MessageProcessor;
