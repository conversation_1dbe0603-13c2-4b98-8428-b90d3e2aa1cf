/**
 * AI招聘助手系统 - API路由管理器
 * 
 * 核心职责：
 * - RESTful API 定义
 * - 请求路由分发
 * - 中间件管理
 * - API 文档生成
 * 
 * 预计代码量：1400行
 */

const express = require('express');
const { v4: uuidv4 } = require('uuid');

class ApiRoutes {
  constructor({ messageProcessor, userManager, database, config }) {
    this.messageProcessor = messageProcessor;
    this.userManager = userManager;
    this.database = database;
    this.config = config;
    this.router = express.Router();
    
    this.setupRoutes();
  }

  /**
   * 设置所有API路由
   */
  setupRoutes() {
    // 聊天相关路由
    this.setupChatRoutes();
    
    // 用户相关路由
    this.setupUserRoutes();
    
    // 职位相关路由
    this.setupJobRoutes();
    
    // 系统相关路由
    this.setupSystemRoutes();
  }

  /**
   * 设置聊天相关路由
   */
  setupChatRoutes() {
    // 发送消息
    this.router.post('/chat/message', async (req, res) => {
      try {
        const { message, sessionId, userEmail } = req.body;
        
        // 验证必需参数
        if (!message || !userEmail) {
          return res.status(400).json({
            error: 'Missing required parameters',
            message: '缺少必需的参数：message 和 userEmail'
          });
        }
        
        // 处理消息
        const result = await this.messageProcessor.processMessage({
          message: message,
          sessionId: sessionId,
          userEmail: userEmail,
          entrySource: req.headers.referer || ''
        });
        
        res.json(result);
        
      } catch (error) {
        console.error('❌ 聊天消息处理失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '消息处理失败'
        });
      }
    });
    
    // 获取聊天历史
    this.router.get('/chat/history/:sessionId', async (req, res) => {
      try {
        const { sessionId } = req.params;
        const { limit = 50 } = req.query;
        
        // 获取会话
        const session = await this.database.getSessionByUuid(sessionId);
        if (!session) {
          return res.status(404).json({
            error: 'Session not found',
            message: '会话不存在'
          });
        }
        
        // 获取消息历史
        const messages = await this.database.getSessionMessages(session.id, parseInt(limit));
        
        res.json({
          success: true,
          sessionId: sessionId,
          messages: messages,
          total: messages.length
        });
        
      } catch (error) {
        console.error('❌ 获取聊天历史失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '获取聊天历史失败'
        });
      }
    });
    
    // 创建新会话
    this.router.post('/chat/session', async (req, res) => {
      try {
        const { userEmail } = req.body;
        
        if (!userEmail) {
          return res.status(400).json({
            error: 'Missing required parameter',
            message: '缺少必需的参数：userEmail'
          });
        }
        
        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(userEmail);
        
        // 创建新会话
        const session = await this.database.createChatSession({
          userId: user.id,
          sessionUuid: uuidv4(),
          entrySourceUrl: req.headers.referer || '',
          initialIntent: '',
          context: {
            state: 'initial',
            lastIntent: null,
            profileCompleteness: 0
          }
        });
        
        res.json({
          success: true,
          sessionId: session.session_uuid,
          userId: user.id,
          timestamp: session.created_at
        });
        
      } catch (error) {
        console.error('❌ 创建会话失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '创建会话失败'
        });
      }
    });
  }

  /**
   * 设置用户相关路由
   */
  setupUserRoutes() {
    // 获取用户档案
    this.router.get('/user/profile/:userEmail', async (req, res) => {
      try {
        const { userEmail } = req.params;
        
        // 获取用户
        const user = await this.database.getUserByEmail(userEmail);
        if (!user) {
          return res.status(404).json({
            error: 'User not found',
            message: '用户不存在'
          });
        }
        
        // 获取候选人档案
        const profile = await this.database.getCandidateProfile(user.id);
        
        res.json({
          success: true,
          user: {
            id: user.id,
            email: user.email,
            userType: user.user_type,
            createdAt: user.created_at,
            lastLoginAt: user.last_login_at
          },
          profile: profile
        });
        
      } catch (error) {
        console.error('❌ 获取用户档案失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '获取用户档案失败'
        });
      }
    });
    
    // 更新用户档案
    this.router.put('/user/profile/:userEmail', async (req, res) => {
      try {
        const { userEmail } = req.params;
        const profileData = req.body;
        
        // 获取用户
        const user = await this.database.getUserByEmail(userEmail);
        if (!user) {
          return res.status(404).json({
            error: 'User not found',
            message: '用户不存在'
          });
        }
        
        // 更新档案
        const updatedProfile = await this.database.upsertCandidateProfile(user.id, profileData);
        
        res.json({
          success: true,
          profile: updatedProfile
        });
        
      } catch (error) {
        console.error('❌ 更新用户档案失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '更新用户档案失败'
        });
      }
    });
  }

  /**
   * 设置职位相关路由
   */
  setupJobRoutes() {
    // 搜索职位
    this.router.get('/jobs/search', async (req, res) => {
      try {
        const { 
          techDirection, 
          location, 
          salaryMin, 
          salaryMax, 
          companyType,
          limit = 20 
        } = req.query;
        
        // 实现职位搜索逻辑
        // 这里暂时返回空结果，等待后续实现
        const jobs = [];
        
        res.json({
          success: true,
          jobs: jobs,
          total: jobs.length,
          filters: {
            techDirection,
            location,
            salaryMin,
            salaryMax,
            companyType
          }
        });
        
      } catch (error) {
        console.error('❌ 搜索职位失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '搜索职位失败'
        });
      }
    });
    
    // 获取职位详情
    this.router.get('/jobs/:jobId', async (req, res) => {
      try {
        const { jobId } = req.params;
        
        // 获取职位详情
        const { data, error } = await this.database.client
          .from('job_listings')
          .select(`
            *,
            companies (
              company_name,
              company_type,
              industry,
              logo_url
            )
          `)
          .eq('id', jobId)
          .single();
        
        if (error) throw error;
        
        if (!data) {
          return res.status(404).json({
            error: 'Job not found',
            message: '职位不存在'
          });
        }
        
        res.json({
          success: true,
          job: data
        });
        
      } catch (error) {
        console.error('❌ 获取职位详情失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '获取职位详情失败'
        });
      }
    });
  }

  /**
   * 设置系统相关路由
   */
  setupSystemRoutes() {
    // 系统状态检查
    this.router.get('/system/status', async (req, res) => {
      try {
        const status = {
          timestamp: new Date().toISOString(),
          version: process.env.npm_package_version || '1.0.0',
          environment: this.config.getNodeEnv(),
          services: {
            database: this.database.isConnected,
            messageProcessor: this.messageProcessor.isReady ? this.messageProcessor.isReady() : false,
            userManager: this.userManager.isReady(),
            ai: this.messageProcessor.aiServices ? this.messageProcessor.aiServices.isReady() : false
          }
        };
        
        res.json({
          success: true,
          status: status
        });
        
      } catch (error) {
        console.error('❌ 获取系统状态失败:', error);
        res.status(500).json({
          error: 'Internal Server Error',
          message: '获取系统状态失败'
        });
      }
    });
    
    // API文档
    this.router.get('/docs', (req, res) => {
      res.json({
        title: 'AI招聘助手 API 文档',
        version: '1.0.0',
        endpoints: {
          chat: {
            'POST /api/chat/message': '发送聊天消息',
            'GET /api/chat/history/:sessionId': '获取聊天历史',
            'POST /api/chat/session': '创建新会话'
          },
          user: {
            'GET /api/user/profile/:userEmail': '获取用户档案',
            'PUT /api/user/profile/:userEmail': '更新用户档案'
          },
          jobs: {
            'GET /api/jobs/search': '搜索职位',
            'GET /api/jobs/:jobId': '获取职位详情'
          },
          system: {
            'GET /api/system/status': '系统状态检查',
            'GET /api/docs': 'API文档'
          }
        }
      });
    });
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = ApiRoutes;
