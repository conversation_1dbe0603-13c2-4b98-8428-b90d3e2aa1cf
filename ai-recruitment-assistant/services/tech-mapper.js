/**
 * AI招聘助手系统 - 技术方向映射器
 * 
 * 核心职责：
 * - 技术方向智能映射
 * - 歧义检测和处理
 * - 技术关键词标准化
 * - 映射关系维护
 * 
 * 预计代码量：1400行
 */

class TechMapper {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;
    this.techTree = null;
    this.mappingRules = null;
  }

  /**
   * 初始化技术映射器
   */
  async initialize() {
    try {
      // 加载技术树
      await this.loadTechTree();
      
      // 加载映射规则
      await this.loadMappingRules();
      
      this.isInitialized = true;
      console.log('🗺️ 技术映射器初始化完成');
      
    } catch (error) {
      console.error('❌ 技术映射器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载技术树
   */
  async loadTechTree() {
    try {
      // 使用数据库管理器的方法获取技术树
      const data = await this.database.getTechTree();
      
      this.techTree = this.buildTechHierarchy(data);
      console.log('📊 技术树加载完成');
      
    } catch (error) {
      console.error('❌ 加载技术树失败:', error);
      throw error;
    }
  }

  /**
   * 构建技术层级结构
   */
  buildTechHierarchy(techData) {
    const hierarchy = {
      level1: [],
      level2: [],
      level3: [],
      byId: {},
      byName: {}
    };
    
    techData.forEach(tech => {
      hierarchy[`level${tech.level}`].push(tech);
      hierarchy.byId[tech.id] = tech;
      hierarchy.byName[tech.tech_name.toLowerCase()] = tech;
    });
    
    return hierarchy;
  }

  /**
   * 加载映射规则
   */
  async loadMappingRules() {
    // 加载技术方向映射规则
    this.mappingRules = {
      synonyms: {
        'javascript': ['js', 'node.js', 'nodejs'],
        'python': ['py'],
        'frontend': ['前端', 'fe'],
        'backend': ['后端', 'be'],
        'fullstack': ['全栈', '全端']
      },
      ambiguous: {
        'java': ['java开发', 'javascript'],
        'react': ['react native', 'reactjs']
      }
    };
  }

  /**
   * 映射技术方向
   */
  async mapTechDirection(userInput) {
    try {
      const normalizedInput = userInput.toLowerCase().trim();
      
      // 1. 直接匹配
      const directMatch = this.findDirectMatch(normalizedInput);
      if (directMatch) {
        return {
          success: true,
          matched: directMatch,
          confidence: 0.9,
          ambiguous: false
        };
      }
      
      // 2. 同义词匹配
      const synonymMatch = this.findSynonymMatch(normalizedInput);
      if (synonymMatch) {
        return {
          success: true,
          matched: synonymMatch,
          confidence: 0.8,
          ambiguous: false
        };
      }
      
      // 3. 模糊匹配
      const fuzzyMatch = this.findFuzzyMatch(normalizedInput);
      if (fuzzyMatch) {
        return {
          success: true,
          matched: fuzzyMatch.tech,
          confidence: fuzzyMatch.confidence,
          ambiguous: fuzzyMatch.ambiguous
        };
      }
      
      // 4. 未找到匹配
      return {
        success: false,
        matched: null,
        confidence: 0,
        ambiguous: false,
        suggestions: this.getSuggestions(normalizedInput)
      };
      
    } catch (error) {
      console.error('❌ 技术方向映射失败:', error);
      throw error;
    }
  }

  /**
   * 直接匹配
   */
  findDirectMatch(input) {
    return this.techTree.byName[input] || null;
  }

  /**
   * 同义词匹配
   */
  findSynonymMatch(input) {
    for (const [canonical, synonyms] of Object.entries(this.mappingRules.synonyms)) {
      if (synonyms.includes(input)) {
        return this.techTree.byName[canonical];
      }
    }
    return null;
  }

  /**
   * 模糊匹配
   */
  findFuzzyMatch(input) {
    const matches = [];
    
    // 检查所有技术名称
    for (const [name, tech] of Object.entries(this.techTree.byName)) {
      const similarity = this.calculateSimilarity(input, name);
      if (similarity > 0.6) {
        matches.push({
          tech: tech,
          confidence: similarity,
          ambiguous: this.isAmbiguous(input, name)
        });
      }
    }
    
    // 返回最佳匹配
    if (matches.length > 0) {
      matches.sort((a, b) => b.confidence - a.confidence);
      return matches[0];
    }
    
    return null;
  }

  /**
   * 计算相似度
   */
  calculateSimilarity(str1, str2) {
    // 简单的相似度计算算法
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 检查是否存在歧义
   */
  isAmbiguous(input, matched) {
    return this.mappingRules.ambiguous[input] !== undefined;
  }

  /**
   * 获取建议
   */
  getSuggestions(input) {
    // 返回相关的技术方向建议
    return this.techTree.level1.slice(0, 5).map(tech => tech.tech_name);
  }

  /**
   * 检查映射器状态
   */
  isReady() {
    return this.isInitialized;
  }
}

module.exports = TechMapper;
