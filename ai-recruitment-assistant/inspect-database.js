/**
 * 数据库结构检查脚本
 * 用于查看现有表结构，避免重复建表
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

async function inspectDatabase() {
  console.log('🔍 开始检查数据库结构...\n');
  
  // 创建Supabase客户端
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // 1. 检查现有表
    console.log('📋 现有表列表:');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');

    if (tablesError) {
      console.error('❌ 获取表列表失败:', tablesError);
      return;
    }

    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });

    // 2. 检查关键表的结构
    const keyTables = ['users', 'chat_sessions', 'chat_messages', 'candidate_profiles'];
    
    for (const tableName of keyTables) {
      console.log(`\n📊 ${tableName} 表结构:`);
      
      // 获取表结构
      const { data: columns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_schema', 'public')
        .eq('table_name', tableName)
        .order('ordinal_position');

      if (columnsError) {
        console.error(`❌ 获取 ${tableName} 表结构失败:`, columnsError);
        continue;
      }

      if (columns && columns.length > 0) {
        columns.forEach(col => {
          console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
        });
      } else {
        console.log(`  ⚠️ 表 ${tableName} 不存在或无权限访问`);
      }
    }

    // 3. 检查会话相关数据
    console.log('\n📈 数据统计:');
    
    const { data: userCount } = await supabase
      .from('users')
      .select('id', { count: 'exact', head: true });
    console.log(`  - 用户数量: ${userCount?.length || 0}`);

    const { data: sessionCount } = await supabase
      .from('chat_sessions')
      .select('id', { count: 'exact', head: true });
    console.log(`  - 会话数量: ${sessionCount?.length || 0}`);

    const { data: messageCount } = await supabase
      .from('chat_messages')
      .select('id', { count: 'exact', head: true });
    console.log(`  - 消息数量: ${messageCount?.length || 0}`);

    // 4. 检查最近的会话
    console.log('\n🕒 最近的会话:');
    const { data: recentSessions } = await supabase
      .from('chat_sessions')
      .select('id, session_uuid, created_at, last_active_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (recentSessions && recentSessions.length > 0) {
      recentSessions.forEach(session => {
        console.log(`  - Session ${session.session_uuid.substring(0, 8)}... (${session.created_at})`);
      });
    } else {
      console.log('  - 暂无会话记录');
    }

    console.log('\n✅ 数据库检查完成!');

  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
  }
}

// 运行检查
inspectDatabase();
