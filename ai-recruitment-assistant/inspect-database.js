/**
 * 数据库结构检查脚本
 * 用于查看现有表结构，避免重复建表
 */

require("dotenv").config({ path: "../.env.local" });
const { createClient } = require("@supabase/supabase-js");

async function inspectDatabase() {
  console.log("🔍 开始检查数据库结构...\n");

  // 创建Supabase客户端
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // 1. 直接检查关键表
    console.log("📋 检查关键表:");
    const keyTables = [
      "tech_tree",
      "candidate_standard_levels",
      "companies",
      "users",
      "chat_sessions",
    ];

    for (const tableName of keyTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select("*")
          .limit(1);

        if (error) {
          console.log(`  ❌ ${tableName} - 不存在或无权限`);
        } else {
          console.log(`  ✅ ${tableName} - 存在`);
        }
      } catch (e) {
        console.log(`  ❌ ${tableName} - 检查失败`);
      }
    }

    // 2. 检查关键表的结构和数据
    const detailTables = [
      "tech_tree",
      "candidate_standard_levels",
      "companies",
    ];

    for (const tableName of detailTables) {
      console.log(`\n📊 ${tableName} 表结构:`);

      // 获取表结构
      const { data: columns, error: columnsError } = await supabase
        .from("information_schema.columns")
        .select("column_name, data_type, is_nullable, column_default")
        .eq("table_schema", "public")
        .eq("table_name", tableName)
        .order("ordinal_position");

      if (columnsError) {
        console.error(`❌ 获取 ${tableName} 表结构失败:`, columnsError);
        continue;
      }

      if (columns && columns.length > 0) {
        columns.forEach((col) => {
          console.log(
            `  - ${col.column_name} (${col.data_type}) ${col.is_nullable === "NO" ? "NOT NULL" : "NULL"}`
          );
        });

        // 获取表数据样例
        console.log(`\n📋 ${tableName} 数据样例:`);
        const { data: sampleData, error: dataError } = await supabase
          .from(tableName)
          .select("*")
          .limit(10);

        if (dataError) {
          console.error(`❌ 获取 ${tableName} 数据失败:`, dataError);
        } else if (sampleData && sampleData.length > 0) {
          sampleData.forEach((row, index) => {
            console.log(`  ${index + 1}. ${JSON.stringify(row)}`);
          });
        }
      } else {
        console.log(`  ⚠️ 表 ${tableName} 不存在或无权限访问`);
      }
    }

    // 3. 专门查看tech_tree的层级关系
    console.log("\n🌳 技术树层级关系:");
    const { data: techTree, error: techError } = await supabase
      .from("tech_tree")
      .select("*")
      .order("level", { ascending: true })
      .order("parent_id", { ascending: true });

    if (techError) {
      console.error("❌ 获取技术树失败:", techError);
    } else if (techTree) {
      const levels = {};
      techTree.forEach((tech) => {
        if (!levels[tech.level]) levels[tech.level] = [];
        levels[tech.level].push(tech);
      });

      Object.keys(levels).forEach((level) => {
        console.log(`\n  Level ${level}:`);
        levels[level].forEach((tech) => {
          console.log(
            `    - ${tech.tech_name} (ID: ${tech.id}, Parent: ${tech.parent_id})`
          );
        });
      });
    }

    // 4. 查看职级对照表
    console.log("\n📈 职级对照表:");
    const { data: levels, error: levelError } = await supabase
      .from("candidate_standard_levels")
      .select("*")
      .order("level_order");

    if (levelError) {
      console.error("❌ 获取职级表失败:", levelError);
    } else if (levels) {
      levels.forEach((level) => {
        console.log(`  - ${level.level_name}: ${level.description || "N/A"}`);
      });
    }

    // 5. 查看公司信息
    console.log("\n🏢 公司信息样例:");
    const { data: companies, error: companyError } = await supabase
      .from("companies")
      .select("*")
      .limit(20);

    if (companyError) {
      console.error("❌ 获取公司信息失败:", companyError);
    } else if (companies) {
      companies.forEach((company) => {
        console.log(
          `  - ${company.company_name} (类型: ${company.company_type}, 行业: ${company.industry})`
        );
      });
    }

    // 3. 检查会话相关数据
    console.log("\n📈 数据统计:");

    const { data: userCount } = await supabase
      .from("users")
      .select("id", { count: "exact", head: true });
    console.log(`  - 用户数量: ${userCount?.length || 0}`);

    const { data: sessionCount } = await supabase
      .from("chat_sessions")
      .select("id", { count: "exact", head: true });
    console.log(`  - 会话数量: ${sessionCount?.length || 0}`);

    const { data: messageCount } = await supabase
      .from("chat_messages")
      .select("id", { count: "exact", head: true });
    console.log(`  - 消息数量: ${messageCount?.length || 0}`);

    // 4. 检查最近的会话
    console.log("\n🕒 最近的会话:");
    const { data: recentSessions } = await supabase
      .from("chat_sessions")
      .select("id, session_uuid, created_at, last_active_at")
      .order("created_at", { ascending: false })
      .limit(5);

    if (recentSessions && recentSessions.length > 0) {
      recentSessions.forEach((session) => {
        console.log(
          `  - Session ${session.session_uuid.substring(0, 8)}... (${session.created_at})`
        );
      });
    } else {
      console.log("  - 暂无会话记录");
    }

    console.log("\n✅ 数据库检查完成!");
  } catch (error) {
    console.error("❌ 数据库检查失败:", error);
  }
}

// 运行检查
inspectDatabase();
