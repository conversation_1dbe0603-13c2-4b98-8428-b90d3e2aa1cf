/**
 * AI招聘助手系统 - 系统入口文件
 * 
 * 核心职责：
 * - 系统初始化
 * - 模块协调
 * - 启动流程管理
 * - 全局错误处理
 * 
 * 预计代码量：800行
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

// 导入核心模块
const AppConfig = require('./config/app-config');
const MessageProcessor = require('./message-processor');
const DatabaseManager = require('./database-manager');
const UserManager = require('./services/user-manager');
const ApiRoutes = require('./routes/api-routes');

class AIRecruitmentSystem {
  constructor() {
    this.app = express();
    this.config = null;
    this.database = null;
    this.messageProcessor = null;
    this.userManager = null;
    this.isInitialized = false;
  }

  /**
   * 系统初始化
   */
  async initialize() {
    try {
      console.log('🚀 AI招聘助手系统启动中...');
      
      // 1. 加载配置
      await this.loadConfiguration();
      
      // 2. 初始化数据库
      await this.initializeDatabase();
      
      // 3. 初始化核心模块
      await this.initializeCoreModules();
      
      // 4. 配置中间件
      this.configureMiddleware();
      
      // 5. 设置路由
      this.setupRoutes();
      
      // 6. 配置错误处理
      this.setupErrorHandling();
      
      this.isInitialized = true;
      console.log('✅ 系统初始化完成');
      
    } catch (error) {
      console.error('❌ 系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载系统配置
   */
  async loadConfiguration() {
    this.config = new AppConfig();
    await this.config.initialize();
    console.log('📋 配置加载完成');
  }

  /**
   * 初始化数据库连接
   */
  async initializeDatabase() {
    this.database = new DatabaseManager(this.config.getDatabaseConfig());
    await this.database.connect();
    await this.database.checkHealth();
    console.log('🗄️ 数据库连接成功');
  }

  /**
   * 初始化核心业务模块
   */
  async initializeCoreModules() {
    // 用户管理器
    this.userManager = new UserManager(this.database, this.config);
    await this.userManager.initialize();
    
    // 消息处理器（主路由）
    this.messageProcessor = new MessageProcessor({
      database: this.database,
      userManager: this.userManager,
      config: this.config
    });
    await this.messageProcessor.initialize();
    
    console.log('🧠 核心模块初始化完成');
  }

  /**
   * 配置Express中间件
   */
  configureMiddleware() {
    // 安全中间件
    this.app.use(helmet());
    
    // CORS配置
    this.app.use(cors({
      origin: this.config.getAllowedOrigins(),
      credentials: true
    }));
    
    // 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // 请求日志
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * 设置API路由
   */
  setupRoutes() {
    const apiRoutes = new ApiRoutes({
      messageProcessor: this.messageProcessor,
      userManager: this.userManager,
      database: this.database,
      config: this.config
    });
    
    this.app.use('/api', apiRoutes.getRouter());
    
    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
      });
    });
    
    // 静态文件服务
    this.app.use(express.static('public'));
  }

  /**
   * 配置全局错误处理
   */
  setupErrorHandling() {
    // 404处理
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `路径 ${req.path} 不存在`
      });
    });
    
    // 全局错误处理
    this.app.use((error, req, res, next) => {
      console.error('全局错误:', error);
      
      res.status(error.status || 500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      });
    });
  }

  /**
   * 启动服务器
   */
  async start() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    const port = this.config.getServerPort();
    
    this.server = this.app.listen(port, () => {
      console.log(`🌟 AI招聘助手系统运行在端口 ${port}`);
      console.log(`📱 访问地址: http://localhost:${port}`);
    });
    
    // 优雅关闭处理
    this.setupGracefulShutdown();
  }

  /**
   * 配置优雅关闭
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`\n收到 ${signal} 信号，开始优雅关闭...`);
      
      if (this.server) {
        this.server.close(async () => {
          console.log('HTTP服务器已关闭');
          
          // 关闭数据库连接
          if (this.database) {
            await this.database.disconnect();
            console.log('数据库连接已关闭');
          }
          
          console.log('✅ 系统已安全关闭');
          process.exit(0);
        });
      }
    };
    
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }

  /**
   * 获取应用实例（用于测试）
   */
  getApp() {
    return this.app;
  }
}

// 如果直接运行此文件，启动系统
if (require.main === module) {
  const system = new AIRecruitmentSystem();
  
  system.start().catch(error => {
    console.error('❌ 系统启动失败:', error);
    process.exit(1);
  });
}

module.exports = AIRecruitmentSystem;
