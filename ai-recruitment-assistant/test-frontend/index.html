<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI招聘助手 - 测试界面</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .chat-container {
        width: 90%;
        max-width: 800px;
        height: 80vh;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .chat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .chat-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
      }

      .chat-header p {
        opacity: 0.9;
        font-size: 14px;
      }

      .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f8f9fa;
      }

      .message {
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
      }

      .message.user {
        justify-content: flex-end;
      }

      .message.assistant {
        justify-content: flex-start;
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
      }

      .message.user .message-content {
        background: #007bff;
        color: white;
        border-bottom-right-radius: 4px;
      }

      .message.assistant .message-content {
        background: white;
        color: #333;
        border: 1px solid #e9ecef;
        border-bottom-left-radius: 4px;
      }

      .message-time {
        font-size: 11px;
        color: #6c757d;
        margin: 5px 10px 0;
      }

      .chat-input {
        padding: 20px;
        background: white;
        border-top: 1px solid #e9ecef;
      }

      .input-group {
        display: flex;
        gap: 10px;
      }

      .input-field {
        flex: 1;
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.3s;
      }

      .input-field:focus {
        border-color: #667eea;
      }

      .send-button {
        padding: 12px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: transform 0.2s;
      }

      .send-button:hover {
        transform: translateY(-1px);
      }

      .send-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .typing-indicator {
        display: none;
        padding: 10px 16px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 18px;
        margin-bottom: 15px;
        max-width: 70%;
      }

      .typing-dots {
        display: flex;
        gap: 4px;
      }

      .typing-dot {
        width: 8px;
        height: 8px;
        background: #6c757d;
        border-radius: 50%;
        animation: typing 1.4s infinite;
      }

      .typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }

      .typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes typing {
        0%,
        60%,
        100% {
          transform: translateY(0);
        }
        30% {
          transform: translateY(-10px);
        }
      }

      .status-bar {
        padding: 10px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        font-size: 12px;
        color: #6c757d;
        text-align: center;
      }

      .status-connected {
        color: #28a745;
      }

      .status-error {
        color: #dc3545;
      }
    </style>
  </head>
  <body>
    <div class="chat-container">
      <div class="chat-header">
        <h1>🤖 AI招聘助手</h1>
        <p>测试第一句回复处理功能</p>
      </div>

      <div class="chat-messages" id="chatMessages">
        <div class="message assistant">
          <div class="message-content">
            你好！我是猎头，专门做技术岗位推荐的，有合适的机会可以聊聊吗？
          </div>
          <div class="message-time">刚刚</div>
        </div>
      </div>

      <div class="typing-indicator" id="typingIndicator">
        <div class="typing-dots">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>

      <div class="chat-input">
        <div class="input-group">
          <input
            type="text"
            class="input-field"
            id="messageInput"
            placeholder="输入消息测试功能..."
            onkeypress="handleKeyPress(event)"
          />
          <button class="send-button" id="sendButton" onclick="sendMessage()">
            发送
          </button>
        </div>
      </div>

      <div class="status-bar">
        <span id="statusText">准备就绪</span>
      </div>
    </div>

    <script>
      const API_BASE_URL = "http://localhost:3000/api";
      let sessionId = null;

      // 初始化
      document.addEventListener("DOMContentLoaded", function () {
        // 生成会话ID
        sessionId = localStorage.getItem("sessionId");
        if (!sessionId) {
          sessionId =
            "session_" +
            Date.now() +
            "_" +
            Math.random().toString(36).substr(2, 9);
          localStorage.setItem("sessionId", sessionId);
        }
        console.log("当前sessionId:", sessionId);
        updateStatus("已连接到测试服务器", "connected");
      });

      // 处理回车键
      function handleKeyPress(event) {
        if (event.key === "Enter" && !event.shiftKey) {
          event.preventDefault();
          sendMessage();
        }
      }

      // 发送消息
      async function sendMessage() {
        const input = document.getElementById("messageInput");
        const message = input.value.trim();

        if (!message) return;

        // 显示用户消息
        addMessage(message, "user");
        input.value = "";

        // 禁用发送按钮
        const sendButton = document.getElementById("sendButton");
        sendButton.disabled = true;

        // 显示输入指示器
        showTypingIndicator();

        try {
          updateStatus("正在处理消息...", "processing");

          const response = await fetch(`${API_BASE_URL}/chat`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              message: message,
              sessionId: sessionId,
            }),
          });

          const data = await response.json();

          if (data.success) {
            sessionId = data.sessionId;

            // 添加助手回复
            addMessage(data.response.content, "assistant");

            // 如果是第一句回复，2秒后发送引导消息
            if (
              data.response.metadata &&
              data.response.metadata.needsFollowUp
            ) {
              setTimeout(() => {
                const followUpMessage =
                  data.response.metadata.followUpMessage ||
                  "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？";
                addMessage(followUpMessage, "assistant");
              }, 2000);
            }

            updateStatus("消息已处理", "connected");
          } else {
            addMessage(
              "抱歉，处理消息时出现错误：" + (data.error || "未知错误"),
              "assistant"
            );
            updateStatus("处理失败", "error");
          }
        } catch (error) {
          console.error("发送消息失败:", error);
          addMessage("连接服务器失败，请检查服务器是否启动", "assistant");
          updateStatus("连接失败", "error");
        } finally {
          hideTypingIndicator();
          sendButton.disabled = false;
        }
      }

      // 添加消息到聊天界面
      function addMessage(content, type) {
        const messagesContainer = document.getElementById("chatMessages");
        const messageDiv = document.createElement("div");
        messageDiv.className = `message ${type}`;

        const now = new Date();
        const timeStr = now.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        });

        messageDiv.innerHTML = `
                <div class="message-content">${content}</div>
                <div class="message-time">${timeStr}</div>
            `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // 显示输入指示器
      function showTypingIndicator() {
        document.getElementById("typingIndicator").style.display = "block";
        const messagesContainer = document.getElementById("chatMessages");
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // 隐藏输入指示器
      function hideTypingIndicator() {
        document.getElementById("typingIndicator").style.display = "none";
      }

      // 更新状态
      function updateStatus(text, type = "") {
        const statusElement = document.getElementById("statusText");
        statusElement.textContent = text;
        statusElement.className = type ? `status-${type}` : "";
      }
    </script>
  </body>
</html>
