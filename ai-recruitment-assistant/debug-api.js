const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testAPI() {
  console.log('=== 测试第一句回复逻辑 ===\n');
  
  // 使用标准UUID格式
  const testSessionUuid = '550e8400-e29b-41d4-a716-************';
  const testEmail = '<EMAIL>';
  
  console.log(`测试会话UUID: ${testSessionUuid}`);
  console.log(`测试邮箱: ${testEmail}\n`);
  
  // 清理旧数据
  console.log('清理旧测试数据...');
  const { data: existingUser } = await supabase
    .from('users')
    .select('id')
    .eq('email', testEmail)
    .single();
    
  if (existingUser) {
    const { data: sessions } = await supabase
      .from('chat_sessions')
      .select('id')
      .eq('user_id', existingUser.id);
      
    if (sessions) {
      for (const session of sessions) {
        await supabase.from('chat_messages').delete().eq('session_id', session.id);
        await supabase.from('chat_sessions').delete().eq('id', session.id);
      }
    }
    await supabase.from('users').delete().eq('id', existingUser.id);
    console.log('清理完成');
  }
  
  // 测试API调用
  console.log('\n发送API请求...');
  
  try {
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '你好',
        sessionId: testSessionUuid,
        userEmail: testEmail
      })
    });
    
    const result = await response.json();
    
    console.log('\n=== API响应 ===');
    console.log(`Success: ${result.success}`);
    console.log(`Intent: ${result.intent}`);
    console.log(`Response Type: ${result.response?.type}`);
    console.log(`Content: ${result.response?.content}`);
    
    // 检查数据库
    console.log('\n=== 数据库检查 ===');
    const { data: session } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('session_uuid', result.sessionId)
      .single();
      
    if (session) {
      const { data: messages } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', session.id)
        .order('timestamp', { ascending: true });
        
      console.log(`会话ID: ${session.id}`);
      console.log(`消息总数: ${messages?.length || 0}`);
      
      const userMessages = messages?.filter(m => m.message_type === 'user') || [];
      console.log(`用户消息数: ${userMessages.length}`);
      
      console.log('\n消息列表:');
      messages?.forEach((msg, i) => {
        console.log(`${i+1}. [${msg.message_type}] ${msg.message_content?.substring(0, 50)}...`);
      });
      
      // 分析结果
      console.log('\n=== 结果分析 ===');
      if (result.response?.type === 'first_greeting_simple') {
        console.log('✅ 成功！返回了正确的第一句问候回复');
      } else {
        console.log('❌ 失败！没有返回预期的第一句回复');
        console.log(`期望: first_greeting_simple`);
        console.log(`实际: ${result.response?.type}`);
      }
    }
    
  } catch (error) {
    console.error('API调用失败:', error.message);
  }
}

testAPI().catch(console.error);
