// test-hardcoded-replies.js - 测试硬编码回复系统

const HardcodedReplies = require('./core/回复引擎/hardcoded-replies');

function testHardcodedReplies() {
  console.log('🧪 测试硬编码回复系统\n');
  
  const repliesEngine = new HardcodedReplies();
  
  // 测试用例
  const testCases = [
    {
      name: "简单问候",
      message: "你好",
      expectedType: "first_greeting_simple",
      expectedContent: "您有兴趣看看新机会吗？"
    },
    {
      name: "英文问候",
      message: "hello",
      expectedType: "first_greeting_simple",
      expectedContent: "您有兴趣看看新机会吗？"
    },
    {
      name: "职位询问",
      message: "有什么职位",
      expectedType: "first_job_inquiry",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "工作询问",
      message: "有什么工作机会",
      expectedType: "first_job_inquiry",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "身份确认",
      message: "你是谁",
      expectedType: "first_greeting_full",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "AI询问",
      message: "你是机器人吗",
      expectedType: "first_greeting_full",
      expectedContent: "我们合作的公司挺多的"
    },
    {
      name: "技术栈询问",
      message: "需要什么技术栈",
      expectedType: "first_tech_inquiry",
      expectedContent: "我们有各种技术栈的职位"
    },
    {
      name: "薪资询问",
      message: "薪资多少",
      expectedType: "first_salary_inquiry",
      expectedContent: "薪资范围根据技术栈和经验而定"
    },
    {
      name: "未知消息",
      message: "随便说点什么",
      expectedType: "first_greeting_default",
      expectedContent: "我们合作的公司挺多的"
    }
  ];
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  console.log(`📋 开始测试 ${totalTests} 个场景...\n`);
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. 测试: ${testCase.name}`);
    console.log(`   输入: "${testCase.message}"`);
    
    const result = repliesEngine.getFirstReply(testCase.message);
    
    const typeMatch = result.type === testCase.expectedType;
    const contentMatch = result.content.includes(testCase.expectedContent);
    
    if (typeMatch && contentMatch) {
      console.log(`   ✅ 通过`);
      console.log(`   📤 类型: ${result.type}`);
      console.log(`   💬 内容: ${result.content.substring(0, 50)}...`);
      passedTests++;
    } else {
      console.log(`   ❌ 失败`);
      console.log(`   期望类型: ${testCase.expectedType}, 实际: ${result.type}`);
      console.log(`   期望内容包含: "${testCase.expectedContent}"`);
      console.log(`   实际内容: ${result.content.substring(0, 100)}...`);
    }
    
    console.log('');
  });
  
  // 测试统计
  console.log('📊 测试结果统计:');
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！硬编码回复系统工作正常。');
  } else {
    console.log('⚠️ 部分测试失败，需要检查规则配置。');
  }
  
  // 显示系统统计
  console.log('\n📈 系统统计:');
  const stats = repliesEngine.getStats();
  console.log(`   规则数量: ${stats.totalRules}`);
  console.log(`   关键词总数: ${stats.totalKeywords}`);
  console.log(`   规则类别: ${stats.categories.join(', ')}`);
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  testHardcodedReplies();
}

module.exports = testHardcodedReplies;
