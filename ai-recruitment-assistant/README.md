# AI招聘助手系统

> 基于15文件架构的智能招聘助手，集成DeepSeek AI和Supabase数据库

## 🎯 项目概述

AI招聘助手系统是一个智能化的招聘平台，采用15文件架构设计，实现了完整的AI对话、职位推荐、候选人档案管理等功能。

### 核心功能

- 🤖 **智能对话** - 基于DeepSeek的自然语言理解
- 🎯 **4x4矩阵推荐** - 智能职位推荐引擎
- 👤 **候选人档案** - 完整的用户档案管理
- 🗺️ **技术映射** - 智能技术方向识别
- 💬 **实时聊天** - 流畅的对话体验

## 🏗️ 架构设计

### 15文件架构

```
ai-recruitment-assistant/
├── core/                          # 15个核心文件分类存放
│   ├── 系统核心/                   # System Core (3个文件)
│   │   ├── index.js               # 1. 系统入口 (800行)
│   │   ├── app-config.js          # 2. 应用配置 (1000行)
│   │   └── message-processor.js   # 3. 消息处理器 (2000行)
│   │
│   ├── 数据管理/                   # Data Management (2个文件)
│   │   ├── database-manager.js    # 4. 数据库管理 (1800行)
│   │   └── ai-services.js         # 5. AI服务 (1600行)
│   │
│   ├── 业务服务/                   # Business Services (4个文件)
│   │   ├── user-manager.js        # 6. 用户管理 (1200行)
│   │   ├── passive-recommender.js # 7. 被动推荐引擎 (1800行)
│   │   ├── active-recommender.js  # 8. 主动推荐引擎 (1500行)
│   │   └── tech-mapper.js         # 9. 技术映射器 (1400行)
│   │
│   ├── 接口路由/                   # API Routes (1个文件)
│   │   └── api-routes.js          # 10. API路由 (1400行)
│   │
│   ├── 前端界面/                   # Frontend UI (2个文件)
│   │   ├── chat-interface.js      # 11. 聊天界面 (1600行)
│   │   └── ui-components.js       # 12. UI组件库 (1200行)
│   │
│   └── 工具库/                     # Utilities (3个文件)
│       ├── mapping-tables.js      # 13. 映射表 (1200行)
│       ├── utilities.js           # 14. 工具函数 (800行)
│       └── validators.js          # 15. 验证器 (600行)
│
├── package.json                   # 项目配置
└── README.md                      # 项目文档
```

**总计**: 15个文件，约20,000行代码

### 📊 文件分类说明

- **系统核心** (3个文件): 系统启动、配置管理、消息处理主流程
- **数据管理** (2个文件): 数据库操作、AI服务接口
- **业务服务** (4个文件): 用户管理、推荐引擎、技术映射等核心业务
- **接口路由** (1个文件): RESTful API路由定义
- **前端界面** (2个文件): 聊天界面、UI组件库
- **工具库** (3个文件): 映射表、工具函数、数据验证

### 技术栈

- **后端**: Node.js + Express.js
- **数据库**: Supabase (PostgreSQL)
- **AI服务**: DeepSeek API
- **前端**: 原生JavaScript + CSS3
- **工具**: Joi验证、Winston日志

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn
- Supabase 账户
- DeepSeek API 密钥

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-recruitment-assistant
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置值
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
```
http://localhost:3000
```

## ⚙️ 配置说明

### 必需配置

```env
# Supabase 数据库
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# DeepSeek AI
DEEPSEEK_API_KEY=your-deepseek-api-key

# 用户邮箱（开发测试用）
USER_EMAIL=<EMAIL>
```

### 可选配置

- **服务器端口**: `PORT=3000`
- **日志级别**: `LOG_LEVEL=info`
- **缓存设置**: `CACHE_ENABLED=true`
- **速率限制**: `RATE_LIMIT_MAX=100`

## 📊 数据库结构

系统使用Supabase作为数据库，主要表结构：

- **users** - 用户表
- **chat_sessions** - 会话表
- **chat_messages** - 消息表
- **candidate_profiles** - 候选人档案表
- **companies** - 公司表
- **job_listings** - 职位表
- **tech_tree** - 技术树表
- **business_scenarios** - 业务场景表

详细结构请参考 `DATABASE_REFERENCE.md`

## 🔧 API接口

### 聊天接口

```bash
# 发送消息
POST /api/chat/message
{
  "message": "我想找前端工作",
  "sessionId": "uuid",
  "userEmail": "<EMAIL>"
}

# 获取聊天历史
GET /api/chat/history/:sessionId

# 创建新会话
POST /api/chat/session
{
  "userEmail": "<EMAIL>"
}
```

### 用户接口

```bash
# 获取用户档案
GET /api/user/profile/:userEmail

# 更新用户档案
PUT /api/user/profile/:userEmail
{
  "techDirection": "前端开发",
  "experienceYears": 3,
  "expectedSalary": { "min": 15000, "max": 25000 }
}
```

### 职位接口

```bash
# 搜索职位
GET /api/jobs/search?techDirection=frontend&location=北京

# 获取职位详情
GET /api/jobs/:jobId
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行测试并监听变化
npm run test:watch

# 代码检查
npm run lint

# 代码格式化
npm run lint:fix
```

## 📈 性能监控

### 系统状态检查

```bash
GET /api/system/status
```

返回系统各模块的运行状态：

```json
{
  "success": true,
  "status": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0",
    "environment": "development",
    "services": {
      "database": true,
      "messageProcessor": true,
      "userManager": true,
      "ai": true
    }
  }
}
```

## 🔒 安全考虑

- **输入验证**: 所有用户输入都经过严格验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容自动转义
- **速率限制**: API调用频率限制
- **CORS配置**: 跨域请求控制

## 📝 开发指南

### 添加新功能

1. **确定功能归属** - 选择合适的文件模块
2. **更新接口** - 在 `api-routes.js` 添加新接口
3. **实现业务逻辑** - 在对应的业务模块中实现
4. **添加验证** - 在 `validators.js` 添加数据验证
5. **更新前端** - 在 `chat-interface.js` 或 `ui-components.js` 更新界面

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 JavaScript Standard Style
- 函数和类都要有详细的注释
- 错误处理要完整和友好

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `SUPABASE_URL` 和 `SUPABASE_SERVICE_ROLE_KEY`
   - 确认网络连接正常

2. **AI服务调用失败**
   - 检查 `DEEPSEEK_API_KEY` 是否正确
   - 确认API额度是否充足

3. **前端界面异常**
   - 检查浏览器控制台错误信息
   - 确认API接口返回正常

### 日志查看

```bash
# 查看实时日志
npm run logs

# 查看错误日志
npm run logs:error
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: AI Recruitment Team
- 邮箱: <EMAIL>
- 文档: [项目文档](docs/)

---

**🚀 现在就开始使用AI招聘助手，体验智能化的招聘服务！**
