// test-user-first-reply.js - 测试用户第一句回复的4种场景

const MessageProcessor = require("./core/系统核心/message-processor");

async function testUserFirstReply() {
  console.log("🧪 测试用户第一句回复的4种场景\n");

  // 创建一个简化的配置用于测试
  const mockConfig = {
    database: { url: "mock" },
    ai: { apiKey: "mock" },
    cache: { enabled: false },
  };

  const processor = new MessageProcessor(mockConfig);

  // 测试用例
  const testCases = [
    {
      name: "问候类",
      message: "你好",
      expectedCategory: "GREETING",
      expectedType: "user_first_greeting",
      expectedContent: "您有兴趣看看新机会吗",
    },
    {
      name: "问候类-英文",
      message: "hi",
      expectedCategory: "GREETING",
      expectedType: "user_first_greeting",
      expectedContent: "您有兴趣看看新机会吗",
    },
    {
      name: "直接咨询类",
      message: "有什么职位推荐",
      expectedCategory: "DIRECT_INQUIRY",
      expectedType: "user_first_direct_inquiry",
      expectedContent: "我们合作的公司挺多的，大厂、中厂、创业、国企公司都有",
    },
    {
      name: "直接咨询类-简化",
      message: "有什么工作",
      expectedCategory: "DIRECT_INQUIRY",
      expectedType: "user_first_direct_inquiry",
      expectedContent: "我们合作的公司挺多的",
    },
    {
      name: "疑惑类",
      message: "你是AI吗",
      expectedCategory: "DOUBT_AI",
      expectedType: "user_first_doubt_ai",
      expectedContent: "我是Felton团队创造的AI猎头顾问",
    },
    {
      name: "疑惑类-简化",
      message: "机器人",
      expectedCategory: "DOUBT_AI",
      expectedType: "user_first_doubt_ai",
      expectedContent: "我是Felton团队创造的AI猎头顾问",
    },
    {
      name: "API推理类",
      message: "我是前端工程师，3年经验，想找大厂职位",
      expectedCategory: "API_INFERENCE",
      expectedType: "user_first_api_inquiry",
      expectedContent: "我们合作的公司挺多的",
    },
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  console.log(`📋 开始测试 ${totalTests} 个场景...\n`);

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`${i + 1}. 测试: ${testCase.name}`);
    console.log(`   输入: "${testCase.message}"`);

    try {
      // 测试分类功能
      const category = processor.classifyUserFirstReply(testCase.message);
      console.log(`   分类结果: ${category}`);

      // 测试完整处理流程
      const result = await processor.handleFirstUserGreeting(
        { message: testCase.message },
        { sessionId: `test-${i}` },
        { messageCategory: category }
      );

      // 检查分类
      const categoryMatch = category === testCase.expectedCategory;
      // 检查回复类型
      const typeMatch = result?.type === testCase.expectedType;
      // 检查回复内容
      const contentMatch = result?.content?.includes(testCase.expectedContent);

      if (categoryMatch && typeMatch && contentMatch) {
        console.log(`   ✅ 通过`);
        console.log(`   📤 类型: ${result.type}`);
        console.log(`   💬 内容: ${result.content.substring(0, 50)}...`);

        // 检查是否有延迟追加内容
        if (result.hasFollowUp) {
          console.log(
            `   ⏰ 延迟追加: ${result.followUpDelay}ms - ${result.followUpContent.substring(0, 30)}...`
          );
        }

        passedTests++;
      } else {
        console.log(`   ❌ 失败`);
        if (!categoryMatch) {
          console.log(
            `   期望分类: ${testCase.expectedCategory}, 实际: ${category}`
          );
        }
        if (!typeMatch) {
          console.log(
            `   期望类型: ${testCase.expectedType}, 实际: ${result?.type}`
          );
        }
        if (!contentMatch) {
          console.log(`   期望内容包含: "${testCase.expectedContent}"`);
          console.log(`   实际内容: ${result?.content?.substring(0, 100)}...`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 异常: ${error.message}`);
    }

    console.log("");
  }

  // 测试结果统计
  console.log("📊 测试结果统计:");
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log("🎉 所有测试通过！用户第一句回复系统工作正常。");
  } else {
    console.log("⚠️ 部分测试失败，需要检查实现。");
  }

  // 显示4种场景总结
  console.log("\n📋 4种场景总结:");
  console.log(
    '1. 问候类 → "您有兴趣看看新机会吗？我这边合作的客户职位还挺多的。"'
  );
  console.log(
    '2. 直接咨询类 → "我们合作的公司挺多的，大厂、中厂、创业、国企公司都有，职位也不少。" + 2秒延迟追加'
  );
  console.log(
    '3. 疑惑类 → "我是Felton团队创造的AI猎头顾问，我会为您针对性的提供合适的职位。" + 2秒延迟追加'
  );
  console.log("4. 其他 → API推理处理");

  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  testUserFirstReply()
    .then((success) => {
      console.log(`\n🏁 测试完成，结果: ${success ? "成功" : "失败"}`);
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("❌ 测试异常:", error);
      process.exit(1);
    });
}

module.exports = testUserFirstReply;
