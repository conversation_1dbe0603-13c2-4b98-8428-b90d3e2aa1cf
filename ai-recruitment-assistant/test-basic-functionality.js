/**
 * AI招聘助手系统 - 基础功能测试
 * 
 * 用于验证15文件架构的基本功能是否正常工作
 */

require('dotenv').config();

// 导入核心模块
const AppConfig = require('./app-config');
const DatabaseManager = require('./database-manager');
const MessageProcessor = require('./message-processor');
const UserManager = require('./user-manager');
const AIServices = require('./ai-services');
const MappingTables = require('./mapping-tables');
const Utilities = require('./utilities');
const Validators = require('./validators');

class BasicFunctionalityTest {
  constructor() {
    this.config = null;
    this.database = null;
    this.messageProcessor = null;
    this.userManager = null;
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始基础功能测试...\n');

    try {
      // 1. 配置测试
      await this.testConfiguration();
      
      // 2. 数据库连接测试
      await this.testDatabaseConnection();
      
      // 3. 工具函数测试
      await this.testUtilities();
      
      // 4. 验证器测试
      await this.testValidators();
      
      // 5. 映射表测试
      await this.testMappingTables();
      
      // 6. AI服务测试（如果配置了API密钥）
      if (process.env.DEEPSEEK_API_KEY) {
        await this.testAIServices();
      } else {
        console.log('⚠️ 跳过AI服务测试（未配置DEEPSEEK_API_KEY）');
      }
      
      // 7. 用户管理测试
      await this.testUserManager();
      
      // 8. 消息处理器测试
      await this.testMessageProcessor();
      
      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }

  /**
   * 测试配置模块
   */
  async testConfiguration() {
    console.log('📋 测试配置模块...');
    
    try {
      this.config = new AppConfig();
      await this.config.initialize();
      
      // 验证关键配置
      const dbConfig = this.config.getDatabaseConfig();
      const aiConfig = this.config.getAIConfig();
      
      if (!dbConfig.supabaseUrl || !dbConfig.supabaseKey) {
        throw new Error('数据库配置不完整');
      }
      
      this.addTestResult('配置模块', true, '配置加载成功');
      
    } catch (error) {
      this.addTestResult('配置模块', false, error.message);
    }
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection() {
    console.log('🗄️ 测试数据库连接...');
    
    try {
      this.database = new DatabaseManager(this.config.getDatabaseConfig());
      await this.database.connect();
      await this.database.checkHealth();
      
      this.addTestResult('数据库连接', true, '数据库连接成功');
      
    } catch (error) {
      this.addTestResult('数据库连接', false, error.message);
    }
  }

  /**
   * 测试工具函数
   */
  async testUtilities() {
    console.log('🔧 测试工具函数...');
    
    try {
      const utilities = new Utilities();
      
      // 测试字符串处理
      const sanitized = utilities.sanitizeInput('  Hello <script>alert("xss")</script> World!  ');
      if (sanitized !== 'Hello alert("xss") World!') {
        throw new Error('字符串清理功能异常');
      }
      
      // 测试邮箱验证
      if (!utilities.validateEmail('<EMAIL>')) {
        throw new Error('邮箱验证功能异常');
      }
      
      // 测试UUID生成
      const uuid = utilities.generateUUID();
      if (!uuid || uuid.length !== 36) {
        throw new Error('UUID生成功能异常');
      }
      
      this.addTestResult('工具函数', true, '所有工具函数测试通过');
      
    } catch (error) {
      this.addTestResult('工具函数', false, error.message);
    }
  }

  /**
   * 测试验证器
   */
  async testValidators() {
    console.log('✅ 测试验证器...');
    
    try {
      const validators = new Validators();
      
      // 测试消息输入验证
      const validMessage = {
        message: 'Hello',
        userEmail: '<EMAIL>'
      };
      
      const validated = validators.validateMessageInput(validMessage);
      if (!validated || validated.message !== 'Hello') {
        throw new Error('消息验证功能异常');
      }
      
      // 测试邮箱验证
      validators.validateEmail('<EMAIL>');
      
      this.addTestResult('验证器', true, '验证器功能正常');
      
    } catch (error) {
      this.addTestResult('验证器', false, error.message);
    }
  }

  /**
   * 测试映射表
   */
  async testMappingTables() {
    console.log('🗺️ 测试映射表...');
    
    try {
      const mappingTables = new MappingTables();
      
      // 测试意图类型获取
      const intentTypes = mappingTables.getIntentTypes();
      if (!intentTypes || !intentTypes.GREETING) {
        throw new Error('意图类型映射异常');
      }
      
      // 测试技术方向查找
      const techDirection = mappingTables.findTechDirectionByKeyword('javascript');
      if (!techDirection) {
        throw new Error('技术方向映射异常');
      }
      
      // 测试数据库字段映射
      const dbMapping = mappingTables.getDatabaseFieldMapping();
      if (!dbMapping || !dbMapping.users) {
        throw new Error('数据库字段映射异常');
      }
      
      this.addTestResult('映射表', true, '映射表功能正常');
      
    } catch (error) {
      this.addTestResult('映射表', false, error.message);
    }
  }

  /**
   * 测试AI服务
   */
  async testAIServices() {
    console.log('🤖 测试AI服务...');
    
    try {
      const aiServices = new AIServices(this.config.getAIConfig());
      await aiServices.initialize();
      
      // 测试连接（不发送实际请求，只检查初始化）
      if (!aiServices.isReady()) {
        throw new Error('AI服务初始化失败');
      }
      
      // 检查队列状态
      const queueStatus = aiServices.getQueueStatus();
      if (!queueStatus || queueStatus.pending === undefined) {
        throw new Error('AI服务队列功能异常');
      }
      
      this.addTestResult('AI服务', true, 'AI服务初始化成功');
      
    } catch (error) {
      this.addTestResult('AI服务', false, error.message);
    }
  }

  /**
   * 测试用户管理
   */
  async testUserManager() {
    console.log('👤 测试用户管理...');
    
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }
      
      this.userManager = new UserManager(this.database, this.config);
      await this.userManager.initialize();
      
      if (!this.userManager.isReady()) {
        throw new Error('用户管理器初始化失败');
      }
      
      this.addTestResult('用户管理', true, '用户管理器初始化成功');
      
    } catch (error) {
      this.addTestResult('用户管理', false, error.message);
    }
  }

  /**
   * 测试消息处理器
   */
  async testMessageProcessor() {
    console.log('🧠 测试消息处理器...');
    
    try {
      if (!this.database || !this.userManager) {
        throw new Error('依赖模块未初始化');
      }
      
      this.messageProcessor = new MessageProcessor({
        database: this.database,
        userManager: this.userManager,
        config: this.config
      });
      
      // 注意：这里不调用initialize()，因为它会尝试初始化AI服务
      // 只测试基本的意图识别功能
      const intent = this.messageProcessor.identifyIntentByRules('你好');
      
      if (!intent || intent.type !== 'greeting') {
        throw new Error('意图识别功能异常');
      }
      
      this.addTestResult('消息处理器', true, '消息处理器基础功能正常');
      
    } catch (error) {
      this.addTestResult('消息处理器', false, error.message);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success: success,
      message: message
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`));
    }
    
    console.log('\n' + '='.repeat(50));
    
    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！架构基础功能正常。');
    } else {
      console.log('⚠️ 部分测试失败，请检查配置和依赖。');
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.database) {
      await this.database.disconnect();
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new BasicFunctionalityTest();
  
  test.runAllTests()
    .then(() => test.cleanup())
    .catch(error => {
      console.error('❌ 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = BasicFunctionalityTest;
