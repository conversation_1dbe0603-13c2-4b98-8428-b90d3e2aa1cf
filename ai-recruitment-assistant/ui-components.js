/**
 * AI招聘助手系统 - UI组件库
 * 
 * 核心职责：
 * - 可复用 UI 组件
 * - 界面样式管理
 * - 交互效果实现
 * - 响应式设计
 * 
 * 预计代码量：1200行
 */

class UIComponents {
  constructor() {
    this.components = {};
    this.themes = {
      light: {
        primary: '#667eea',
        secondary: '#764ba2',
        background: '#ffffff',
        surface: '#f8f9fa',
        text: '#333333',
        textSecondary: '#5f6368',
        border: '#e1e5e9',
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336'
      },
      dark: {
        primary: '#667eea',
        secondary: '#764ba2',
        background: '#1a1a1a',
        surface: '#2d2d2d',
        text: '#ffffff',
        textSecondary: '#b0b0b0',
        border: '#404040',
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336'
      }
    };
    this.currentTheme = 'light';
  }

  /**
   * 创建按钮组件
   */
  createButton(options = {}) {
    const {
      text = '按钮',
      type = 'primary',
      size = 'medium',
      disabled = false,
      icon = null,
      onClick = null,
      className = ''
    } = options;

    const button = document.createElement('button');
    button.className = `ui-button ui-button--${type} ui-button--${size} ${className}`;
    button.disabled = disabled;

    if (icon) {
      button.innerHTML = `<span class="ui-button__icon">${icon}</span><span class="ui-button__text">${text}</span>`;
    } else {
      button.textContent = text;
    }

    if (onClick) {
      button.addEventListener('click', onClick);
    }

    return button;
  }

  /**
   * 创建输入框组件
   */
  createInput(options = {}) {
    const {
      type = 'text',
      placeholder = '',
      value = '',
      label = null,
      required = false,
      disabled = false,
      maxLength = null,
      className = ''
    } = options;

    const container = document.createElement('div');
    container.className = `ui-input-container ${className}`;

    if (label) {
      const labelElement = document.createElement('label');
      labelElement.className = 'ui-input__label';
      labelElement.textContent = label + (required ? ' *' : '');
      container.appendChild(labelElement);
    }

    const input = document.createElement('input');
    input.type = type;
    input.className = 'ui-input';
    input.placeholder = placeholder;
    input.value = value;
    input.disabled = disabled;
    
    if (required) input.required = true;
    if (maxLength) input.maxLength = maxLength;

    container.appendChild(input);

    return { container, input };
  }

  /**
   * 创建文本域组件
   */
  createTextarea(options = {}) {
    const {
      placeholder = '',
      value = '',
      label = null,
      required = false,
      disabled = false,
      rows = 4,
      maxLength = null,
      className = ''
    } = options;

    const container = document.createElement('div');
    container.className = `ui-textarea-container ${className}`;

    if (label) {
      const labelElement = document.createElement('label');
      labelElement.className = 'ui-textarea__label';
      labelElement.textContent = label + (required ? ' *' : '');
      container.appendChild(labelElement);
    }

    const textarea = document.createElement('textarea');
    textarea.className = 'ui-textarea';
    textarea.placeholder = placeholder;
    textarea.value = value;
    textarea.disabled = disabled;
    textarea.rows = rows;
    
    if (required) textarea.required = true;
    if (maxLength) textarea.maxLength = maxLength;

    container.appendChild(textarea);

    return { container, textarea };
  }

  /**
   * 创建选择框组件
   */
  createSelect(options = {}) {
    const {
      options: selectOptions = [],
      value = '',
      label = null,
      required = false,
      disabled = false,
      placeholder = '请选择...',
      className = ''
    } = options;

    const container = document.createElement('div');
    container.className = `ui-select-container ${className}`;

    if (label) {
      const labelElement = document.createElement('label');
      labelElement.className = 'ui-select__label';
      labelElement.textContent = label + (required ? ' *' : '');
      container.appendChild(labelElement);
    }

    const select = document.createElement('select');
    select.className = 'ui-select';
    select.disabled = disabled;
    
    if (required) select.required = true;

    // 添加占位符选项
    if (placeholder) {
      const placeholderOption = document.createElement('option');
      placeholderOption.value = '';
      placeholderOption.textContent = placeholder;
      placeholderOption.disabled = true;
      placeholderOption.selected = !value;
      select.appendChild(placeholderOption);
    }

    // 添加选项
    selectOptions.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      optionElement.selected = option.value === value;
      select.appendChild(optionElement);
    });

    container.appendChild(select);

    return { container, select };
  }

  /**
   * 创建卡片组件
   */
  createCard(options = {}) {
    const {
      title = null,
      content = '',
      footer = null,
      className = '',
      clickable = false,
      onClick = null
    } = options;

    const card = document.createElement('div');
    card.className = `ui-card ${clickable ? 'ui-card--clickable' : ''} ${className}`;

    if (title) {
      const header = document.createElement('div');
      header.className = 'ui-card__header';
      header.innerHTML = `<h3 class="ui-card__title">${title}</h3>`;
      card.appendChild(header);
    }

    const body = document.createElement('div');
    body.className = 'ui-card__body';
    body.innerHTML = content;
    card.appendChild(body);

    if (footer) {
      const footerElement = document.createElement('div');
      footerElement.className = 'ui-card__footer';
      footerElement.innerHTML = footer;
      card.appendChild(footerElement);
    }

    if (clickable && onClick) {
      card.addEventListener('click', onClick);
    }

    return card;
  }

  /**
   * 创建模态框组件
   */
  createModal(options = {}) {
    const {
      title = '模态框',
      content = '',
      size = 'medium',
      closable = true,
      onClose = null,
      className = ''
    } = options;

    const overlay = document.createElement('div');
    overlay.className = 'ui-modal-overlay';

    const modal = document.createElement('div');
    modal.className = `ui-modal ui-modal--${size} ${className}`;

    const header = document.createElement('div');
    header.className = 'ui-modal__header';
    header.innerHTML = `
      <h3 class="ui-modal__title">${title}</h3>
      ${closable ? '<button class="ui-modal__close">&times;</button>' : ''}
    `;

    const body = document.createElement('div');
    body.className = 'ui-modal__body';
    body.innerHTML = content;

    modal.appendChild(header);
    modal.appendChild(body);
    overlay.appendChild(modal);

    // 关闭功能
    if (closable) {
      const closeButton = header.querySelector('.ui-modal__close');
      const closeModal = () => {
        overlay.remove();
        if (onClose) onClose();
      };

      closeButton.addEventListener('click', closeModal);
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) closeModal();
      });
    }

    return { overlay, modal, body };
  }

  /**
   * 创建通知组件
   */
  createNotification(options = {}) {
    const {
      message = '',
      type = 'info',
      duration = 3000,
      closable = true,
      position = 'top-right'
    } = options;

    const notification = document.createElement('div');
    notification.className = `ui-notification ui-notification--${type} ui-notification--${position}`;

    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <div class="ui-notification__icon">${icons[type] || icons.info}</div>
      <div class="ui-notification__message">${message}</div>
      ${closable ? '<button class="ui-notification__close">&times;</button>' : ''}
    `;

    // 添加到页面
    let container = document.querySelector('.ui-notifications-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'ui-notifications-container';
      document.body.appendChild(container);
    }
    container.appendChild(notification);

    // 关闭功能
    const closeNotification = () => {
      notification.style.animation = 'slideOutRight 0.3s ease-in-out';
      setTimeout(() => notification.remove(), 300);
    };

    if (closable) {
      const closeButton = notification.querySelector('.ui-notification__close');
      closeButton.addEventListener('click', closeNotification);
    }

    // 自动关闭
    if (duration > 0) {
      setTimeout(closeNotification, duration);
    }

    return notification;
  }

  /**
   * 创建加载指示器
   */
  createLoader(options = {}) {
    const {
      size = 'medium',
      text = '加载中...',
      overlay = false,
      className = ''
    } = options;

    const loader = document.createElement('div');
    loader.className = `ui-loader ui-loader--${size} ${overlay ? 'ui-loader--overlay' : ''} ${className}`;

    loader.innerHTML = `
      <div class="ui-loader__spinner">
        <div class="ui-loader__dot"></div>
        <div class="ui-loader__dot"></div>
        <div class="ui-loader__dot"></div>
      </div>
      ${text ? `<div class="ui-loader__text">${text}</div>` : ''}
    `;

    return loader;
  }

  /**
   * 创建进度条组件
   */
  createProgressBar(options = {}) {
    const {
      value = 0,
      max = 100,
      showText = true,
      className = ''
    } = options;

    const container = document.createElement('div');
    container.className = `ui-progress-container ${className}`;

    const progressBar = document.createElement('div');
    progressBar.className = 'ui-progress-bar';

    const progressFill = document.createElement('div');
    progressFill.className = 'ui-progress-fill';
    progressFill.style.width = `${(value / max) * 100}%`;

    progressBar.appendChild(progressFill);
    container.appendChild(progressBar);

    if (showText) {
      const progressText = document.createElement('div');
      progressText.className = 'ui-progress-text';
      progressText.textContent = `${Math.round((value / max) * 100)}%`;
      container.appendChild(progressText);
    }

    return { container, progressBar, progressFill };
  }

  /**
   * 创建标签页组件
   */
  createTabs(options = {}) {
    const {
      tabs = [],
      activeTab = 0,
      className = ''
    } = options;

    const container = document.createElement('div');
    container.className = `ui-tabs-container ${className}`;

    const tabsHeader = document.createElement('div');
    tabsHeader.className = 'ui-tabs-header';

    const tabsContent = document.createElement('div');
    tabsContent.className = 'ui-tabs-content';

    tabs.forEach((tab, index) => {
      // 创建标签头
      const tabButton = document.createElement('button');
      tabButton.className = `ui-tab-button ${index === activeTab ? 'ui-tab-button--active' : ''}`;
      tabButton.textContent = tab.title;
      tabButton.addEventListener('click', () => this.switchTab(index, tabsHeader, tabsContent));
      tabsHeader.appendChild(tabButton);

      // 创建标签内容
      const tabPanel = document.createElement('div');
      tabPanel.className = `ui-tab-panel ${index === activeTab ? 'ui-tab-panel--active' : ''}`;
      tabPanel.innerHTML = tab.content;
      tabsContent.appendChild(tabPanel);
    });

    container.appendChild(tabsHeader);
    container.appendChild(tabsContent);

    return container;
  }

  /**
   * 切换标签页
   */
  switchTab(activeIndex, tabsHeader, tabsContent) {
    // 更新标签头状态
    const tabButtons = tabsHeader.querySelectorAll('.ui-tab-button');
    tabButtons.forEach((button, index) => {
      button.classList.toggle('ui-tab-button--active', index === activeIndex);
    });

    // 更新标签内容状态
    const tabPanels = tabsContent.querySelectorAll('.ui-tab-panel');
    tabPanels.forEach((panel, index) => {
      panel.classList.toggle('ui-tab-panel--active', index === activeIndex);
    });
  }

  /**
   * 应用主题
   */
  applyTheme(themeName) {
    if (!this.themes[themeName]) return;

    this.currentTheme = themeName;
    const theme = this.themes[themeName];

    // 设置CSS变量
    const root = document.documentElement;
    Object.entries(theme).forEach(([key, value]) => {
      root.style.setProperty(`--ui-${key}`, value);
    });

    // 添加主题类
    document.body.className = document.body.className.replace(/ui-theme-\w+/g, '');
    document.body.classList.add(`ui-theme-${themeName}`);
  }

  /**
   * 初始化组件样式
   */
  initStyles() {
    if (document.getElementById('uiComponentsStyles')) return;

    const styles = `
      <style id="uiComponentsStyles">
        :root {
          --ui-primary: #667eea;
          --ui-secondary: #764ba2;
          --ui-background: #ffffff;
          --ui-surface: #f8f9fa;
          --ui-text: #333333;
          --ui-text-secondary: #5f6368;
          --ui-border: #e1e5e9;
          --ui-success: #4caf50;
          --ui-warning: #ff9800;
          --ui-error: #f44336;
        }

        /* 按钮样式 */
        .ui-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          text-decoration: none;
        }

        .ui-button--primary {
          background: linear-gradient(135deg, var(--ui-primary) 0%, var(--ui-secondary) 100%);
          color: white;
        }

        .ui-button--secondary {
          background: var(--ui-surface);
          color: var(--ui-text);
          border: 1px solid var(--ui-border);
        }

        .ui-button--small {
          padding: 4px 8px;
          font-size: 12px;
        }

        .ui-button--large {
          padding: 12px 24px;
          font-size: 16px;
        }

        .ui-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .ui-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        /* 输入框样式 */
        .ui-input-container,
        .ui-textarea-container,
        .ui-select-container {
          margin-bottom: 16px;
        }

        .ui-input__label,
        .ui-textarea__label,
        .ui-select__label {
          display: block;
          margin-bottom: 4px;
          font-size: 14px;
          font-weight: 500;
          color: var(--ui-text);
        }

        .ui-input,
        .ui-textarea,
        .ui-select {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid var(--ui-border);
          border-radius: 4px;
          font-size: 14px;
          transition: border-color 0.2s;
        }

        .ui-input:focus,
        .ui-textarea:focus,
        .ui-select:focus {
          outline: none;
          border-color: var(--ui-primary);
        }

        /* 卡片样式 */
        .ui-card {
          background: var(--ui-background);
          border: 1px solid var(--ui-border);
          border-radius: 8px;
          overflow: hidden;
          transition: box-shadow 0.2s;
        }

        .ui-card--clickable {
          cursor: pointer;
        }

        .ui-card--clickable:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ui-card__header {
          padding: 16px;
          border-bottom: 1px solid var(--ui-border);
        }

        .ui-card__title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .ui-card__body {
          padding: 16px;
        }

        .ui-card__footer {
          padding: 16px;
          border-top: 1px solid var(--ui-border);
          background: var(--ui-surface);
        }

        /* 模态框样式 */
        .ui-modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .ui-modal {
          background: var(--ui-background);
          border-radius: 8px;
          max-height: 90vh;
          overflow-y: auto;
          animation: modalSlideIn 0.3s ease-out;
        }

        .ui-modal--small { width: 400px; }
        .ui-modal--medium { width: 600px; }
        .ui-modal--large { width: 800px; }

        .ui-modal__header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          border-bottom: 1px solid var(--ui-border);
        }

        .ui-modal__title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .ui-modal__close {
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
          color: var(--ui-text-secondary);
        }

        .ui-modal__body {
          padding: 16px;
        }

        /* 通知样式 */
        .ui-notifications-container {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1100;
        }

        .ui-notification {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          margin-bottom: 8px;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          animation: slideInRight 0.3s ease-out;
          max-width: 400px;
        }

        .ui-notification--success { background: var(--ui-success); color: white; }
        .ui-notification--error { background: var(--ui-error); color: white; }
        .ui-notification--warning { background: var(--ui-warning); color: white; }
        .ui-notification--info { background: var(--ui-primary); color: white; }

        .ui-notification__icon {
          margin-right: 8px;
          font-size: 16px;
        }

        .ui-notification__message {
          flex: 1;
        }

        .ui-notification__close {
          background: none;
          border: none;
          color: inherit;
          font-size: 18px;
          cursor: pointer;
          margin-left: 8px;
        }

        /* 动画 */
        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-50px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(100%);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes slideOutRight {
          from {
            opacity: 1;
            transform: translateX(0);
          }
          to {
            opacity: 0;
            transform: translateX(100%);
          }
        }
      </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
  }

  /**
   * 初始化组件库
   */
  init() {
    this.initStyles();
    this.applyTheme(this.currentTheme);
  }
}

// 如果在浏览器环境中，将类添加到全局对象
if (typeof window !== 'undefined') {
  window.UIComponents = UIComponents;
}

module.exports = UIComponents;
