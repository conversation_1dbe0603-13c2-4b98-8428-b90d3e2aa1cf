<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI招聘助手 - 第一句回复测试</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .chat-container {
        width: 90%;
        max-width: 800px;
        height: 600px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .chat-header {
        background: #4f46e5;
        color: white;
        padding: 20px;
        text-align: center;
      }

      .chat-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
      }

      .chat-header p {
        opacity: 0.9;
        font-size: 14px;
      }

      .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f8fafc;
      }

      .message {
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
      }

      .message.user {
        justify-content: flex-end;
      }

      .message.bot {
        justify-content: flex-start;
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
        white-space: pre-wrap;
      }

      .message.user .message-content {
        background: #4f46e5;
        color: white;
      }

      .message.bot .message-content {
        background: white;
        color: #1f2937;
        border: 1px solid #e5e7eb;
      }

      .suggestions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
      }

      .suggestion-btn {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .suggestion-btn:hover {
        background: #e5e7eb;
      }

      .chat-input {
        padding: 20px;
        background: white;
        border-top: 1px solid #e5e7eb;
      }

      .input-container {
        display: flex;
        gap: 10px;
      }

      .message-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #d1d5db;
        border-radius: 25px;
        outline: none;
        font-size: 16px;
      }

      .message-input:focus {
        border-color: #4f46e5;
      }

      .send-btn {
        background: #4f46e5;
        color: white;
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
      }

      .send-btn:hover {
        background: #4338ca;
      }

      .send-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .status {
        text-align: center;
        padding: 10px;
        font-size: 14px;
        color: #6b7280;
      }

      .status.error {
        color: #ef4444;
      }

      .status.success {
        color: #10b981;
      }

      .test-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .test-btn {
        background: #8b5cf6;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s;
      }

      .test-btn:hover {
        background: #7c3aed;
      }
    </style>
  </head>
  <body>
    <div class="chat-container">
      <div class="chat-header">
        <h1>AI招聘助手</h1>
        <p>测试第一句回复修复 - 直接点击下方按钮或输入消息测试</p>
      </div>

      <div class="chat-messages" id="chatMessages">
        <div class="status" id="status">连接中...</div>

        <div class="test-buttons">
          <button class="test-btn" onclick="testMessage('你好')">
            测试: 你好
          </button>
          <button class="test-btn" onclick="testMessage('hello')">
            测试: hello
          </button>
          <button class="test-btn" onclick="testMessage('你是谁')">
            测试: 你是谁
          </button>
          <button class="test-btn" onclick="testMessage('有什么职位')">
            测试: 有什么职位
          </button>
          <button class="test-btn" onclick="testMessage('招聘')">
            测试: 招聘
          </button>
          <button class="test-btn" onclick="testMessage('随便说点什么')">
            测试: 其他消息
          </button>
          <button class="test-btn" onclick="clearChat()">清空对话</button>
        </div>
      </div>

      <div class="chat-input">
        <div class="input-container">
          <input
            type="text"
            class="message-input"
            id="messageInput"
            placeholder="输入消息测试第一句回复..."
            onkeypress="handleKeyPress(event)"
          />
          <button class="send-btn" id="sendBtn" onclick="sendMessage()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <script>
      let sessionId = null;
      let messageCount = 0;

      // 页面加载时初始化
      window.onload = function () {
        checkServerStatus();
        showWelcomeMessage();
      };

      // 显示欢迎消息
      function showWelcomeMessage() {
        const welcomeMessage = `您好，我是AI领域的猎头Katrina，专注于AI算法职位。

聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。

同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`;

        addMessage(welcomeMessage, "bot", [
          "开始对话",
          "我想了解职位",
          "上传简历",
        ]);
      }

      // 检查服务器状态
      async function checkServerStatus() {
        try {
          const response = await fetch("http://localhost:3000/api/health");
          if (response.ok) {
            updateStatus("服务器连接成功！可以开始测试", "success");
          } else {
            updateStatus("服务器连接失败，请确保后端服务已启动", "error");
          }
        } catch (error) {
          updateStatus(
            "无法连接到服务器 (http://localhost:3000)，请启动后端服务",
            "error"
          );
        }
      }

      // 更新状态
      function updateStatus(message, type = "") {
        const status = document.getElementById("status");
        status.textContent = message;
        status.className = `status ${type}`;
      }

      // 测试特定消息
      async function testMessage(message) {
        document.getElementById("messageInput").value = message;
        await sendMessage();
      }

      // 发送消息
      async function sendMessage() {
        const input = document.getElementById("messageInput");
        const message = input.value.trim();

        if (!message) return;

        // 显示用户消息
        addMessage(message, "user");
        input.value = "";

        // 禁用发送按钮
        const sendBtn = document.getElementById("sendBtn");
        sendBtn.disabled = true;

        try {
          messageCount++;

          // 构造请求数据
          const requestData = {
            message: message,
            userEmail: "<EMAIL>",
            sessionId: sessionId,
            isFirstMessage: messageCount === 1,
          };

          updateStatus("发送中...", "");

          // 发送到后端
          const response = await fetch("http://localhost:3000/api/chat", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestData),
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          if (data.success) {
            // 保存会话ID
            if (data.sessionId) {
              sessionId = data.sessionId;
            }

            // 显示AI回复
            addMessage(data.response.content, "bot", data.response.suggestions);

            // 显示调试信息
            const debugInfo = `
回复类型: ${data.response.type}
是否第一句: ${data.response.metadata?.isFirstResponse || false}
消息来源: ${data.response.metadata?.responseSource || "unknown"}
会话ID: ${sessionId}`;

            updateStatus(`✅ 发送成功 | ${debugInfo}`, "success");
          } else {
            addMessage(`错误: ${data.error || "未知错误"}`, "bot");
            updateStatus(`❌ 发送失败: ${data.error}`, "error");
          }
        } catch (error) {
          console.error("发送消息失败:", error);
          addMessage(`连接错误: ${error.message}`, "bot");
          updateStatus(`❌ 连接失败: ${error.message}`, "error");
        } finally {
          sendBtn.disabled = false;
        }
      }

      // 添加消息到聊天界面
      function addMessage(content, sender, suggestions = []) {
        const messagesContainer = document.getElementById("chatMessages");

        const messageDiv = document.createElement("div");
        messageDiv.className = `message ${sender}`;

        const contentDiv = document.createElement("div");
        contentDiv.className = "message-content";
        contentDiv.textContent = content;

        messageDiv.appendChild(contentDiv);

        // 添加建议按钮
        if (suggestions && suggestions.length > 0) {
          const suggestionsDiv = document.createElement("div");
          suggestionsDiv.className = "suggestions";

          suggestions.forEach((suggestion) => {
            const btn = document.createElement("button");
            btn.className = "suggestion-btn";
            btn.textContent = suggestion;
            btn.onclick = () => testMessage(suggestion);
            suggestionsDiv.appendChild(btn);
          });

          messageDiv.appendChild(suggestionsDiv);
        }

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      // 清空对话
      function clearChat() {
        const messagesContainer = document.getElementById("chatMessages");
        messagesContainer.innerHTML = `
                <div class="status" id="status">对话已清空，可以重新测试</div>
                <div class="test-buttons">
                    <button class="test-btn" onclick="testMessage('你好')">测试: 你好</button>
                    <button class="test-btn" onclick="testMessage('hello')">测试: hello</button>
                    <button class="test-btn" onclick="testMessage('你是谁')">测试: 你是谁</button>
                    <button class="test-btn" onclick="testMessage('有什么职位')">测试: 有什么职位</button>
                    <button class="test-btn" onclick="testMessage('招聘')">测试: 招聘</button>
                    <button class="test-btn" onclick="testMessage('随便说点什么')">测试: 其他消息</button>
                    <button class="test-btn" onclick="clearChat()">清空对话</button>
                </div>
            `;
        sessionId = null;
        messageCount = 0;
      }

      // 处理回车键
      function handleKeyPress(event) {
        if (event.key === "Enter") {
          sendMessage();
        }
      }
    </script>
  </body>
</html>
