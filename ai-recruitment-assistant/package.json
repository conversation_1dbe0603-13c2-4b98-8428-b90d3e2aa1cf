{"name": "ai-recruitment-assistant", "version": "1.0.0", "description": "AI招聘助手系统 - 15文件架构方案", "main": "index.js", "scripts": {"start": "node core/index.js", "dev": "nodemon core/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "setup": "cp .env.example .env && echo '请编辑 .env 文件配置您的环境变量'"}, "keywords": ["ai", "recruitment", "chatbot", "deepseek", "supabase"], "author": "AI Recruitment Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.0", "axios": "^1.5.0", "joi": "^17.9.2", "winston": "^3.10.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "eslint": "^8.47.0", "prettier": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}