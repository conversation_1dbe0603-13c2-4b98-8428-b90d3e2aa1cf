// simple-test-server.js - 简单的测试服务器
const express = require("express");
const cors = require("cors");
const path = require("path");

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, "test-website")));

// 模拟会话存储
const sessions = new Map();
let sessionCounter = 0;

// 健康检查接口
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    message: "AI招聘助手测试服务器运行中",
    timestamp: new Date().toISOString(),
  });
});

// 聊天接口
app.post("/api/chat", async (req, res) => {
  try {
    const { message, userEmail, sessionId, isFirstMessage } = req.body;

    console.log(
      `📨 收到消息: "${message}" | 会话ID: ${sessionId} | 是否第一句: ${isFirstMessage}`
    );

    // 获取或创建会话
    let currentSessionId = sessionId;
    if (!currentSessionId) {
      sessionCounter++;
      currentSessionId = `test-session-${sessionCounter}`;
      sessions.set(currentSessionId, {
        id: currentSessionId,
        userEmail: userEmail,
        messageCount: 0,
        createdAt: new Date(),
      });
      console.log(`🆕 创建新会话: ${currentSessionId}`);
    }

    const session = sessions.get(currentSessionId);
    if (session) {
      session.messageCount++;
    }

    // 判断是否为第一句用户消息
    const isActuallyFirst = !session || session.messageCount === 1;

    console.log(
      `🔍 消息分析: 会话消息数=${session?.messageCount || 0}, 实际是第一句=${isActuallyFirst}`
    );

    // 模拟第一句回复逻辑
    const response = generateFirstReply(message, isActuallyFirst);

    console.log(`✅ 生成回复: ${response.type}`);

    res.json({
      success: true,
      sessionId: currentSessionId,
      response: response,
      debug: {
        messageCount: session?.messageCount || 0,
        isFirstMessage: isActuallyFirst,
        originalMessage: message,
      },
    });
  } catch (error) {
    console.error("❌ 处理聊天请求失败:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// 生成第一句回复
function generateFirstReply(message, isFirstMessage) {
  if (!isFirstMessage) {
    return {
      type: "normal_reply",
      content: "这不是第一句消息，正常回复逻辑...",
      metadata: {
        isFirstResponse: false,
        responseSource: "normal_conversation",
      },
      suggestions: ["继续对话", "了解更多"],
    };
  }

  // 第一句消息的处理逻辑
  const category = getMessageCategory(message);

  // 统一的完整开场白
  const fullGreeting =
    "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。";

  const suggestions = [
    "我是前端开发工程师",
    "我做后端开发",
    "我是全栈工程师",
    "我想上传简历",
  ];

  switch (category) {
    case "greeting":
    case "identity_check":
      return {
        type: "first_greeting_full",
        content: fullGreeting,
        metadata: {
          isFirstResponse: true,
          responseSource: "first_user_greeting",
          messageCategory: category,
          followUpMessage:
            "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？",
        },
        suggestions: suggestions,
      };

    case "job_inquiry":
      return {
        type: "first_job_inquiry",
        content: fullGreeting,
        metadata: {
          isFirstResponse: true,
          responseSource: "first_job_inquiry",
          messageCategory: category,
          followUpMessage:
            "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？",
        },
        suggestions: suggestions,
      };

    default:
      return {
        type: "first_greeting_default",
        content: fullGreeting,
        metadata: {
          isFirstResponse: true,
          responseSource: "default_first_message",
          messageCategory: "other",
          followUpMessage:
            "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？",
        },
        suggestions: suggestions,
      };
  }
}

// 获取消息类别
function getMessageCategory(message) {
  const lowerMessage = message.toLowerCase().trim();

  // 问候类关键词
  const greetingKeywords = ["你好", "hi", "hello", "哈喽", "在的", "在吗"];
  if (greetingKeywords.some((keyword) => lowerMessage.includes(keyword))) {
    return "greeting";
  }

  // 职位询问类关键词
  const jobKeywords = [
    "职位",
    "工作",
    "招聘",
    "岗位",
    "机会",
    "有什么",
    "推荐",
  ];
  if (jobKeywords.some((keyword) => lowerMessage.includes(keyword))) {
    return "job_inquiry";
  }

  // 身份确认类关键词
  const identityKeywords = ["你是谁", "ai", "机器人", "真人", "人工"];
  if (identityKeywords.some((keyword) => lowerMessage.includes(keyword))) {
    return "identity_check";
  }

  return "other";
}

// 获取会话列表接口（调试用）
app.get("/api/sessions", (req, res) => {
  const sessionList = Array.from(sessions.values()).map((session) => ({
    id: session.id,
    userEmail: session.userEmail,
    messageCount: session.messageCount,
    createdAt: session.createdAt,
  }));

  res.json({
    success: true,
    sessions: sessionList,
    totalSessions: sessions.size,
  });
});

// 清空会话接口（调试用）
app.delete("/api/sessions", (req, res) => {
  sessions.clear();
  sessionCounter = 0;
  res.json({
    success: true,
    message: "所有会话已清空",
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 AI招聘助手测试服务器启动成功！`);
  console.log(`📱 测试网站: http://localhost:${PORT}`);
  console.log(`🔧 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`💬 聊天接口: http://localhost:${PORT}/api/chat`);
  console.log(`📊 会话列表: http://localhost:${PORT}/api/sessions`);
  console.log(`\n🧪 测试说明:`);
  console.log(`1. 打开浏览器访问 http://localhost:${PORT}`);
  console.log(`2. 点击测试按钮或输入消息测试第一句回复`);
  console.log(`3. 检查回复是否以"我们合作的公司挺多的..."开头`);
  console.log(`4. 检查是否包含简历上传和邮箱说明`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on("SIGINT", () => {
  console.log("\n🛑 正在关闭服务器...");
  process.exit(0);
});
