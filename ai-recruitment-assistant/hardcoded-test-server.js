// hardcoded-test-server.js - 使用硬编码回复系统的测试服务器
// 简单、直接、可靠

const express = require('express');
const cors = require('cors');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const SimpleMessageProcessor = require('./core/回复引擎/simple-message-processor');

const app = express();
const PORT = 8080;

// 初始化消息处理器
const messageProcessor = new SimpleMessageProcessor();

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 主页
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI招聘助手 - 硬编码回复测试</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
            button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
            button:hover { background: #0056b3; }
            input { padding: 8px; margin: 5px; width: 300px; border: 1px solid #ddd; border-radius: 3px; }
            .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
            .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🤖 AI招聘助手 - 硬编码回复测试</h1>
        
        <div class="test-section">
            <h2>📋 预设测试场景</h2>
            <button onclick="testMessage('你好')">测试: 你好</button>
            <button onclick="testMessage('有什么职位')">测试: 有什么职位</button>
            <button onclick="testMessage('你是谁')">测试: 你是谁</button>
            <button onclick="testMessage('薪资多少')">测试: 薪资多少</button>
            <button onclick="testMessage('技术栈')">测试: 技术栈</button>
        </div>

        <div class="test-section">
            <h2>✏️ 自定义测试</h2>
            <input type="text" id="customMessage" placeholder="输入测试消息..." />
            <button onclick="testCustomMessage()">发送测试</button>
        </div>

        <div class="test-section">
            <h2>📊 系统统计</h2>
            <button onclick="getStats()">获取统计信息</button>
        </div>

        <div id="results"></div>

        <script>
            async function testMessage(message) {
                const sessionId = 'test-' + Date.now();
                const userEmail = '<EMAIL>';
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message, sessionId, userEmail })
                    });
                    
                    const result = await response.json();
                    displayResult(message, result);
                } catch (error) {
                    displayError('测试失败: ' + error.message);
                }
            }

            async function testCustomMessage() {
                const message = document.getElementById('customMessage').value;
                if (!message.trim()) {
                    alert('请输入测试消息');
                    return;
                }
                await testMessage(message);
                document.getElementById('customMessage').value = '';
            }

            async function getStats() {
                try {
                    const response = await fetch('/api/stats');
                    const stats = await response.json();
                    displayStats(stats);
                } catch (error) {
                    displayError('获取统计失败: ' + error.message);
                }
            }

            function displayResult(message, result) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
                
                resultDiv.innerHTML = \`
                    <h3>测试消息: "\${message}"</h3>
                    <p><strong>回复类型:</strong> \${result.response?.type || 'N/A'}</p>
                    <p><strong>回复内容:</strong> \${result.response?.content || 'N/A'}</p>
                    <p><strong>处理方式:</strong> \${result.debug?.processingMethod || 'N/A'}</p>
                    <p><strong>是否第一句:</strong> \${result.debug?.isFirstMessage || 'N/A'}</p>
                    \${result.response?.suggestions ? '<p><strong>建议选项:</strong> ' + result.response.suggestions.join(', ') + '</p>' : ''}
                    <details>
                        <summary>完整响应数据</summary>
                        <pre>\${JSON.stringify(result, null, 2)}</pre>
                    </details>
                \`;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            function displayStats(stats) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                
                resultDiv.innerHTML = \`
                    <h3>📊 系统统计信息</h3>
                    <p><strong>总会话数:</strong> \${stats.totalSessions}</p>
                    <p><strong>活跃会话数:</strong> \${stats.activeSessions}</p>
                    <p><strong>硬编码规则数:</strong> \${stats.hardcodedRules.totalRules}</p>
                    <p><strong>关键词总数:</strong> \${stats.hardcodedRules.totalKeywords}</p>
                    <p><strong>规则类别:</strong> \${stats.hardcodedRules.categories.join(', ')}</p>
                    <details>
                        <summary>完整统计数据</summary>
                        <pre>\${JSON.stringify(stats, null, 2)}</pre>
                    </details>
                \`;
                
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            function displayError(message) {
                const resultsDiv = document.getElementById('results');
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = \`<h3>❌ 错误</h3><p>\${message}</p>\`;
                resultsDiv.insertBefore(resultDiv, resultsDiv.firstChild);
            }

            // 回车键发送
            document.getElementById('customMessage').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    testCustomMessage();
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 聊天接口
app.post('/api/chat', async (req, res) => {
  try {
    const { message, sessionId, userEmail } = req.body;
    
    // 验证输入
    if (!message || !message.trim()) {
      return res.status(400).json({
        success: false,
        error: '消息不能为空'
      });
    }

    // 生成sessionId（如果没有提供）
    const finalSessionId = sessionId || uuidv4();
    const finalUserEmail = userEmail || '<EMAIL>';

    console.log(`\n📨 收到聊天请求:`);
    console.log(`   消息: "${message}"`);
    console.log(`   会话ID: ${finalSessionId}`);
    console.log(`   用户邮箱: ${finalUserEmail}`);

    // 处理消息
    const result = await messageProcessor.processMessage({
      message: message.trim(),
      sessionId: finalSessionId,
      userEmail: finalUserEmail
    });

    console.log(`✅ 处理完成，回复类型: ${result.response?.type}`);
    
    res.json(result);

  } catch (error) {
    console.error('❌ 聊天接口错误:', error);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      message: error.message
    });
  }
});

// 统计接口
app.get('/api/stats', (req, res) => {
  try {
    const stats = messageProcessor.getStats();
    res.json(stats);
  } catch (error) {
    console.error('❌ 统计接口错误:', error);
    res.status(500).json({
      success: false,
      error: '获取统计信息失败'
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`\n🚀 硬编码回复测试服务器启动成功！`);
  console.log(`📱 测试网站: http://localhost:${PORT}`);
  console.log(`💬 聊天接口: http://localhost:${PORT}/api/chat`);
  console.log(`📊 统计接口: http://localhost:${PORT}/api/stats`);
  console.log(`🔧 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`\n🧪 测试说明:`);
  console.log(`1. 打开浏览器访问 http://localhost:${PORT}`);
  console.log(`2. 点击预设测试按钮或输入自定义消息`);
  console.log(`3. 查看不同消息的回复类型和内容`);
  console.log(`4. 检查硬编码规则是否正确匹配`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

module.exports = app;
