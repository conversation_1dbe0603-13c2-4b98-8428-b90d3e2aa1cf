/**
 * AI招聘助手系统 - 数据验证器
 * 
 * 核心职责：
 * - 输入数据验证
 * - 数据格式检查
 * - 安全性验证
 * - 错误信息生成
 * 
 * 预计代码量：600行
 */

const Joi = require('joi');

class Validators {
  constructor() {
    this.setupSchemas();
  }

  /**
   * 设置验证模式
   */
  setupSchemas() {
    // 消息输入验证模式
    this.messageInputSchema = Joi.object({
      message: Joi.string().min(1).max(2000).required(),
      sessionId: Joi.string().uuid().optional(),
      userEmail: Joi.string().email().required(),
      entrySource: Joi.string().uri().optional()
    });

    // 用户档案验证模式
    this.userProfileSchema = Joi.object({
      currentCompanyNameRaw: Joi.string().max(100).optional(),
      candidateLevelRaw: Joi.string().max(50).optional(),
      candidateTechDirectionRaw: Joi.string().max(100).optional(),
      candidateBusinessScenarioRaw: Joi.string().max(100).optional(),
      expectedCompensationMin: Joi.number().min(0).max(1000000).optional(),
      expectedCompensationMax: Joi.number().min(0).max(1000000).optional(),
      workLocation: Joi.string().max(100).optional(),
      experienceYears: Joi.number().min(0).max(50).optional(),
      skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
      education: Joi.string().max(200).optional(),
      profileCompletenessScore: Joi.number().min(0).max(100).optional()
    });

    // 职位搜索验证模式
    this.jobSearchSchema = Joi.object({
      techDirection: Joi.string().max(100).optional(),
      location: Joi.string().max(100).optional(),
      salaryMin: Joi.number().min(0).max(1000000).optional(),
      salaryMax: Joi.number().min(0).max(1000000).optional(),
      companyType: Joi.string().valid('startup', 'bigtech', 'traditional', 'foreign').optional(),
      experienceLevel: Joi.string().max(50).optional(),
      limit: Joi.number().min(1).max(100).default(20),
      offset: Joi.number().min(0).default(0)
    });

    // 会话创建验证模式
    this.sessionCreateSchema = Joi.object({
      userEmail: Joi.string().email().required(),
      entrySourceUrl: Joi.string().uri().optional(),
      initialIntent: Joi.string().max(100).optional()
    });
  }

  /**
   * 验证消息输入
   */
  validateMessageInput(data) {
    const { error, value } = this.messageInputSchema.validate(data);
    
    if (error) {
      throw new Error(`消息输入验证失败: ${error.details[0].message}`);
    }
    
    // 额外的安全检查
    this.checkForMaliciousContent(value.message);
    
    return value;
  }

  /**
   * 验证用户档案数据
   */
  validateUserProfile(data) {
    const { error, value } = this.userProfileSchema.validate(data);
    
    if (error) {
      throw new Error(`用户档案验证失败: ${error.details[0].message}`);
    }
    
    // 业务规则验证
    this.validateBusinessRules(value);
    
    return value;
  }

  /**
   * 验证职位搜索参数
   */
  validateJobSearch(data) {
    const { error, value } = this.jobSearchSchema.validate(data);
    
    if (error) {
      throw new Error(`职位搜索参数验证失败: ${error.details[0].message}`);
    }
    
    // 薪资范围验证
    if (value.salaryMin && value.salaryMax && value.salaryMin > value.salaryMax) {
      throw new Error('最低薪资不能大于最高薪资');
    }
    
    return value;
  }

  /**
   * 验证会话创建数据
   */
  validateSessionCreate(data) {
    const { error, value } = this.sessionCreateSchema.validate(data);
    
    if (error) {
      throw new Error(`会话创建验证失败: ${error.details[0].message}`);
    }
    
    return value;
  }

  /**
   * 检查恶意内容
   */
  checkForMaliciousContent(content) {
    // SQL注入检查
    const sqlInjectionPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i
    ];
    
    for (const pattern of sqlInjectionPatterns) {
      if (pattern.test(content)) {
        throw new Error('检测到潜在的SQL注入攻击');
      }
    }
    
    // XSS检查
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi
    ];
    
    for (const pattern of xssPatterns) {
      if (pattern.test(content)) {
        throw new Error('检测到潜在的XSS攻击');
      }
    }
    
    // 检查过长的内容
    if (content.length > 10000) {
      throw new Error('内容长度超出限制');
    }
  }

  /**
   * 验证业务规则
   */
  validateBusinessRules(profileData) {
    // 薪资范围验证
    if (profileData.expectedCompensationMin && profileData.expectedCompensationMax) {
      if (profileData.expectedCompensationMin > profileData.expectedCompensationMax) {
        throw new Error('期望薪资最小值不能大于最大值');
      }
      
      // 薪资范围合理性检查
      const ratio = profileData.expectedCompensationMax / profileData.expectedCompensationMin;
      if (ratio > 3) {
        throw new Error('薪资范围过大，请提供更具体的期望');
      }
    }
    
    // 经验年限合理性检查
    if (profileData.experienceYears !== undefined) {
      if (profileData.experienceYears < 0 || profileData.experienceYears > 50) {
        throw new Error('工作经验年限不合理');
      }
    }
    
    // 技能数量检查
    if (profileData.skills && profileData.skills.length > 20) {
      throw new Error('技能数量过多，请选择最重要的技能');
    }
  }

  /**
   * 验证邮箱格式
   */
  validateEmail(email) {
    const emailSchema = Joi.string().email().required();
    const { error } = emailSchema.validate(email);
    
    if (error) {
      throw new Error('邮箱格式不正确');
    }
    
    // 检查是否为一次性邮箱
    const disposableEmailDomains = [
      '10minutemail.com',
      'tempmail.org',
      'guerrillamail.com'
    ];
    
    const domain = email.split('@')[1];
    if (disposableEmailDomains.includes(domain)) {
      throw new Error('不支持一次性邮箱');
    }
    
    return true;
  }

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneSchema = Joi.string().pattern(/^1[3-9]\d{9}$/).required();
    const { error } = phoneSchema.validate(phone);
    
    if (error) {
      throw new Error('手机号格式不正确');
    }
    
    return true;
  }

  /**
   * 验证UUID格式
   */
  validateUUID(uuid) {
    const uuidSchema = Joi.string().uuid().required();
    const { error } = uuidSchema.validate(uuid);
    
    if (error) {
      throw new Error('UUID格式不正确');
    }
    
    return true;
  }

  /**
   * 验证URL格式
   */
  validateURL(url) {
    const urlSchema = Joi.string().uri().required();
    const { error } = urlSchema.validate(url);
    
    if (error) {
      throw new Error('URL格式不正确');
    }
    
    return true;
  }

  /**
   * 验证日期格式
   */
  validateDate(date) {
    const dateSchema = Joi.date().required();
    const { error } = dateSchema.validate(date);
    
    if (error) {
      throw new Error('日期格式不正确');
    }
    
    return true;
  }

  /**
   * 验证数字范围
   */
  validateNumberRange(value, min, max) {
    const schema = Joi.number().min(min).max(max).required();
    const { error } = schema.validate(value);
    
    if (error) {
      throw new Error(`数值必须在 ${min} 到 ${max} 之间`);
    }
    
    return true;
  }

  /**
   * 验证字符串长度
   */
  validateStringLength(value, minLength, maxLength) {
    const schema = Joi.string().min(minLength).max(maxLength).required();
    const { error } = schema.validate(value);
    
    if (error) {
      throw new Error(`字符串长度必须在 ${minLength} 到 ${maxLength} 之间`);
    }
    
    return true;
  }

  /**
   * 验证数组长度
   */
  validateArrayLength(array, minLength, maxLength) {
    if (!Array.isArray(array)) {
      throw new Error('输入必须是数组');
    }
    
    if (array.length < minLength || array.length > maxLength) {
      throw new Error(`数组长度必须在 ${minLength} 到 ${maxLength} 之间`);
    }
    
    return true;
  }

  /**
   * 清理HTML标签
   */
  sanitizeHTML(html) {
    if (typeof html !== 'string') return '';
    
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * 验证文件类型
   */
  validateFileType(filename, allowedTypes) {
    if (!filename || !allowedTypes || !Array.isArray(allowedTypes)) {
      throw new Error('文件名或允许的文件类型无效');
    }
    
    const extension = filename.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(extension)) {
      throw new Error(`不支持的文件类型。允许的类型: ${allowedTypes.join(', ')}`);
    }
    
    return true;
  }

  /**
   * 验证文件大小
   */
  validateFileSize(fileSize, maxSize) {
    if (fileSize > maxSize) {
      throw new Error(`文件大小超出限制。最大允许: ${maxSize} 字节`);
    }
    
    return true;
  }
}

module.exports = Validators;
