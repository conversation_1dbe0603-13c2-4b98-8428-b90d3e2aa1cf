/**
 * AI招聘助手系统 - 数据库管理器
 * 
 * 核心职责：
 * - Supabase 数据库连接管理
 * - 所有数据 CRUD 操作
 * - 查询优化和缓存
 * - 数据一致性保证
 * 
 * 预计代码量：1800行
 */

const { createClient } = require('@supabase/supabase-js');

class DatabaseManager {
  constructor(config) {
    this.config = config;
    this.client = null;
    this.isConnected = false;
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 建立数据库连接
   */
  async connect() {
    try {
      this.client = createClient(
        this.config.supabaseUrl,
        this.config.supabaseKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          },
          db: {
            schema: 'public'
          },
          global: {
            headers: {
              'x-client-info': 'ai-recruitment-assistant'
            }
          },
          // 高并发优化配置
          realtime: {
            params: {
              eventsPerSecond: 10
            }
          }
        }
      );

      this.isConnected = true;
      console.log('🗄️ Supabase数据库连接成功');

      // 启动连接监控
      this.startConnectionMonitoring();

    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 启动连接监控
   */
  startConnectionMonitoring() {
    // 每30秒检查一次连接状态
    setInterval(async () => {
      try {
        await this.checkHealth();
      } catch (error) {
        console.error('⚠️ 数据库连接监控检测到问题:', error);
        // 可以在这里实现重连逻辑
      }
    }, 30000);
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    if (this.client) {
      this.isConnected = false;
      this.cache.clear();
      console.log('🗄️ 数据库连接已断开');
    }
  }

  /**
   * 健康检查
   */
  async checkHealth() {
    try {
      const { data, error } = await this.client
        .from('users')
        .select('count')
        .limit(1);
      
      if (error) throw error;
      
      console.log('✅ 数据库健康检查通过');
      return true;
      
    } catch (error) {
      console.error('❌ 数据库健康检查失败:', error);
      throw error;
    }
  }

  // ==================== 用户数据操作 ====================

  /**
   * 创建用户
   */
  async createUser(userData) {
    try {
      const { data, error } = await this.client
        .from('users')
        .insert([{
          email: userData.email,
          user_type: userData.userType || 'candidate',
          is_active: true,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      
      console.log('✅ 用户创建成功:', data.id);
      return data;
      
    } catch (error) {
      console.error('❌ 用户创建失败:', error);
      throw error;
    }
  }

  /**
   * 根据邮箱获取用户
   */
  async getUserByEmail(email) {
    try {
      const cacheKey = `user_email_${email}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        this.setCache(cacheKey, data);
      }
      
      return data;
      
    } catch (error) {
      console.error('❌ 获取用户失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户最后登录时间
   */
  async updateUserLastLogin(userId) {
    try {
      const { data, error } = await this.client
        .from('users')
        .update({ last_login_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      
      // 清除相关缓存
      this.clearUserCache(userId);
      
      return data;
      
    } catch (error) {
      console.error('❌ 更新用户登录时间失败:', error);
      throw error;
    }
  }

  // ==================== 会话数据操作 ====================

  /**
   * 创建聊天会话
   */
  async createChatSession(sessionData) {
    try {
      const { data, error } = await this.client
        .from('chat_sessions')
        .insert([{
          user_id: sessionData.userId,
          session_uuid: sessionData.sessionUuid,
          entry_source_url: sessionData.entrySourceUrl || '',
          initial_intent: sessionData.initialIntent || '',
          current_interaction_context: sessionData.context || {},
          created_at: new Date().toISOString(),
          last_active_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      
      console.log('✅ 会话创建成功:', data.id);
      return data;
      
    } catch (error) {
      console.error('❌ 会话创建失败:', error);
      throw error;
    }
  }

  /**
   * 根据UUID获取会话
   */
  async getSessionByUuid(sessionUuid) {
    try {
      const cacheKey = `session_${sessionUuid}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from('chat_sessions')
        .select('*')
        .eq('session_uuid', sessionUuid)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        this.setCache(cacheKey, data);
      }
      
      return data;
      
    } catch (error) {
      console.error('❌ 获取会话失败:', error);
      throw error;
    }
  }

  /**
   * 更新会话活跃时间
   */
  async updateSessionActivity(sessionId, context = null) {
    try {
      const updateData = {
        last_active_at: new Date().toISOString()
      };
      
      if (context) {
        updateData.current_interaction_context = context;
      }

      const { data, error } = await this.client
        .from('chat_sessions')
        .update(updateData)
        .eq('id', sessionId)
        .select()
        .single();

      if (error) throw error;
      
      // 清除相关缓存
      this.clearSessionCache(sessionId);
      
      return data;
      
    } catch (error) {
      console.error('❌ 更新会话活跃时间失败:', error);
      throw error;
    }
  }

  // ==================== 消息数据操作 ====================

  /**
   * 保存聊天消息
   */
  async saveChatMessage(messageData) {
    try {
      const { data, error } = await this.client
        .from('chat_messages')
        .insert([{
          session_id: messageData.sessionId,
          message_type: messageData.messageType,
          message_content: messageData.content,
          metadata_json: messageData.metadata || {},
          timestamp: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      
      return data;
      
    } catch (error) {
      console.error('❌ 保存消息失败:', error);
      throw error;
    }
  }

  /**
   * 获取会话消息历史
   */
  async getSessionMessages(sessionId, limit = 50) {
    try {
      const { data, error } = await this.client
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) throw error;
      
      return data.reverse(); // 返回时间正序
      
    } catch (error) {
      console.error('❌ 获取消息历史失败:', error);
      throw error;
    }
  }

  // ==================== 候选人档案操作 ====================

  /**
   * 创建或更新候选人档案
   */
  async upsertCandidateProfile(userId, profileData) {
    try {
      const { data, error } = await this.client
        .from('candidate_profiles')
        .upsert([{
          user_id: userId,
          ...profileData,
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      
      // 清除相关缓存
      this.clearProfileCache(userId);
      
      console.log('✅ 候选人档案更新成功');
      return data;
      
    } catch (error) {
      console.error('❌ 候选人档案更新失败:', error);
      throw error;
    }
  }

  /**
   * 获取候选人档案
   */
  async getCandidateProfile(userId) {
    try {
      const cacheKey = `profile_${userId}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from('candidate_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        this.setCache(cacheKey, data);
      }
      
      return data;
      
    } catch (error) {
      console.error('❌ 获取候选人档案失败:', error);
      throw error;
    }
  }

  // ==================== 职位数据操作 ====================

  /**
   * 搜索职位
   */
  async searchJobs(criteria = {}) {
    try {
      let query = this.client
        .from('job_listings')
        .select(`
          *,
          companies (
            id,
            company_name,
            company_type,
            industry,
            logo_url
          )
        `)
        .eq('is_active', true);

      // 技术方向筛选
      if (criteria.techDirectionId) {
        query = query.eq('primary_tech_direction_id', criteria.techDirectionId);
      }

      // 业务场景筛选
      if (criteria.businessScenarioId) {
        query = query.eq('primary_business_scenario_id', criteria.businessScenarioId);
      }

      // 薪资范围筛选
      if (criteria.salaryMin) {
        query = query.gte('salary_min', criteria.salaryMin);
      }
      if (criteria.salaryMax) {
        query = query.lte('salary_max', criteria.salaryMax);
      }

      // 经验要求筛选
      if (criteria.experienceMin) {
        query = query.gte('job_standard_level_min', criteria.experienceMin);
      }
      if (criteria.experienceMax) {
        query = query.lte('job_standard_level_max', criteria.experienceMax);
      }

      // 排序和限制
      query = query
        .order('priority_level', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(criteria.limit || 20);

      const { data, error } = await query;

      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('❌ 搜索职位失败:', error);
      throw error;
    }
  }

  /**
   * 获取职位详情
   */
  async getJobDetails(jobId) {
    try {
      const { data, error } = await this.client
        .from('job_listings')
        .select(`
          *,
          companies (
            id,
            company_name,
            company_type,
            industry,
            description,
            website,
            logo_url
          )
        `)
        .eq('id', jobId)
        .single();

      if (error) throw error;

      return data;

    } catch (error) {
      console.error('❌ 获取职位详情失败:', error);
      throw error;
    }
  }

  /**
   * 根据技术方向获取职位
   */
  async getJobsByTechDirection(techDirectionId, limit = 10) {
    try {
      const { data, error } = await this.client
        .from('job_listings')
        .select(`
          *,
          companies (
            company_name,
            company_type,
            logo_url
          )
        `)
        .eq('primary_tech_direction_id', techDirectionId)
        .eq('is_active', true)
        .order('priority_level', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('❌ 根据技术方向获取职位失败:', error);
      throw error;
    }
  }

  // ==================== 技术树数据操作 ====================

  /**
   * 获取技术树数据
   */
  async getTechTree() {
    try {
      const cacheKey = 'tech_tree_all';
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from('tech_tree')
        .select('*')
        .order('level', { ascending: true })
        .order('tech_name', { ascending: true });

      if (error) throw error;

      this.setCache(cacheKey, data);
      return data || [];

    } catch (error) {
      console.error('❌ 获取技术树失败:', error);
      throw error;
    }
  }

  /**
   * 根据关键词搜索技术
   */
  async searchTechByKeyword(keyword) {
    try {
      const { data, error } = await this.client
        .from('tech_tree')
        .select('*')
        .or(`tech_name.ilike.%${keyword}%,keywords.ilike.%${keyword}%`)
        .order('level', { ascending: true })
        .limit(20);

      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('❌ 搜索技术失败:', error);
      throw error;
    }
  }

  // ==================== 业务场景数据操作 ====================

  /**
   * 获取业务场景数据
   */
  async getBusinessScenarios() {
    try {
      const cacheKey = 'business_scenarios_all';
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from('business_scenarios')
        .select('*')
        .order('level', { ascending: true })
        .order('scenario_name', { ascending: true });

      if (error) throw error;

      this.setCache(cacheKey, data);
      return data || [];

    } catch (error) {
      console.error('❌ 获取业务场景失败:', error);
      throw error;
    }
  }

  // ==================== 公司数据操作 ====================

  /**
   * 获取公司列表
   */
  async getCompanies(filters = {}) {
    try {
      let query = this.client
        .from('companies')
        .select('*')
        .eq('is_blocked', false);

      if (filters.companyType) {
        query = query.eq('company_type', filters.companyType);
      }

      if (filters.industry) {
        query = query.eq('industry', filters.industry);
      }

      query = query
        .order('company_name', { ascending: true })
        .limit(filters.limit || 50);

      const { data, error } = await query;

      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('❌ 获取公司列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据名称搜索公司
   */
  async searchCompaniesByName(name) {
    try {
      const { data, error } = await this.client
        .from('companies')
        .select('*')
        .ilike('company_name', `%${name}%`)
        .eq('is_blocked', false)
        .order('company_name', { ascending: true })
        .limit(20);

      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('❌ 搜索公司失败:', error);
      throw error;
    }
  }

  // ==================== 缓存管理 ====================

  /**
   * 设置缓存
   */
  setCache(key, value) {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    });
  }

  /**
   * 获取缓存
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * 清除用户相关缓存
   */
  clearUserCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`user_${userId}`) || key.includes(`profile_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * 清除会话相关缓存
   */
  clearSessionCache(sessionId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`session_${sessionId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * 清除档案相关缓存
   */
  clearProfileCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`profile_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear();
    console.log('🗑️ 所有缓存已清除');
  }
}

module.exports = DatabaseManager;
