/**
 * 读取数据库实际数据（只读操作）
 * 绝对不修改任何数据库内容
 */

require("dotenv").config({ path: "../.env.local" });
const { createClient } = require("@supabase/supabase-js");

async function readDatabaseData() {
  console.log("🔍 开始读取数据库数据...\n");

  // 创建Supabase客户端
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // 1. 读取技术树数据
    console.log("🌳 技术树数据 (tech_tree):");
    const { data: techTree, error: techError } = await supabase
      .from('tech_tree')
      .select('*')
      .order('id');
      
    if (techError) {
      console.error('❌ 读取技术树失败:', techError);
    } else if (techTree && techTree.length > 0) {
      console.log(`  总计: ${techTree.length} 条记录`);
      
      // 按层级分组显示
      const levels = {};
      techTree.forEach(tech => {
        const level = tech.level || 'unknown';
        if (!levels[level]) levels[level] = [];
        levels[level].push(tech);
      });
      
      Object.keys(levels).sort().forEach(level => {
        console.log(`\n  Level ${level} (${levels[level].length}条):`);
        levels[level].slice(0, 10).forEach(tech => {
          console.log(`    - ID:${tech.id} ${tech.tech_name || tech.name || 'N/A'}`);
        });
        if (levels[level].length > 10) {
          console.log(`    ... 还有 ${levels[level].length - 10} 条记录`);
        }
      });
    } else {
      console.log('  📝 tech_tree 表为空');
    }

    // 2. 读取公司数据
    console.log("\n🏢 公司数据 (companies):");
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('*')
      .limit(30);
      
    if (companyError) {
      console.error('❌ 读取公司数据失败:', companyError);
    } else if (companies && companies.length > 0) {
      console.log(`  显示前30条记录:`);
      companies.forEach(company => {
        console.log(`    - ${company.company_name} (类型: ${company.company_type || 'N/A'})`);
      });
    } else {
      console.log('  📝 companies 表为空');
    }

    // 3. 尝试读取职级数据（可能表名不同）
    console.log("\n📈 职级数据:");
    const possibleLevelTables = [
      'candidate_standard_levels', 
      'standard_levels', 
      'levels',
      'job_levels',
      'candidate_levels'
    ];
    
    let levelData = null;
    for (const tableName of possibleLevelTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(20);
          
        if (!error && data && data.length > 0) {
          console.log(`  ✅ 找到职级表: ${tableName}`);
          console.log(`  总计: ${data.length} 条记录`);
          data.forEach(level => {
            const name = level.level_name || level.name || level.title || 'N/A';
            const desc = level.description || level.desc || '';
            console.log(`    - ${name} ${desc ? '(' + desc + ')' : ''}`);
          });
          levelData = data;
          break;
        }
      } catch (e) {
        // 表不存在，继续尝试下一个
      }
    }
    
    if (!levelData) {
      console.log('  ❌ 未找到职级相关表');
    }

    // 4. 检查是否有其他相关表
    console.log("\n🔍 检查其他可能的表:");
    const otherTables = [
      'job_listings',
      'candidate_profiles', 
      'tech_directions',
      'business_scenarios'
    ];
    
    for (const tableName of otherTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
          
        if (!error) {
          const { count } = await supabase
            .from(tableName)
            .select('*', { count: 'exact', head: true });
          console.log(`  ✅ ${tableName} - ${count || 0} 条记录`);
        }
      } catch (e) {
        console.log(`  ❌ ${tableName} - 不存在`);
      }
    }

    console.log('\n✅ 数据库数据读取完成!');

  } catch (error) {
    console.error('❌ 数据库读取失败:', error);
  }
}

// 运行读取
readDatabaseData();
