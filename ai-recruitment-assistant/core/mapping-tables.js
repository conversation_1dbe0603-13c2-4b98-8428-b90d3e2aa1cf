/**
 * AI招聘助手系统 - 映射表和常量
 * 
 * 核心职责：
 * - 业务常量定义
 * - 映射关系管理
 * - 枚举值维护
 * - 配置数据存储
 * 
 * 预计代码量：1200行
 */

class MappingTables {
  constructor() {
    this.initializeMappings();
  }

  /**
   * 初始化所有映射表
   */
  initializeMappings() {
    // 意图类型映射
    this.intentTypes = {
      GREETING: 'greeting',
      PROFILE_UPDATE: 'profile_update',
      JOB_SEARCH: 'job_search',
      RECOMMENDATION_REQUEST: 'recommendation_request',
      TECH_DIRECTION_INQUIRY: 'tech_direction_inquiry',
      SALARY_INQUIRY: 'salary_inquiry',
      COMPANY_INQUIRY: 'company_inquiry',
      LOCATION_INQUIRY: 'location_inquiry',
      EXPERIENCE_INQUIRY: 'experience_inquiry',
      RESUME_UPLOAD: 'resume_upload',
      UNKNOWN: 'unknown'
    };

    // 技术方向映射
    this.techDirectionMapping = {
      // 前端技术
      frontend: {
        keywords: ['前端', 'frontend', 'fe', 'react', 'vue', 'angular', 'javascript', 'js', 'html', 'css'],
        standardName: '前端开发',
        category: 'development',
        level: 1
      },
      
      // 后端技术
      backend: {
        keywords: ['后端', 'backend', 'be', 'java', 'python', 'node.js', 'go', 'php', 'c++', 'c#'],
        standardName: '后端开发',
        category: 'development',
        level: 1
      },
      
      // 全栈技术
      fullstack: {
        keywords: ['全栈', 'fullstack', '全端', 'full-stack'],
        standardName: '全栈开发',
        category: 'development',
        level: 1
      },
      
      // 移动端技术
      mobile: {
        keywords: ['移动端', 'mobile', 'ios', 'android', 'react native', 'flutter', 'swift', 'kotlin'],
        standardName: '移动端开发',
        category: 'development',
        level: 1
      },
      
      // 数据相关
      data: {
        keywords: ['数据', 'data', '大数据', 'bigdata', '数据分析', '数据科学', 'ml', '机器学习'],
        standardName: '数据开发',
        category: 'data',
        level: 1
      },
      
      // 算法相关
      algorithm: {
        keywords: ['算法', 'algorithm', 'ai', '人工智能', '深度学习', 'deep learning'],
        standardName: '算法工程师',
        category: 'ai',
        level: 1
      }
    };

    // 公司类型映射
    this.companyTypeMapping = {
      startup: {
        keywords: ['创业', '初创', 'startup', '小公司'],
        standardName: '创业公司',
        description: '快速发展的初创企业',
        characteristics: ['成长快', '机会多', '挑战大']
      },
      
      bigtech: {
        keywords: ['大厂', '互联网大厂', 'bigtech', '腾讯', '阿里', '字节', '美团', '百度'],
        standardName: '互联网大厂',
        description: '知名互联网科技公司',
        characteristics: ['平台大', '福利好', '技术强']
      },
      
      traditional: {
        keywords: ['传统', '国企', '银行', '保险', '制造业'],
        standardName: '传统企业',
        description: '传统行业的稳定企业',
        characteristics: ['稳定性强', '福利完善', '发展稳健']
      },
      
      foreign: {
        keywords: ['外企', '外资', 'foreign', '跨国'],
        standardName: '外资企业',
        description: '国际化的外资公司',
        characteristics: ['国际化', '流程规范', '文化多元']
      }
    };

    // 地理位置映射
    this.locationMapping = {
      beijing: {
        keywords: ['北京', 'beijing', 'bj'],
        standardName: '北京',
        region: '华北',
        tier: 1
      },
      
      shanghai: {
        keywords: ['上海', 'shanghai', 'sh'],
        standardName: '上海',
        region: '华东',
        tier: 1
      },
      
      shenzhen: {
        keywords: ['深圳', 'shenzhen', 'sz'],
        standardName: '深圳',
        region: '华南',
        tier: 1
      },
      
      guangzhou: {
        keywords: ['广州', 'guangzhou', 'gz'],
        standardName: '广州',
        region: '华南',
        tier: 1
      },
      
      hangzhou: {
        keywords: ['杭州', 'hangzhou', 'hz'],
        standardName: '杭州',
        region: '华东',
        tier: 2
      },
      
      nanjing: {
        keywords: ['南京', 'nanjing', 'nj'],
        standardName: '南京',
        region: '华东',
        tier: 2
      },
      
      chengdu: {
        keywords: ['成都', 'chengdu', 'cd'],
        standardName: '成都',
        region: '西南',
        tier: 2
      },
      
      wuhan: {
        keywords: ['武汉', 'wuhan', 'wh'],
        standardName: '武汉',
        region: '华中',
        tier: 2
      }
    };

    // 薪资关键词映射
    this.salaryMapping = {
      ranges: {
        junior: { min: 8000, max: 15000, keywords: ['初级', '新手', '应届', '1-3年'] },
        middle: { min: 15000, max: 30000, keywords: ['中级', '3-5年', '有经验'] },
        senior: { min: 30000, max: 50000, keywords: ['高级', '资深', '5-8年'] },
        expert: { min: 50000, max: 100000, keywords: ['专家', '架构师', '8年以上'] }
      },
      
      keywords: {
        '8k': 8000, '10k': 10000, '15k': 15000, '20k': 20000,
        '25k': 25000, '30k': 30000, '35k': 35000, '40k': 40000,
        '50k': 50000, '60k': 60000, '80k': 80000, '100k': 100000
      }
    };

    // 经验等级映射
    this.experienceMapping = {
      fresh: {
        keywords: ['应届', '新手', '0年', '无经验', 'fresh'],
        standardName: '应届生',
        yearRange: [0, 1],
        level: 1
      },
      
      junior: {
        keywords: ['初级', '1年', '2年', '1-3年', 'junior'],
        standardName: '初级工程师',
        yearRange: [1, 3],
        level: 2
      },
      
      middle: {
        keywords: ['中级', '3年', '4年', '5年', '3-5年', 'middle'],
        standardName: '中级工程师',
        yearRange: [3, 5],
        level: 3
      },
      
      senior: {
        keywords: ['高级', '资深', '6年', '7年', '8年', '5-8年', 'senior'],
        standardName: '高级工程师',
        yearRange: [5, 8],
        level: 4
      },
      
      expert: {
        keywords: ['专家', '架构师', '技术专家', '8年以上', 'expert', 'architect'],
        standardName: '技术专家',
        yearRange: [8, 20],
        level: 5
      }
    };

    // 业务场景映射
    this.businessScenarioMapping = {
      ecommerce: {
        keywords: ['电商', '购物', '商城', 'ecommerce', '零售'],
        standardName: '电商业务',
        category: 'business',
        description: '电子商务相关业务场景'
      },
      
      fintech: {
        keywords: ['金融', '支付', '银行', 'fintech', '保险', '理财'],
        standardName: '金融科技',
        category: 'business',
        description: '金融科技相关业务场景'
      },
      
      social: {
        keywords: ['社交', '社区', '直播', 'social', '内容'],
        standardName: '社交媒体',
        category: 'business',
        description: '社交媒体相关业务场景'
      },
      
      gaming: {
        keywords: ['游戏', 'game', '娱乐', '竞技'],
        standardName: '游戏娱乐',
        category: 'business',
        description: '游戏娱乐相关业务场景'
      },
      
      education: {
        keywords: ['教育', '学习', 'education', '培训', '知识'],
        standardName: '在线教育',
        category: 'business',
        description: '在线教育相关业务场景'
      }
    };

    // 固定话术库
    this.fixedResponses = {
      greeting: {
        newUser: '你好！欢迎使用AI招聘助手。我是Katrina，很高兴为您服务！',
        returningUser: '欢迎回来！今天想要什么帮助吗？',
        general: '您好！我可以帮您找到合适的工作机会。'
      },
      
      profileIncomplete: {
        techDirection: '为了给您推荐最合适的职位，请告诉我您的技术方向？',
        experience: '请告诉我您有多少年工作经验？',
        salary: '您期望的薪资范围是多少？',
        location: '您希望在哪个城市工作？'
      },
      
      clarification: {
        techDirection: '您提到的技术方向我不太确定，您是指以下哪一种？',
        ambiguous: '您的描述有些模糊，能否更具体一些？',
        multiple: '我找到了多个相关选项，请选择最符合您需求的：'
      },
      
      error: {
        general: '抱歉，我遇到了一些技术问题。请稍后再试。',
        noResults: '抱歉，没有找到符合条件的职位。您可以调整一下搜索条件。',
        invalidInput: '您的输入格式不正确，请重新输入。'
      }
    };

    // 推荐策略配置
    this.recommendationConfig = {
      matrix4x4: {
        companyTypes: ['startup', 'bigtech', 'traditional', 'foreign'],
        techDirections: ['frontend', 'backend', 'fullstack', 'mobile'],
        maxJobsPerCell: 3,
        minMatchScore: 0.6
      },
      
      scoring: {
        techMatch: 0.4,        // 技术匹配权重
        salaryMatch: 0.3,      // 薪资匹配权重
        locationMatch: 0.2,    // 地点匹配权重
        experienceMatch: 0.1   // 经验匹配权重
      },
      
      filters: {
        maxRecommendations: 20,
        diversityFactor: 0.3,
        freshnessDays: 30
      }
    };
  }

  // ==================== 获取方法 ====================

  /**
   * 获取意图类型
   */
  getIntentTypes() {
    return this.intentTypes;
  }

  /**
   * 获取技术方向映射
   */
  getTechDirectionMapping() {
    return this.techDirectionMapping;
  }

  /**
   * 获取公司类型映射
   */
  getCompanyTypeMapping() {
    return this.companyTypeMapping;
  }

  /**
   * 获取地理位置映射
   */
  getLocationMapping() {
    return this.locationMapping;
  }

  /**
   * 获取薪资映射
   */
  getSalaryMapping() {
    return this.salaryMapping;
  }

  /**
   * 获取经验映射
   */
  getExperienceMapping() {
    return this.experienceMapping;
  }

  /**
   * 获取业务场景映射
   */
  getBusinessScenarioMapping() {
    return this.businessScenarioMapping;
  }

  /**
   * 获取固定话术
   */
  getFixedResponses() {
    return this.fixedResponses;
  }

  /**
   * 获取推荐配置
   */
  getRecommendationConfig() {
    return this.recommendationConfig;
  }

  // ==================== 查找方法 ====================

  /**
   * 根据关键词查找技术方向
   */
  findTechDirectionByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    
    for (const [key, mapping] of Object.entries(this.techDirectionMapping)) {
      if (mapping.keywords.some(k => k.toLowerCase().includes(lowerKeyword))) {
        return { key, ...mapping };
      }
    }
    
    return null;
  }

  /**
   * 根据关键词查找公司类型
   */
  findCompanyTypeByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    
    for (const [key, mapping] of Object.entries(this.companyTypeMapping)) {
      if (mapping.keywords.some(k => k.toLowerCase().includes(lowerKeyword))) {
        return { key, ...mapping };
      }
    }
    
    return null;
  }

  /**
   * 根据关键词查找地理位置
   */
  findLocationByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    
    for (const [key, mapping] of Object.entries(this.locationMapping)) {
      if (mapping.keywords.some(k => k.toLowerCase().includes(lowerKeyword))) {
        return { key, ...mapping };
      }
    }
    
    return null;
  }

  /**
   * 根据年限查找经验等级
   */
  findExperienceByYears(years) {
    for (const [key, mapping] of Object.entries(this.experienceMapping)) {
      if (years >= mapping.yearRange[0] && years <= mapping.yearRange[1]) {
        return { key, ...mapping };
      }
    }
    
    return null;
  }

  /**
   * 解析薪资关键词
   */
  parseSalaryKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();
    
    // 直接匹配
    if (this.salaryMapping.keywords[lowerKeyword]) {
      return this.salaryMapping.keywords[lowerKeyword];
    }
    
    // 数字提取
    const numberMatch = keyword.match(/(\d+)k?/i);
    if (numberMatch) {
      const number = parseInt(numberMatch[1]);
      return keyword.toLowerCase().includes('k') ? number * 1000 : number;
    }
    
    return null;
  }
}

module.exports = MappingTables;
