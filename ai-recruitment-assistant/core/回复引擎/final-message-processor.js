// final-message-processor.js - 最终消息处理器
// 简单分类 + 硬编码回复 + 少量API调用

const FirstMessageClassifier = require('./first-message-classifier');

class FinalMessageProcessor {
  constructor() {
    this.classifier = new FirstMessageClassifier();
    this.sessions = new Map(); // 简单会话管理
  }

  /**
   * 处理消息 - 主入口
   * @param {Object} messageData - 消息数据
   * @returns {Object} 处理结果
   */
  async processMessage(messageData) {
    try {
      const { message, sessionId, userEmail } = messageData;
      
      console.log(`\n🚀 最终消息处理器 - 开始处理`);
      console.log(`📨 消息: "${message}"`);
      console.log(`🆔 会话: ${sessionId}`);

      // 检查是否为第一句消息
      const isFirstMessage = !this.sessions.has(sessionId);
      console.log(`🔍 是否第一句: ${isFirstMessage}`);

      if (isFirstMessage) {
        return await this.handleFirstMessage(message, sessionId, userEmail);
      } else {
        return await this.handleFollowUpMessage(message, sessionId);
      }

    } catch (error) {
      console.error(`❌ 处理失败:`, error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 处理第一句消息
   * @param {string} message - 用户消息
   * @param {string} sessionId - 会话ID
   * @param {string} userEmail - 用户邮箱
   * @returns {Object} 处理结果
   */
  async handleFirstMessage(message, sessionId, userEmail) {
    console.log(`🎯 处理第一句消息`);
    
    // 1. 分类消息
    const classification = this.classifier.classify(message);
    console.log(`📋 分类结果: ${classification.category} (${classification.method})`);
    
    // 2. 获取回复
    const response = this.classifier.getResponse(classification, message);
    console.log(`📤 回复类型: ${response.type}`);
    console.log(`💬 回复内容: ${response.content.substring(0, 50)}...`);
    
    // 3. 记录会话
    this.recordSession(sessionId, userEmail, message, response);
    
    return {
      success: true,
      sessionId: sessionId,
      response: response,
      debug: {
        isFirstMessage: true,
        classification: classification,
        processingTime: new Date().toISOString()
      }
    };
  }

  /**
   * 处理后续消息
   * @param {string} message - 用户消息
   * @param {string} sessionId - 会话ID
   * @returns {Object} 处理结果
   */
  async handleFollowUpMessage(message, sessionId) {
    console.log(`🔄 处理后续消息`);
    
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.log(`⚠️ 会话不存在，当作第一句处理`);
      return await this.handleFirstMessage(message, sessionId, '<EMAIL>');
    }

    // 简单的后续消息处理
    const response = this.getFollowUpResponse(message, session);
    
    // 更新会话
    this.updateSession(sessionId, message, response);
    
    return {
      success: true,
      sessionId: sessionId,
      response: response,
      debug: {
        isFirstMessage: false,
        messageCount: session.messages.length,
        processingTime: new Date().toISOString()
      }
    };
  }

  /**
   * 获取后续消息回复
   * @param {string} message - 用户消息
   * @param {Object} session - 会话信息
   * @returns {Object} 回复对象
   */
  getFollowUpResponse(message, session) {
    const lowerMessage = message.toLowerCase().trim();
    
    // 技术栈回复
    if (lowerMessage.includes('前端') || lowerMessage.includes('react') || lowerMessage.includes('vue')) {
      return {
        type: 'tech_stack_frontend',
        content: '前端开发很不错！我们有很多前端职位，包括React、Vue、Angular等。您有几年经验？期望薪资是多少？',
        suggestions: ['1-3年经验', '3-5年经验', '5年以上', '我想了解薪资']
      };
    }
    
    if (lowerMessage.includes('后端') || lowerMessage.includes('java') || lowerMessage.includes('python')) {
      return {
        type: 'tech_stack_backend',
        content: '后端开发需求很大！我们有Java、Python、Go等职位。您主要用什么技术？有几年经验？',
        suggestions: ['Java开发', 'Python开发', 'Go开发', 'Node.js开发']
      };
    }
    
    // 经验年限回复
    if (/\d+年/.test(message)) {
      return {
        type: 'experience_info',
        content: '好的，了解了您的工作经验。请告诉我您期望的薪资范围和希望加入的公司类型？',
        suggestions: ['大厂优先', '创业公司', '薪资面议', '了解市场行情']
      };
    }
    
    // 默认后续回复
    return {
      type: 'follow_up_default',
      content: '请告诉我更多信息，比如您的技术栈、工作经验、期望薪资等，这样我能为您推荐更合适的职位。',
      suggestions: ['我的技术栈', '工作经验', '期望薪资', '公司类型偏好']
    };
  }

  /**
   * 记录会话
   * @param {string} sessionId - 会话ID
   * @param {string} userEmail - 用户邮箱
   * @param {string} message - 用户消息
   * @param {Object} response - 回复对象
   */
  recordSession(sessionId, userEmail, message, response) {
    const session = {
      sessionId,
      userEmail,
      messages: [
        {
          type: 'user',
          content: message,
          timestamp: new Date().toISOString()
        },
        {
          type: 'assistant',
          content: response.content,
          responseType: response.type,
          timestamp: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      lastActiveAt: new Date().toISOString()
    };

    this.sessions.set(sessionId, session);
    console.log(`📝 会话已记录，消息数: ${session.messages.length}`);
  }

  /**
   * 更新会话
   * @param {string} sessionId - 会话ID
   * @param {string} message - 用户消息
   * @param {Object} response - 回复对象
   */
  updateSession(sessionId, message, response) {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.messages.push(
        {
          type: 'user',
          content: message,
          timestamp: new Date().toISOString()
        },
        {
          type: 'assistant',
          content: response.content,
          responseType: response.type,
          timestamp: new Date().toISOString()
        }
      );
      session.lastActiveAt = new Date().toISOString();
      console.log(`📝 会话已更新，总消息数: ${session.messages.length}`);
    }
  }

  /**
   * 获取错误回复
   * @param {Error} error - 错误对象
   * @returns {Object} 错误回复
   */
  getErrorResponse(error) {
    return {
      success: false,
      error: error.message,
      response: {
        type: 'error',
        content: '抱歉，系统遇到了问题，请稍后重试。',
        suggestions: ['重新开始', '联系客服']
      }
    };
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const classifierStats = this.classifier.getStats();
    
    return {
      totalSessions: this.sessions.size,
      activeSessions: Array.from(this.sessions.values()).filter(
        session => (new Date() - new Date(session.lastActiveAt)) < 24 * 60 * 60 * 1000
      ).length,
      classifier: classifierStats
    };
  }

  /**
   * 获取会话信息
   * @param {string} sessionId - 会话ID
   * @returns {Object|null} 会话信息
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId) || null;
  }
}

module.exports = FinalMessageProcessor;
