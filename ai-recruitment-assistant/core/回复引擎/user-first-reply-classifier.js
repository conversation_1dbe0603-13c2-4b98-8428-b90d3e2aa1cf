// user-first-reply-classifier.js - 用户第一句回复分类器
// 专门处理用户在Katrina开场白后的第一句回复

class UserFirstReplyClassifier {
  constructor() {
    // 用户第一句回复的4种场景分类
    this.classificationRules = [
      {
        category: 'GREETING',
        description: '问候类',
        keywords: ['你好', 'hi', 'hello', '嗨', '哈喽', '在的', '在', '您好', 'hey', '嗯'],
        priority: 1,
        response: {
          type: 'user_first_greeting',
          content: '您有兴趣看看新机会吗？我这边合作的客户职位还挺多的。',
          suggestions: ['有兴趣了解', '我想看看', '暂时不考虑', '先了解一下']
        }
      },
      
      {
        category: 'DIRECT_INQUIRY',
        description: '直接咨询类',
        keywords: [
          '职位推荐', '有什么介绍', '有什么公司', '有什么推荐', 
          '职位', '工作', '招聘', '岗位', '机会', '有什么', '推荐', 
          '什么职位', '找工作', '介绍', '公司', '什么工作', '工作机会',
          '有哪些', '都有什么', '推荐一下', '介绍一下'
        ],
        priority: 2,
        response: {
          type: 'user_first_direct_inquiry',
          content: '我们合作的公司挺多的，大厂、中厂、创业、国企公司都有，职位也不少。',
          followUpContent: '您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。',
          followUpDelay: 2000, // 2秒延迟
          hasFollowUp: true,
          suggestions: ['我是前端开发工程师', '我做后端开发', '我是全栈工程师', '我想了解更多']
        }
      },
      
      {
        category: 'DOUBT_AI',
        description: '疑惑类',
        keywords: [
          '你是ai吗', '你是ai', 'ai', '机器人', '人工智能', 
          '真人', '人工', '什么系统', '谁在说话', '是机器人吗',
          '你是真人吗', '是人工智能吗', '智能助手', '虚拟助手'
        ],
        priority: 3,
        response: {
          type: 'user_first_doubt_ai',
          content: '我是Felton团队创造的AI猎头顾问，我会为您针对性的提供合适的职位。',
          followUpContent: '请问您最近有兴趣看看新机会吗？',
          followUpDelay: 2000, // 2秒延迟
          hasFollowUp: true,
          suggestions: ['有兴趣了解', '我想看看', '暂时不考虑', '先了解一下']
        }
      }
    ];
    
    // 默认回复（需要API推理的情况）
    this.defaultResponse = {
      type: 'user_first_api_inference',
      content: '让我为您分析一下...',
      needsAPI: true,
      suggestions: ['我想了解职位', '告诉我技术要求', '薪资范围如何', '公司情况']
    };
  }

  /**
   * 分类用户第一句回复
   * @param {string} message - 用户消息
   * @returns {Object} 分类结果
   */
  classify(message) {
    console.log(`🔍 分类用户第一句回复: "${message}"`);
    
    const lowerMessage = message.toLowerCase().trim();
    
    // 按优先级遍历规则
    for (const rule of this.classificationRules) {
      const matched = rule.keywords.some(keyword => 
        lowerMessage.includes(keyword.toLowerCase())
      );
      
      if (matched) {
        console.log(`✅ 匹配分类: ${rule.category} - ${rule.description} (优先级: ${rule.priority})`);
        return {
          category: rule.category,
          description: rule.description,
          confidence: 1.0,
          method: 'keyword_match',
          rule: rule,
          needsAPI: false
        };
      }
    }
    
    // 没有匹配到规则，需要API推理
    console.log(`🤖 未匹配到规则，需要API推理`);
    return {
      category: 'API_INFERENCE',
      description: '需要API推理',
      confidence: 0.5,
      method: 'api_inference',
      rule: null,
      needsAPI: true
    };
  }

  /**
   * 获取回复内容
   * @param {Object} classification - 分类结果
   * @param {string} originalMessage - 原始消息
   * @returns {Object} 回复对象
   */
  getResponse(classification, originalMessage) {
    if (classification.needsAPI) {
      // 需要API推理的情况
      return this.getAPIResponse(originalMessage);
    }
    
    if (classification.rule) {
      // 有匹配规则的情况
      return {
        ...classification.rule.response,
        metadata: {
          isUserFirstReply: true,
          category: classification.category,
          description: classification.description,
          method: classification.method,
          confidence: classification.confidence,
          timestamp: new Date().toISOString()
        }
      };
    }
    
    // 默认回复
    return {
      ...this.defaultResponse,
      metadata: {
        isUserFirstReply: true,
        category: 'DEFAULT',
        description: '默认回复',
        method: 'default',
        confidence: 0.3,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * API推理回复
   * @param {string} message - 用户消息
   * @returns {Object} 回复对象
   */
  getAPIResponse(message) {
    console.log(`🤖 API推理处理用户第一句回复: "${message}"`);
    
    // 简单的关键词权重分析
    const weights = {
      greeting: 0,
      inquiry: 0,
      doubt: 0,
      tech: 0,
      salary: 0
    };
    
    const lowerMessage = message.toLowerCase();
    
    // 计算权重
    if (/你好|hello|hi|嗨/.test(lowerMessage)) weights.greeting += 2;
    if (/职位|工作|招聘|推荐/.test(lowerMessage)) weights.inquiry += 3;
    if (/ai|机器人|真人/.test(lowerMessage)) weights.doubt += 3;
    if (/技术|开发|编程/.test(lowerMessage)) weights.tech += 2;
    if (/薪资|工资|待遇/.test(lowerMessage)) weights.salary += 2;
    
    // 找到最高权重
    const maxWeight = Math.max(...Object.values(weights));
    const topCategory = Object.keys(weights).find(key => weights[key] === maxWeight);
    
    // 根据分析结果返回回复
    switch (topCategory) {
      case 'inquiry':
        return {
          type: 'user_first_api_inquiry',
          content: '我们合作的公司挺多的，大厂、中厂、创业、国企公司都有，职位也不少。根据您的描述，我来为您详细介绍。',
          followUpContent: '您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。',
          followUpDelay: 2000,
          hasFollowUp: true,
          suggestions: ['告诉我具体需求', '我想了解薪资', '推荐大厂职位', '了解技术要求'],
          metadata: {
            isUserFirstReply: true,
            category: 'API_INQUIRY',
            method: 'api_inference',
            confidence: 0.8,
            weights: weights,
            timestamp: new Date().toISOString()
          }
        };
      
      case 'doubt':
        return {
          type: 'user_first_api_doubt',
          content: '我是Felton团队创造的AI猎头顾问，我会为您针对性的提供合适的职位。',
          followUpContent: '请问您最近有兴趣看看新机会吗？',
          followUpDelay: 2000,
          hasFollowUp: true,
          suggestions: ['有兴趣了解', '我想看看', '暂时不考虑', '先了解一下'],
          metadata: {
            isUserFirstReply: true,
            category: 'API_DOUBT',
            method: 'api_inference',
            confidence: 0.8,
            weights: weights,
            timestamp: new Date().toISOString()
          }
        };
      
      default:
        return {
          type: 'user_first_api_general',
          content: '我理解您的需求。我是专业的AI猎头顾问，可以为您提供个性化的职位推荐。',
          followUpContent: '请告诉我您的技术方向和期望，我来为您匹配合适的机会。',
          followUpDelay: 2000,
          hasFollowUp: true,
          suggestions: ['我想找工作', '了解薪资行情', '技术栈匹配', '公司推荐'],
          metadata: {
            isUserFirstReply: true,
            category: 'API_GENERAL',
            method: 'api_inference',
            confidence: 0.6,
            weights: weights,
            timestamp: new Date().toISOString()
          }
        };
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalRules = this.classificationRules.length;
    const totalKeywords = this.classificationRules.reduce((sum, rule) => sum + rule.keywords.length, 0);
    const categories = this.classificationRules.map(rule => ({
      category: rule.category,
      description: rule.description,
      keywordCount: rule.keywords.length
    }));
    
    return {
      totalRules,
      totalKeywords,
      categories,
      hasAPIFallback: true,
      supportedScenarios: [
        '问候类 - 您有兴趣看看新机会吗',
        '直接咨询类 - 我们合作的公司挺多的 + 2秒延迟追加',
        '疑惑类 - 我是Felton团队AI猎头 + 2秒延迟追加',
        '其他 - API推理处理'
      ]
    };
  }
}

module.exports = UserFirstReplyClassifier;
