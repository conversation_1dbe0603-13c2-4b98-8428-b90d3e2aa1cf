// simple-message-processor.js - 简化的消息处理器
// 优先使用硬编码规则，必要时调用AI推理

const HardcodedReplies = require('./hardcoded-replies');

class SimpleMessageProcessor {
  constructor() {
    this.hardcodedReplies = new HardcodedReplies();
    this.sessions = new Map(); // 简单的会话管理
  }

  /**
   * 处理消息的主入口
   * @param {Object} messageData - 消息数据
   * @returns {Object} 处理结果
   */
  async processMessage(messageData) {
    try {
      const { message, sessionId, userEmail } = messageData;
      
      console.log(`\n🔄 简化消息处理器 - 开始处理`);
      console.log(`📨 消息: "${message}"`);
      console.log(`🆔 会话ID: ${sessionId}`);
      console.log(`📧 用户邮箱: ${userEmail}`);

      // 1. 检查是否为第一句消息
      const isFirstMessage = this.isFirstMessage(sessionId);
      console.log(`🔍 是否第一句消息: ${isFirstMessage}`);

      if (isFirstMessage) {
        // 第一句消息：使用硬编码规则
        const response = this.handleFirstMessage(message, sessionId);
        
        // 记录会话
        this.recordSession(sessionId, userEmail, message, response);
        
        return {
          success: true,
          sessionId: sessionId,
          response: response,
          debug: {
            isFirstMessage: true,
            processingMethod: "hardcoded_rules",
            timestamp: new Date().toISOString()
          }
        };
      } else {
        // 后续消息：可以使用更复杂的逻辑或AI推理
        const response = await this.handleFollowUpMessage(message, sessionId);
        
        return {
          success: true,
          sessionId: sessionId,
          response: response,
          debug: {
            isFirstMessage: false,
            processingMethod: "follow_up_logic",
            timestamp: new Date().toISOString()
          }
        };
      }

    } catch (error) {
      console.error(`❌ 消息处理失败:`, error);
      return {
        success: false,
        error: error.message,
        response: {
          type: "error",
          content: "抱歉，系统遇到了问题，请稍后重试。",
          suggestions: ["重新开始", "联系客服"]
        }
      };
    }
  }

  /**
   * 处理第一句消息
   * @param {string} message - 用户消息
   * @param {string} sessionId - 会话ID
   * @returns {Object} 回复对象
   */
  handleFirstMessage(message, sessionId) {
    console.log(`🎯 处理第一句消息`);
    
    // 检查是否需要AI推理
    if (this.hardcodedReplies.needsAIInference(message)) {
      console.log(`🤖 消息复杂，可能需要AI推理`);
      // 这里可以调用AI API，但现在先用默认回复
      return this.hardcodedReplies.getFirstReply(message);
    }
    
    // 使用硬编码规则
    return this.hardcodedReplies.getFirstReply(message);
  }

  /**
   * 处理后续消息
   * @param {string} message - 用户消息
   * @param {string} sessionId - 会话ID
   * @returns {Object} 回复对象
   */
  async handleFollowUpMessage(message, sessionId) {
    console.log(`🔄 处理后续消息`);
    
    // 获取会话历史
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.log(`⚠️ 会话不存在，当作第一句处理`);
      return this.handleFirstMessage(message, sessionId);
    }

    // 简单的后续消息处理逻辑
    const lowerMessage = message.toLowerCase().trim();
    
    // 技术栈信息
    if (lowerMessage.includes('前端') || lowerMessage.includes('react') || lowerMessage.includes('vue')) {
      return {
        type: "tech_stack_frontend",
        content: "前端开发很不错！我们有很多前端职位，包括React、Vue、Angular等技术栈。您有几年经验？期望薪资范围是多少？",
        suggestions: ["1-3年经验", "3-5年经验", "5年以上", "我想了解薪资范围"]
      };
    }
    
    if (lowerMessage.includes('后端') || lowerMessage.includes('java') || lowerMessage.includes('python')) {
      return {
        type: "tech_stack_backend", 
        content: "后端开发需求很大！我们有Java、Python、Go、Node.js等各种技术栈的职位。您主要用什么技术？",
        suggestions: ["Java开发", "Python开发", "Go开发", "Node.js开发"]
      };
    }

    // 默认后续回复
    return {
      type: "follow_up_default",
      content: "请告诉我更多关于您的技术背景和期望，这样我能为您推荐更合适的职位。",
      suggestions: [
        "我的技术栈是...",
        "我有...年经验",
        "我期望薪资...",
        "我想了解具体职位"
      ]
    };
  }

  /**
   * 检查是否为第一句消息
   * @param {string} sessionId - 会话ID
   * @returns {boolean} 是否为第一句消息
   */
  isFirstMessage(sessionId) {
    return !this.sessions.has(sessionId);
  }

  /**
   * 记录会话信息
   * @param {string} sessionId - 会话ID
   * @param {string} userEmail - 用户邮箱
   * @param {string} message - 用户消息
   * @param {Object} response - 回复对象
   */
  recordSession(sessionId, userEmail, message, response) {
    const session = this.sessions.get(sessionId) || {
      sessionId,
      userEmail,
      messages: [],
      createdAt: new Date().toISOString()
    };

    session.messages.push({
      type: 'user',
      content: message,
      timestamp: new Date().toISOString()
    });

    session.messages.push({
      type: 'assistant',
      content: response.content,
      responseType: response.type,
      timestamp: new Date().toISOString()
    });

    session.lastActiveAt = new Date().toISOString();
    this.sessions.set(sessionId, session);

    console.log(`📝 会话已记录，消息数: ${session.messages.length}`);
  }

  /**
   * 获取会话信息
   * @param {string} sessionId - 会话ID
   * @returns {Object|null} 会话信息
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hardcodedStats = this.hardcodedReplies.getStats();
    
    return {
      totalSessions: this.sessions.size,
      hardcodedRules: hardcodedStats,
      activeSessions: Array.from(this.sessions.values()).filter(
        session => {
          const lastActive = new Date(session.lastActiveAt);
          const now = new Date();
          return (now - lastActive) < 24 * 60 * 60 * 1000; // 24小时内活跃
        }
      ).length
    };
  }
}

module.exports = SimpleMessageProcessor;
