// hardcoded-replies.js - 硬编码回复系统
// 简单、直接、可靠的第一句回复处理

class HardcodedReplies {
  constructor() {
    // 第一句回复映射表
    this.firstReplyMap = {
      // 简单问候类 - 返回简短回复
      greeting_simple: {
        keywords: ["你好", "hi", "hello", "哈喽", "在吗", "在的"],
        response: {
          type: "first_greeting_simple",
          content: "您有兴趣看看新机会吗？我这边合作的客户职位还挺多的。",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发", 
            "我是全栈工程师",
            "我想了解更多"
          ]
        }
      },

      // 职位询问类 - 返回职位相关回复
      job_inquiry: {
        keywords: ["职位", "工作", "招聘", "岗位", "机会", "有什么", "推荐", "什么职位"],
        response: {
          type: "first_job_inquiry",
          content: "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师", 
            "我想了解更多"
          ]
        }
      },

      // 身份确认类 - 返回完整开场白
      identity_check: {
        keywords: ["你是谁", "ai", "机器人", "真人", "人工", "什么系统"],
        response: {
          type: "first_greeting_full",
          content: "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想上传简历"
          ]
        }
      },

      // 技术栈询问类
      tech_inquiry: {
        keywords: ["技术栈", "用什么技术", "开发语言", "框架", "技术方向"],
        response: {
          type: "first_tech_inquiry", 
          content: "我们有各种技术栈的职位，前端、后端、全栈、移动端都有。请告诉我您的技术方向？",
          suggestions: [
            "前端开发(React/Vue)",
            "后端开发(Java/Python/Go)",
            "全栈开发",
            "移动端开发"
          ]
        }
      },

      // 薪资询问类
      salary_inquiry: {
        keywords: ["薪资", "工资", "薪水", "待遇", "多少钱", "收入"],
        response: {
          type: "first_salary_inquiry",
          content: "薪资范围根据技术栈和经验而定。请先告诉我您的技术方向和工作年限？",
          suggestions: [
            "前端，3年经验",
            "后端，5年经验", 
            "全栈，2年经验",
            "我想了解具体范围"
          ]
        }
      }
    };

    // 默认回复
    this.defaultFirstReply = {
      type: "first_greeting_default",
      content: "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。",
      suggestions: [
        "我是前端开发工程师",
        "我做后端开发",
        "我是全栈工程师",
        "我想了解更多"
      ]
    };
  }

  /**
   * 获取第一句回复
   * @param {string} message - 用户消息
   * @returns {Object} 回复对象
   */
  getFirstReply(message) {
    console.log(`🔍 硬编码回复引擎 - 分析消息: "${message}"`);
    
    const lowerMessage = message.toLowerCase().trim();
    
    // 遍历所有回复类型，寻找匹配的关键词
    for (const [category, config] of Object.entries(this.firstReplyMap)) {
      const matched = config.keywords.some(keyword => 
        lowerMessage.includes(keyword.toLowerCase())
      );
      
      if (matched) {
        console.log(`✅ 匹配到类型: ${category}`);
        console.log(`📤 返回回复类型: ${config.response.type}`);
        console.log(`💬 回复内容: ${config.response.content.substring(0, 50)}...`);
        
        return {
          ...config.response,
          metadata: {
            isFirstResponse: true,
            matchedCategory: category,
            responseSource: "hardcoded_rules",
            timestamp: new Date().toISOString()
          }
        };
      }
    }
    
    // 没有匹配到任何规则，返回默认回复
    console.log(`❓ 未匹配到规则，返回默认回复`);
    console.log(`📤 返回回复类型: ${this.defaultFirstReply.type}`);
    
    return {
      ...this.defaultFirstReply,
      metadata: {
        isFirstResponse: true,
        matchedCategory: "default",
        responseSource: "hardcoded_default",
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 检查是否需要AI推理
   * @param {string} message - 用户消息
   * @returns {boolean} 是否需要AI推理
   */
  needsAIInference(message) {
    // 如果消息很复杂或包含多个意图，可能需要AI推理
    const complexPatterns = [
      /我是.+工程师.+年经验/,  // 复合信息
      /想找.+公司.+职位/,      // 复合需求
      /薪资.+技术栈.+/,        // 多重询问
    ];
    
    return complexPatterns.some(pattern => pattern.test(message));
  }

  /**
   * 添加新的硬编码规则
   * @param {string} category - 类别名称
   * @param {Array} keywords - 关键词列表
   * @param {Object} response - 回复对象
   */
  addRule(category, keywords, response) {
    this.firstReplyMap[category] = {
      keywords,
      response
    };
    console.log(`✅ 添加新规则: ${category}`);
  }

  /**
   * 获取所有规则统计
   * @returns {Object} 规则统计信息
   */
  getStats() {
    const totalRules = Object.keys(this.firstReplyMap).length;
    const totalKeywords = Object.values(this.firstReplyMap)
      .reduce((sum, rule) => sum + rule.keywords.length, 0);
    
    return {
      totalRules,
      totalKeywords,
      categories: Object.keys(this.firstReplyMap)
    };
  }
}

module.exports = HardcodedReplies;
