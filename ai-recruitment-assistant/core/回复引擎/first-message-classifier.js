// first-message-classifier.js - 第一句话分类器
// 简单、直接、高效的分类系统

class FirstMessageClassifier {
  constructor() {
    // 第一句话分类规则 - 按优先级排序
    this.classificationRules = [
      {
        category: "GREETING_SIMPLE",
        keywords: ["你好", "hi", "hello", "哈喽", "在吗", "在的"],
        priority: 1,
        response: {
          type: "first_greeting_simple",
          content: "您有兴趣看看新机会吗？我这边合作的客户职位还挺多的。",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想了解更多",
          ],
        },
      },

      {
        category: "JOB_INQUIRY",
        keywords: [
          "职位",
          "工作",
          "招聘",
          "岗位",
          "机会",
          "有什么",
          "推荐",
          "什么职位",
          "找工作",
        ],
        priority: 2,
        response: {
          type: "first_job_inquiry",
          content: "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想了解更多",
          ],
        },
      },

      {
        category: "IDENTITY_CHECK",
        keywords: [
          "你是谁",
          "ai",
          "机器人",
          "真人",
          "人工",
          "什么系统",
          "谁在说话",
        ],
        priority: 3,
        response: {
          type: "first_greeting_full",
          content:
            "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想上传简历",
          ],
        },
      },

      {
        category: "TECH_INQUIRY",
        keywords: [
          "技术栈",
          "技术",
          "开发",
          "编程",
          "语言",
          "框架",
          "前端",
          "后端",
          "全栈",
        ],
        priority: 4,
        response: {
          type: "first_tech_inquiry",
          content:
            "我们有各种技术栈的职位，前端、后端、全栈、移动端都有。请告诉我您的技术方向？",
          suggestions: [
            "前端开发(React/Vue)",
            "后端开发(Java/Python/Go)",
            "全栈开发",
            "移动端开发",
          ],
        },
      },

      {
        category: "SALARY_INQUIRY",
        keywords: ["薪资", "工资", "薪水", "待遇", "多少钱", "收入", "薪酬"],
        priority: 5,
        response: {
          type: "first_salary_inquiry",
          content:
            "薪资范围根据技术栈和经验而定。请先告诉我您的技术方向和工作年限？",
          suggestions: [
            "前端，3年经验",
            "后端，5年经验",
            "全栈，2年经验",
            "我想了解具体范围",
          ],
        },
      },
    ];

    // 默认回复
    this.defaultResponse = {
      type: "first_greeting_default",
      content:
        "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。",
      suggestions: [
        "我是前端开发工程师",
        "我做后端开发",
        "我是全栈工程师",
        "我想了解更多",
      ],
    };
  }

  /**
   * 分类第一句消息
   * @param {string} message - 用户消息
   * @returns {Object} 分类结果
   */
  classify(message) {
    console.log(`🔍 分类第一句消息: "${message}"`);

    const lowerMessage = message.toLowerCase().trim();

    // 按优先级遍历规则
    for (const rule of this.classificationRules) {
      const matched = rule.keywords.some((keyword) =>
        lowerMessage.includes(keyword.toLowerCase())
      );

      if (matched) {
        console.log(`✅ 匹配分类: ${rule.category} (优先级: ${rule.priority})`);
        return {
          category: rule.category,
          confidence: 1.0,
          method: "keyword_match",
          rule: rule,
          needsAPI: false,
        };
      }
    }

    // 检查是否需要API推理
    if (this.needsAPIInference(message)) {
      console.log(`🤖 需要API推理: 消息复杂或包含多重意图`);
      return {
        category: "NEEDS_API",
        confidence: 0.5,
        method: "api_inference",
        rule: null,
        needsAPI: true,
      };
    }

    // 默认分类
    console.log(`❓ 使用默认分类`);
    return {
      category: "DEFAULT",
      confidence: 0.3,
      method: "default",
      rule: null,
      needsAPI: false,
    };
  }

  /**
   * 获取回复内容
   * @param {Object} classification - 分类结果
   * @param {string} originalMessage - 原始消息
   * @returns {Object} 回复对象
   */
  getResponse(classification, originalMessage) {
    if (classification.needsAPI) {
      // 需要API推理的情况
      return this.getAPIResponse(originalMessage);
    }

    if (classification.rule) {
      // 有匹配规则的情况
      return {
        ...classification.rule.response,
        metadata: {
          isFirstResponse: true,
          category: classification.category,
          method: classification.method,
          confidence: classification.confidence,
          timestamp: new Date().toISOString(),
        },
      };
    }

    // 默认回复
    return {
      ...this.defaultResponse,
      metadata: {
        isFirstResponse: true,
        category: "DEFAULT",
        method: "default",
        confidence: 0.3,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 检查是否需要API推理
   * @param {string} message - 用户消息
   * @returns {boolean} 是否需要API推理
   */
  needsAPIInference(message) {
    // 复杂模式检测 - 更精确的模式
    const complexPatterns = [
      /我是.+工程师.+年经验/, // 复合信息: "我是前端工程师，3年经验"
      /想找.+公司.+职位/, // 复合需求: "想找大厂的前端职位"
      /薪资.+技术栈/, // 多重询问: "薪资多少，需要什么技术栈"
      /[，。；！？].+[，。；！？]/, // 多句话
      /.+年经验.+薪资/, // 经验+薪资组合
      /.+大厂.+职位/, // 公司类型+职位组合
    ];

    // 长消息检测 - 降低阈值
    if (message.length > 30) {
      return true;
    }

    // 多个关键词组合检测
    const keywordCount = [
      /工程师|开发|技术/,
      /年|经验|工作/,
      /薪资|工资|待遇/,
      /公司|大厂|创业/,
      /职位|岗位|工作/,
    ].filter((pattern) => pattern.test(message)).length;

    if (keywordCount >= 3) {
      return true;
    }

    // 复杂模式检测
    return complexPatterns.some((pattern) => pattern.test(message));
  }

  /**
   * API推理回复（简化版）
   * @param {string} message - 用户消息
   * @returns {Object} 回复对象
   */
  getAPIResponse(message) {
    // 这里可以调用真实的API，现在先用简化逻辑
    console.log(`🤖 API推理处理: "${message}"`);

    // 简单的关键词权重分析
    const weights = {
      greeting: 0,
      job: 0,
      tech: 0,
      salary: 0,
    };

    const lowerMessage = message.toLowerCase();

    // 计算权重
    if (/你好|hello|hi/.test(lowerMessage)) weights.greeting += 2;
    if (/职位|工作|招聘|岗位/.test(lowerMessage)) weights.job += 3;
    if (/技术|开发|编程/.test(lowerMessage)) weights.tech += 2;
    if (/薪资|工资|待遇/.test(lowerMessage)) weights.salary += 2;

    // 找到最高权重
    const maxWeight = Math.max(...Object.values(weights));
    const topCategory = Object.keys(weights).find(
      (key) => weights[key] === maxWeight
    );

    // 根据分析结果返回回复
    switch (topCategory) {
      case "job":
        return {
          type: "first_job_inquiry_api",
          content:
            "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。根据您的描述，我来为您推荐合适的职位。",
          suggestions: [
            "告诉我具体技术栈",
            "我想了解薪资范围",
            "推荐大厂职位",
            "推荐创业公司",
          ],
          metadata: {
            isFirstResponse: true,
            category: "JOB_INQUIRY_API",
            method: "api_inference",
            confidence: 0.8,
            weights: weights,
            timestamp: new Date().toISOString(),
          },
        };

      default:
        return {
          type: "first_greeting_api",
          content:
            "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。请告诉我您的具体需求？",
          suggestions: ["我想找工作", "了解薪资行情", "技术栈匹配", "公司推荐"],
          metadata: {
            isFirstResponse: true,
            category: "GREETING_API",
            method: "api_inference",
            confidence: 0.6,
            weights: weights,
            timestamp: new Date().toISOString(),
          },
        };
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalRules = this.classificationRules.length;
    const totalKeywords = this.classificationRules.reduce(
      (sum, rule) => sum + rule.keywords.length,
      0
    );
    const categories = this.classificationRules.map((rule) => rule.category);

    return {
      totalRules,
      totalKeywords,
      categories,
      hasAPIFallback: true,
    };
  }
}

module.exports = FirstMessageClassifier;
