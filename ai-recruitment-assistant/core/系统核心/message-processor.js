/**
 * AI招聘助手系统 - 消息处理器（主路由）
 *
 * 核心职责：
 * - 消息路由和意图识别
 * - 业务流程编排
 * - 上下文管理
 * - 错误处理和回退机制
 *
 * 预计代码量：2000行
 */

const { v4: uuidv4 } = require("uuid");

// 导入业务模块
const AIServices = require("../数据管理/ai-services");
const PassiveRecommender = require("../业务服务/passive-recommender");
const ActiveRecommender = require("../业务服务/active-recommender");
const TechMapper = require("../业务服务/tech-mapper");
const Utilities = require("../工具库/utilities");
const Validators = require("../工具库/validators");

class MessageProcessor {
  constructor({ database, userManager, config }) {
    this.database = database;
    this.userManager = userManager;
    this.config = config;

    // 业务模块
    this.aiServices = null;
    this.passiveRecommender = null;
    this.activeRecommender = null;
    this.techMapper = null;

    // 工具模块
    this.utilities = new Utilities();
    this.validators = new Validators();

    // 意图类型定义
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      UNKNOWN: "unknown",
    };

    // 上下文状态
    this.contextStates = {
      INITIAL: "initial",
      PROFILE_BUILDING: "profile_building",
      JOB_SEARCHING: "job_searching",
      RECOMMENDATION_VIEWING: "recommendation_viewing",
      CLARIFICATION_NEEDED: "clarification_needed",
    };
  }

  /**
   * 初始化消息处理器
   */
  async initialize() {
    try {
      // 初始化AI服务
      this.aiServices = new AIServices(this.config.getAIConfig());
      await this.aiServices.initialize();

      // 初始化推荐引擎
      this.passiveRecommender = new PassiveRecommender(
        this.database,
        this.config
      );
      await this.passiveRecommender.initialize();

      this.activeRecommender = new ActiveRecommender(
        this.database,
        this.config
      );
      await this.activeRecommender.initialize();

      // 初始化技术映射器
      this.techMapper = new TechMapper(this.database, this.config);
      await this.techMapper.initialize();

      console.log("🧠 消息处理器初始化完成");
    } catch (error) {
      console.error("❌ 消息处理器初始化失败:", error);
      throw error;
    }
  }

  /**
   * 处理用户消息 - 主入口
   */
  async processMessage(messageData) {
    try {
      // 1. 验证输入数据
      const validatedData = this.validators.validateMessageInput(messageData);

      // 2. 检查是否是初始化请求
      if (validatedData.message === "__INIT__") {
        return await this.handleInitRequest(validatedData);
      }

      // 3. 获取或创建会话
      const session = await this.getOrCreateSession(validatedData);

      // 4. 检查是否是第一条用户消息（在保存之前检查）
      const isFirstUserMessage = await this.isFirstUserMessage(session.id);
      console.log(
        `🔍 主流程检查第一句消息 - 会话ID: ${session.id}, 结果: ${isFirstUserMessage}`
      );

      // 5. 保存用户消息
      await this.saveUserMessage(session.id, validatedData.message);
      console.log(`💾 用户消息已保存 - 会话ID: ${session.id}`);

      // 6. 分析用户意图
      const intent = await this.analyzeUserIntent(
        validatedData.message,
        session,
        isFirstUserMessage
      );

      // 6. 根据意图路由到相应处理器
      const response = await this.routeToHandler(
        intent,
        validatedData,
        session,
        isFirstUserMessage
      );

      // 6. 保存助手回复
      await this.saveAssistantMessage(session.id, response);

      // 7. 更新会话状态
      await this.updateSessionContext(session.id, intent, response);

      console.log(
        `🔍 最终返回的响应 - 类型: ${response.type}, 内容开头: ${response.content?.substring(0, 30)}...`
      );

      return {
        success: true,
        sessionId: session.session_uuid,
        response: response,
        intent: intent.type,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 消息处理失败:", error);

      // 返回错误回退响应
      return {
        success: false,
        error: error.message,
        response: this.getErrorFallbackResponse(),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 处理初始化请求
   */
  async handleInitRequest(messageData) {
    try {
      // 获取或创建会话
      const session = await this.getOrCreateSession(messageData);

      // 返回开场白
      return {
        success: true,
        sessionId: session.session_uuid,
        response: {
          type: "welcome",
          content: `您好，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`,
          metadata: {
            isWelcome: true,
            timestamp: new Date().toISOString(),
          },
        },
        intent: "welcome",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 处理初始化请求失败:", error);
      return {
        success: false,
        error: "初始化失败",
        response: {
          type: "error",
          content: "抱歉，系统初始化失败，请刷新页面重试。",
        },
      };
    }
  }

  /**
   * 获取或创建会话（支持跨设备同步）
   */
  async getOrCreateSession(messageData) {
    try {
      let session = null;

      // 如果提供了sessionId，尝试获取现有会话
      if (messageData.sessionId) {
        // 检查是否是加密token（长度超过36的通常是加密token）
        if (messageData.sessionId.length > 36) {
          // 尝试解密token获取用户信息
          const utilities = require("../工具库/utilities");
          const util = new utilities();
          const tokenData = util.parseSessionToken(messageData.sessionId);

          if (tokenData && tokenData.email) {
            // 使用token中的email查找用户的活跃会话
            const user = await this.userManager.getOrCreateUser(
              tokenData.email
            );
            session = await this.findActiveUserSession(user.id);
          }
        } else {
          // 普通sessionId，直接查询
          session = await this.database.getSessionByUuid(messageData.sessionId);
        }

        // 检查会话是否过期（从配置读取天数）
        if (session) {
          const sessionTimeoutDays =
            this.config.getBusinessConfig().sessionTimeout || 14;
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - sessionTimeoutDays);

          if (new Date(session.last_active_at) < cutoffDate) {
            // 会话已过期，清理并创建新会话
            await this.cleanExpiredSession(session);
            session = null;
          }
        }
      }

      // 如果没有找到会话，创建新会话
      if (!session) {
        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(
          messageData.userEmail
        );

        // 检查用户是否有其他活跃会话（跨设备同步）
        // 暂时禁用跨设备同步来测试第一句回复
        // const activeSession = await this.findActiveUserSession(user.id);

        // if (activeSession) {
        //   // 使用现有活跃会话实现跨设备同步
        //   session = activeSession;
        // } else {
        // 创建新会话 - 始终使用新的UUID，不使用加密token作为sessionUuid
        session = await this.database.createChatSession({
          userId: user.id,
          sessionUuid: uuidv4(),
          entrySourceUrl: messageData.entrySource || "",
          initialIntent: "",
          context: {
            state: this.contextStates.INITIAL,
            lastIntent: null,
            profileCompleteness: 0,
          },
        });

        // 发送开场白
        await this.sendWelcomeMessage(session.id, user.name || "");
        // }
      }

      return session;
    } catch (error) {
      console.error("❌ 获取或创建会话失败:", error);
      throw error;
    }
  }

  /**
   * 查找用户的活跃会话（跨设备同步）
   */
  async findActiveUserSession(userId) {
    try {
      const sessionTimeoutDays =
        this.config.getBusinessConfig().sessionTimeout || 14;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - sessionTimeoutDays);

      const { data: sessions, error } = await this.database.client
        .from("chat_sessions")
        .select("*")
        .eq("user_id", userId)
        .gte("last_active_at", cutoffDate.toISOString())
        .order("last_active_at", { ascending: false })
        .limit(1);

      if (error) throw error;

      return sessions && sessions.length > 0 ? sessions[0] : null;
    } catch (error) {
      console.error("❌ 查找活跃会话失败:", error);
      return null;
    }
  }

  /**
   * 清理过期会话
   */
  async cleanExpiredSession(session) {
    try {
      // 删除会话相关消息
      await this.database.client
        .from("chat_messages")
        .delete()
        .eq("session_id", session.id);

      // 删除会话
      await this.database.client
        .from("chat_sessions")
        .delete()
        .eq("id", session.id);

      console.log(`🗑️ 清理过期会话: ${session.session_uuid}`);
    } catch (error) {
      console.error("❌ 清理过期会话失败:", error);
    }
  }

  /**
   * 检查是否是用户第一次发言
   */
  async isFirstUserMessage(sessionId) {
    try {
      const { data: messages, error } = await this.database.client
        .from("chat_messages")
        .select("id")
        .eq("session_id", sessionId)
        .eq("message_type", "user")
        .limit(1);

      if (error) throw error;

      // 如果没有用户消息记录，说明这是第一次
      const isFirst = !messages || messages.length === 0;
      console.log(
        `🔍 检查第一句用户消息 - 会话ID: ${sessionId}, 用户消息数: ${messages?.length || 0}, 是否第一句: ${isFirst}`
      );
      return isFirst;
    } catch (error) {
      console.error("❌ 检查用户消息历史失败:", error);
      return false;
    }
  }

  /**
   * 分析第一句用户消息（三层处理逻辑）
   */
  async analyzeFirstUserMessage(message, _session) {
    try {
      // 第一层：关键词快速匹配
      const quickMatch = this.checkQuickPatterns(message);
      if (quickMatch) {
        return {
          type: quickMatch.type,
          confidence: 0.9,
          source: "quick_pattern",
          needsFollowUp: true,
          extractedInfo: {},
          messageCategory: quickMatch.category,
        };
      }

      // 第二层：信息提取处理
      const extractedInfo = await this.extractUserInfo(message);
      if (extractedInfo.hasInfo) {
        return {
          type: this.intentTypes.PROFILE_UPDATE,
          confidence: 0.8,
          source: "info_extraction",
          needsFollowUp: true,
          extractedInfo: extractedInfo,
          messageCategory: "info_providing",
        };
      }

      // 第三层：默认处理
      return {
        type: this.intentTypes.UNKNOWN,
        confidence: 0.3,
        source: "default",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: "other",
      };
    } catch (error) {
      console.error("❌ 分析第一句消息失败:", error);
      return {
        type: this.intentTypes.UNKNOWN,
        confidence: 0.1,
        source: "error",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: "error",
      };
    }
  }

  /**
   * 第一层：关键词快速匹配
   */
  checkQuickPatterns(message) {
    const lowerMessage = message.toLowerCase().trim();

    // 问候类关键词 - 返回完整开场白
    const greetingKeywords = ["你好", "hi", "hello", "哈喽", "在的", "在吗"];
    if (greetingKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return {
        type: this.intentTypes.GREETING,
        category: "greeting",
      };
    }

    // 职位询问类关键词 - 返回"我们合作的公司挺多的..."
    const jobKeywords = [
      "职位",
      "工作",
      "机会",
      "公司",
      "推荐",
      "介绍",
      "什么职位",
    ];
    if (jobKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        category: "job_inquiry",
      };
    }

    // 身份确认类关键词 - 返回完整开场白
    const identityKeywords = ["你是谁", "ai", "机器人", "真人", "人工"];
    if (identityKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return {
        type: this.intentTypes.GREETING,
        category: "identity_check",
      };
    }

    return null;
  }

  /**
   * 第二层：信息提取处理
   */
  async extractUserInfo(message) {
    try {
      const extractedInfo = {
        company: null,
        techStack: null,
        level: null,
        salary: null,
        hasInfo: false,
      };

      // 提取公司信息（基于数据库companies表）
      extractedInfo.company = await this.extractCompanyFromMessage(message);

      // 提取技术栈信息（基于数据库tech_tree表）
      extractedInfo.techStack = await this.extractTechFromMessage(message);

      // 提取职级信息
      extractedInfo.level = this.extractLevelFromMessage(message);

      // 提取薪资信息
      extractedInfo.salary = this.extractSalaryFromMessage(message);

      // 检查是否提取到任何信息
      extractedInfo.hasInfo = !!(
        extractedInfo.company ||
        extractedInfo.techStack ||
        extractedInfo.level ||
        extractedInfo.salary
      );

      return extractedInfo;
    } catch (error) {
      console.error("❌ 信息提取失败:", error);
      return { hasInfo: false };
    }
  }

  /**
   * 分析用户意图
   */
  async analyzeUserIntent(message, session, isFirstUserMessage = null) {
    try {
      // 如果没有传入isFirstUserMessage，则检查
      if (isFirstUserMessage === null) {
        isFirstUserMessage = await this.isFirstUserMessage(session.id);
      }

      if (isFirstUserMessage) {
        // 第一句回复使用三层处理逻辑
        return await this.analyzeFirstUserMessage(message, session);
      }

      // 使用AI服务分析意图
      const aiAnalysis = await this.aiServices.analyzeUserIntent(message, {
        sessionContext: session.current_interaction_context,
        messageHistory: await this.getRecentMessages(session.id, 5),
      });

      // 结合规则引擎进行意图识别
      const ruleBasedIntent = this.identifyIntentByRules(message);

      // 合并分析结果
      const finalIntent = this.mergeIntentAnalysis(aiAnalysis, ruleBasedIntent);

      return {
        type: finalIntent.type,
        confidence: finalIntent.confidence,
        entities: finalIntent.entities || {},
        context: finalIntent.context || {},
      };
    } catch (error) {
      console.error("❌ 意图分析失败:", error);

      // 回退到规则引擎
      return this.identifyIntentByRules(message);
    }
  }

  /**
   * 基于规则的意图识别
   */
  identifyIntentByRules(message) {
    const lowerMessage = message.toLowerCase();

    // 问候语识别
    if (
      this.utilities.containsAny(lowerMessage, ["你好", "hello", "嗨", "开始"])
    ) {
      return {
        type: this.intentTypes.GREETING,
        confidence: 0.9,
        entities: {},
      };
    }

    // 职位搜索识别
    if (
      this.utilities.containsAny(lowerMessage, [
        "找工作",
        "职位",
        "岗位",
        "招聘",
      ])
    ) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        confidence: 0.8,
        entities: {},
      };
    }

    // 推荐请求识别
    if (this.utilities.containsAny(lowerMessage, ["推荐", "建议", "合适的"])) {
      return {
        type: this.intentTypes.RECOMMENDATION_REQUEST,
        confidence: 0.8,
        entities: {},
      };
    }

    // 技术方向询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "技术",
        "开发",
        "前端",
        "后端",
        "算法",
      ])
    ) {
      return {
        type: this.intentTypes.TECH_DIRECTION_INQUIRY,
        confidence: 0.7,
        entities: {},
      };
    }

    // 薪资询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "薪资",
        "工资",
        "薪水",
        "待遇",
        "收入",
      ])
    ) {
      return {
        type: this.intentTypes.SALARY_INQUIRY,
        confidence: 0.8,
        entities: {},
      };
    }

    // 默认未知意图
    return {
      type: this.intentTypes.UNKNOWN,
      confidence: 0.3,
      entities: {},
    };
  }

  /**
   * 合并AI和规则引擎的意图分析结果
   */
  mergeIntentAnalysis(aiAnalysis, ruleAnalysis) {
    // 如果AI分析置信度高，优先使用AI结果
    if (aiAnalysis.confidence > 0.8) {
      return aiAnalysis;
    }

    // 如果规则引擎置信度高，使用规则结果
    if (ruleAnalysis.confidence > 0.7) {
      return ruleAnalysis;
    }

    // 否则选择置信度更高的结果
    return aiAnalysis.confidence >= ruleAnalysis.confidence
      ? aiAnalysis
      : ruleAnalysis;
  }

  /**
   * 根据意图路由到相应处理器
   */
  async routeToHandler(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      switch (intent.type) {
        case this.intentTypes.GREETING:
          return await this.handleGreeting(
            intent,
            messageData,
            session,
            isFirstUserMessage
          );

        case this.intentTypes.PROFILE_UPDATE:
          return await this.handleProfileUpdate(intent, messageData, session);

        case this.intentTypes.JOB_SEARCH:
          return await this.handleJobSearch(
            intent,
            messageData,
            session,
            isFirstUserMessage
          );

        case this.intentTypes.RECOMMENDATION_REQUEST:
          return await this.handleRecommendationRequest(
            intent,
            messageData,
            session
          );

        case this.intentTypes.TECH_DIRECTION_INQUIRY:
          return await this.handleTechDirectionInquiry(
            intent,
            messageData,
            session
          );

        case this.intentTypes.SALARY_INQUIRY:
          return await this.handleSalaryInquiry(intent, messageData, session);

        case this.intentTypes.COMPANY_INQUIRY:
          return await this.handleCompanyInquiry(intent, messageData, session);

        default:
          return await this.handleUnknownIntent(messageData, session);
      }
    } catch (error) {
      console.error("❌ 意图处理失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 处理问候语
   */
  async handleGreeting(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      console.log(
        `🎯 handleGreeting 开始 - 会话ID: ${session.id}, isFirstUserMessage参数: ${isFirstUserMessage}`
      );

      // 如果没有传入isFirstUserMessage，则检查
      if (isFirstUserMessage === null) {
        isFirstUserMessage = await this.isFirstUserMessage(session.id);
        console.log(
          `🔍 handleGreeting 重新检查第一句 - 会话ID: ${session.id}, 结果: ${isFirstUserMessage}`
        );
      }

      if (isFirstUserMessage) {
        console.log(`✅ 识别为第一句消息，调用 handleFirstUserGreeting`);
        // 第一句回复的特殊处理
        return await this.handleFirstUserGreeting(messageData, session, intent);
      }

      console.log(`❌ 识别为非第一句消息，使用常规处理逻辑`);

      // 非第一句回复：检查用户档案完整性
      const user = await this.userManager.getUserBySession(session);
      const profile = await this.database.getCandidateProfile(user.id);

      if (!profile) {
        return {
          type: "greeting_new_user",
          content:
            "您好！我需要了解一些基本信息来为您推荐合适的职位。请告诉我您的技术方向和工作经验？",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想了解更多",
          ],
        };
      } else {
        return {
          type: "greeting_returning_user",
          content: `欢迎回来！我记得您是${profile.candidate_tech_direction_raw || "技术"}方向的。今天想要什么帮助吗？`,
          suggestions: [
            "推荐职位给我",
            "更新我的信息",
            "查看薪资行情",
            "了解公司信息",
          ],
        };
      }
    } catch (error) {
      console.error("❌ 处理问候语失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 处理第一句用户回复 - 用户在Katrina开场白后的回复
   */
  async handleFirstUserGreeting(messageData, session, intent) {
    try {
      // 根据消息类型返回不同的第一句回复 - 4种场景
      const messageCategory = this.classifyUserFirstReply(messageData.message);
      console.log(`🎭 handleFirstUserGreeting - 消息类别: ${messageCategory}`);

      switch (messageCategory) {
        case "GREETING":
          // 1. 问候类（你好，hi，嗨，哈喽，在的，在，等等）
          return {
            type: "user_first_greeting",
            content: "您有兴趣看看新机会吗？我这边合作的客户职位还挺多的。",
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_greeting",
              messageCategory: messageCategory,
            },
            suggestions: ["有兴趣了解", "我想看看", "暂时不考虑", "先了解一下"],
          };

        case "DIRECT_INQUIRY":
          // 2. 直接咨询类（你有什么职位推荐，有什么介绍，有什么公司，有什么推荐等等）
          return {
            type: "user_first_direct_inquiry",
            content:
              "我们合作的公司挺多的，大厂、中厂、创业、国企公司都有，职位也不少。",
            followUpContent:
              "您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。",
            followUpDelay: 2000, // 2秒延迟，前端实现
            hasFollowUp: true,
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_direct_inquiry",
              messageCategory: messageCategory,
            },
            suggestions: [
              "我是前端开发工程师",
              "我做后端开发",
              "我是全栈工程师",
              "我想了解更多",
            ],
          };

        case "DOUBT_AI":
          // 3. 疑惑类（你是AI吗？你是AI？AI？机器人？等等）
          return {
            type: "user_first_doubt_ai",
            content:
              "我是Felton团队创造的AI猎头顾问，我会为您针对性的提供合适的职位。",
            followUpContent: "请问您最近有兴趣看看新机会吗？",
            followUpDelay: 2000, // 2秒延迟，前端实现
            hasFollowUp: true,
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_doubt_ai",
              messageCategory: messageCategory,
            },
            suggestions: ["有兴趣了解", "我想看看", "暂时不考虑", "先了解一下"],
          };

        case "API_INFERENCE":
        default:
          // 4. 不是上面三项的问题，调用API进行推理
          return await this.handleFirstUserAPIInference(
            messageData,
            session,
            intent
          );
      }
    } catch (error) {
      console.error("❌ 处理第一句问候失败:", error);
      return this.getDefaultFirstResponse();
    }
  }

  /**
   * 分类用户第一句回复
   */
  classifyUserFirstReply(message) {
    const lowerMessage = message.toLowerCase().trim();

    // 1. 问候类
    const greetingKeywords = [
      "你好",
      "hi",
      "hello",
      "嗨",
      "哈喽",
      "在的",
      "在",
      "您好",
      "hey",
      "嗯",
    ];
    if (greetingKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return "GREETING";
    }

    // 2. 直接咨询类
    const inquiryKeywords = [
      "职位推荐",
      "有什么介绍",
      "有什么公司",
      "有什么推荐",
      "职位",
      "工作",
      "招聘",
      "岗位",
      "机会",
      "有什么",
      "推荐",
      "什么职位",
      "找工作",
      "介绍",
      "公司",
      "什么工作",
      "工作机会",
      "有哪些",
      "都有什么",
      "推荐一下",
      "介绍一下",
    ];
    if (inquiryKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return "DIRECT_INQUIRY";
    }

    // 3. 疑惑类
    const doubtKeywords = [
      "你是ai吗",
      "你是ai",
      "ai",
      "机器人",
      "人工智能",
      "真人",
      "人工",
      "什么系统",
      "谁在说话",
      "是机器人吗",
      "你是真人吗",
      "是人工智能吗",
      "智能助手",
      "虚拟助手",
    ];
    if (doubtKeywords.some((keyword) => lowerMessage.includes(keyword))) {
      return "DOUBT_AI";
    }

    // 4. 其他情况需要API推理
    return "API_INFERENCE";
  }

  /**
   * 处理需要API推理的第一句回复
   */
  async handleFirstUserAPIInference(messageData, session, intent) {
    try {
      console.log(`🤖 API推理处理用户第一句回复: "${messageData.message}"`);

      // 简单的关键词权重分析
      const message = messageData.message.toLowerCase();
      const weights = {
        greeting: 0,
        inquiry: 0,
        doubt: 0,
        tech: 0,
        salary: 0,
      };

      // 计算权重
      if (/你好|hello|hi|嗨/.test(message)) weights.greeting += 2;
      if (/职位|工作|招聘|推荐/.test(message)) weights.inquiry += 3;
      if (/ai|机器人|真人/.test(message)) weights.doubt += 3;
      if (/技术|开发|编程/.test(message)) weights.tech += 2;
      if (/薪资|工资|待遇/.test(message)) weights.salary += 2;

      // 找到最高权重
      const maxWeight = Math.max(...Object.values(weights));
      const topCategory = Object.keys(weights).find(
        (key) => weights[key] === maxWeight
      );

      // 根据分析结果返回回复
      switch (topCategory) {
        case "inquiry":
          return {
            type: "user_first_api_inquiry",
            content:
              "我们合作的公司挺多的，大厂、中厂、创业、国企公司都有，职位也不少。根据您的描述，我来为您详细介绍。",
            followUpContent:
              "您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。",
            followUpDelay: 2000,
            hasFollowUp: true,
            suggestions: [
              "告诉我具体需求",
              "我想了解薪资",
              "推荐大厂职位",
              "了解技术要求",
            ],
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_api_inquiry",
              messageCategory: "API_INQUIRY",
              weights: weights,
            },
          };

        case "doubt":
          return {
            type: "user_first_api_doubt",
            content:
              "我是Felton团队创造的AI猎头顾问，我会为您针对性的提供合适的职位。",
            followUpContent: "请问您最近有兴趣看看新机会吗？",
            followUpDelay: 2000,
            hasFollowUp: true,
            suggestions: ["有兴趣了解", "我想看看", "暂时不考虑", "先了解一下"],
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_api_doubt",
              messageCategory: "API_DOUBT",
              weights: weights,
            },
          };

        default:
          return {
            type: "user_first_api_general",
            content:
              "我理解您的需求。我是专业的AI猎头顾问，可以为您提供个性化的职位推荐。",
            followUpContent:
              "请告诉我您的技术方向和期望，我来为您匹配合适的机会。",
            followUpDelay: 2000,
            hasFollowUp: true,
            suggestions: [
              "我想找工作",
              "了解薪资行情",
              "技术栈匹配",
              "公司推荐",
            ],
            metadata: {
              isFirstResponse: true,
              responseSource: "user_first_api_general",
              messageCategory: "API_GENERAL",
              weights: weights,
            },
          };
      }
    } catch (error) {
      console.error("❌ API推理失败:", error);
      return {
        type: "user_first_api_error",
        content: "我理解您的需求，让我为您提供帮助。",
        suggestions: ["我想找工作", "了解职位信息", "技术栈匹配", "薪资咨询"],
        metadata: {
          isFirstResponse: true,
          responseSource: "user_first_api_error",
          messageCategory: "API_ERROR",
        },
      };
    }
  }

  /**
   * 处理第一句信息提供类消息
   */
  async handleFirstInfoProviding(_messageData, _session, intent) {
    try {
      const extractedInfo = intent.extractedInfo || {};
      let confirmationText = "我了解到您";

      // 构建确认信息的文本
      const infoItems = [];
      if (extractedInfo.techStack && extractedInfo.techStack.length > 0) {
        infoItems.push(`技术栈是${extractedInfo.techStack.join("、")}`);
      }
      if (extractedInfo.experience) {
        infoItems.push(`有${extractedInfo.experience}年经验`);
      }
      if (extractedInfo.currentCompany) {
        infoItems.push(`目前在${extractedInfo.currentCompany}工作`);
      }

      if (infoItems.length > 0) {
        confirmationText += infoItems.join("，") + "。";
      } else {
        confirmationText = "感谢您提供的信息。";
      }

      return {
        type: "first_info_confirmation",
        content:
          confirmationText +
          "为了给您推荐更合适的职位，能否再告诉我您的期望薪酬和工作地点偏好？",
        metadata: {
          isFirstResponse: true,
          needsFollowUp: true,
          responseSource: "first_info_providing",
          extractedInfo: extractedInfo,
        },
        suggestions: [
          "我期望薪酬在30-50k",
          "我想在北京工作",
          "我对薪酬比较灵活",
          "我想了解更多职位",
        ],
      };
    } catch (error) {
      console.error("❌ 处理第一句信息提供失败:", error);
      return this.getDefaultFirstResponse();
    }
  }

  /**
   * 处理第一句其他类型消息（调用AI推理）
   */
  async handleFirstOtherMessage(messageData, session, intent) {
    try {
      console.log(
        `🤖 handleFirstOtherMessage 被调用 - 消息: ${messageData.message}, 类别: ${intent.messageCategory}`
      );

      // 调用AI服务进行推理
      const aiResponse = await this.aiServices.generateResponse(
        `用户第一次发送消息："${messageData.message}"，请作为AI招聘助手Katrina回复。如果用户的消息比较模糊或不清楚，可以说"您说的我没太明白，这是啥意思？不过没关系"然后引导到职位推荐话题。`,
        {
          isFirstMessage: true,
          sessionContext: session.current_interaction_context,
        }
      );

      console.log(`🤖 AI服务返回内容: ${aiResponse?.substring(0, 50)}...`);

      return {
        type: "first_ai_response",
        content: aiResponse,
        metadata: {
          isFirstResponse: true,
          needsFollowUp: true,
          responseSource: "ai_inference",
          messageCategory: intent.messageCategory,
        },
        suggestions: [
          "我想了解职位机会",
          "我是技术开发人员",
          "有什么好的推荐吗",
          "我想上传简历",
        ],
      };
    } catch (error) {
      console.error("❌ AI推理第一句回复失败:", error);
      return this.getDefaultFirstResponse();
    }
  }

  /**
   * 获取默认第一次回复
   */
  getDefaultFirstResponse() {
    return {
      type: "first_greeting_default",
      content:
        "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。",
      metadata: {
        isFirstResponse: true,
        needsFollowUp: true,
        responseSource: "default_first_message",
        followUpMessage: "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？",
      },
      suggestions: [
        "我是前端开发工程师",
        "我做后端开发",
        "我是全栈工程师",
        "我想上传简历",
      ],
    };
  }

  /**
   * 发送开场白消息
   */
  async sendWelcomeMessage(sessionId, userName) {
    try {
      const welcomeText = userName
        ? `您好${userName}，我是AI领域的猎头Katrina，专注于AI算法职位。`
        : `您好，我是AI领域的猎头Katrina，专注于AI算法职位。`;

      await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "assistant",
        content: welcomeText,
        metadata: {
          type: "welcome",
          userName: userName,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 发送开场白失败:", error);
    }
  }

  /**
   * 获取最近消息
   */
  async getRecentMessages(sessionId, limit = 10) {
    try {
      return await this.database.getSessionMessages(sessionId, limit);
    } catch (error) {
      console.error("❌ 获取最近消息失败:", error);
      return [];
    }
  }

  /**
   * 保存用户消息
   */
  async saveUserMessage(sessionId, message) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "user",
        content: message,
        metadata: {
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 保存用户消息失败:", error);
    }
  }

  /**
   * 保存助手消息
   */
  async saveAssistantMessage(sessionId, response) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "assistant",
        content: typeof response === "string" ? response : response.content,
        metadata: {
          responseType: response.type || "text",
          suggestions: response.suggestions || [],
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 保存助手消息失败:", error);
    }
  }

  /**
   * 更新会话上下文
   */
  async updateSessionContext(sessionId, intent, response) {
    try {
      const newContext = {
        lastIntent: intent.type,
        lastResponse: response.type || "text",
        timestamp: new Date().toISOString(),
      };

      await this.database.updateSessionActivity(sessionId, newContext);
    } catch (error) {
      console.error("❌ 更新会话上下文失败:", error);
    }
  }

  /**
   * 获取错误回退响应
   */
  getErrorFallbackResponse() {
    return {
      type: "error_fallback",
      content:
        "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。",
      suggestions: ["重新开始", "联系客服", "查看帮助"],
    };
  }

  // 占位符方法 - 将在后续实现
  async handleProfileUpdate(_intent, _messageData, _session) {
    return { type: "placeholder", content: "档案更新功能开发中..." };
  }

  async handleJobSearch(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      // 如果没有传入isFirstUserMessage，则检查
      if (isFirstUserMessage === null) {
        isFirstUserMessage = await this.isFirstUserMessage(session.id);
      }

      if (isFirstUserMessage) {
        // 第一句职位询问的特殊处理
        return await this.handleFirstUserGreeting(messageData, session, intent);
      }

      // 非第一句的职位搜索处理
      return { type: "placeholder", content: "职位搜索功能开发中..." };
    } catch (error) {
      console.error("❌ 处理职位搜索失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  async handleRecommendationRequest(intent, messageData, session) {
    return { type: "placeholder", content: "推荐功能开发中..." };
  }

  async handleTechDirectionInquiry(intent, messageData, session) {
    return { type: "placeholder", content: "技术方向询问功能开发中..." };
  }

  async handleSalaryInquiry(intent, messageData, session) {
    return { type: "placeholder", content: "薪资询问功能开发中..." };
  }

  async handleCompanyInquiry(intent, messageData, session) {
    return { type: "placeholder", content: "公司询问功能开发中..." };
  }

  async handleUnknownIntent(messageData, session) {
    return {
      type: "unknown_intent",
      content:
        "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：",
      suggestions: [
        "我想找工作",
        "推荐职位给我",
        "更新个人信息",
        "了解薪资行情",
      ],
    };
  }

  // ==================== 信息提取方法 ====================

  /**
   * 从消息中提取公司信息（基于数据库companies表）
   */
  async extractCompanyFromMessage(message) {
    try {
      // 获取数据库中的公司列表
      const { data: companies, error } = await this.database.client
        .from("companies")
        .select("company_name");

      if (error) throw error;

      if (!companies || companies.length === 0) {
        return null;
      }

      const lowerMessage = message.toLowerCase();

      // 查找匹配的公司
      for (const company of companies) {
        const companyName = company.company_name.toLowerCase();
        if (lowerMessage.includes(companyName)) {
          return company.company_name;
        }

        // 检查常见简称
        const aliases = this.getCompanyAliases(company.company_name);
        for (const alias of aliases) {
          if (lowerMessage.includes(alias.toLowerCase())) {
            return company.company_name;
          }
        }
      }

      return null;
    } catch (error) {
      console.error("❌ 提取公司信息失败:", error);
      return null;
    }
  }

  /**
   * 从消息中提取技术栈信息（基于数据库tech_tree表）
   */
  async extractTechFromMessage(message) {
    try {
      // 获取数据库中的技术树
      const { data: techTree, error } = await this.database.client
        .from("tech_tree")
        .select("tech_name, level");

      if (error) throw error;

      if (!techTree || techTree.length === 0) {
        return null;
      }

      const lowerMessage = message.toLowerCase();

      // 查找匹配的技术
      for (const tech of techTree) {
        const techName = tech.tech_name.toLowerCase();
        if (lowerMessage.includes(techName)) {
          return tech.tech_name;
        }
      }

      return null;
    } catch (error) {
      console.error("❌ 提取技术栈信息失败:", error);
      return null;
    }
  }

  /**
   * 从消息中提取职级信息
   */
  extractLevelFromMessage(message) {
    const lowerMessage = message.toLowerCase();

    // 常见职级关键词
    const levelKeywords = {
      p4: "P4",
      p5: "P5",
      p6: "P6",
      p7: "P7",
      p8: "P8",
      p9: "P9",
      t1: "T1",
      t2: "T2",
      t3: "T3",
      t4: "T4",
      t5: "T5",
      t6: "T6",
      "1-1": "1-1",
      "1-2": "1-2",
      "2-1": "2-1",
      "2-2": "2-2",
      "3-1": "3-1",
      初级: "初级",
      中级: "中级",
      高级: "高级",
      专家: "专家",
      架构师: "架构师",
    };

    for (const [keyword, level] of Object.entries(levelKeywords)) {
      if (lowerMessage.includes(keyword)) {
        return level;
      }
    }

    return null;
  }

  /**
   * 从消息中提取薪资信息
   */
  extractSalaryFromMessage(message) {
    // 薪资匹配正则
    const salaryPatterns = [
      /(\d+)k/gi,
      /(\d+)万/gi,
      /(\d+)-(\d+)k/gi,
      /(\d+)-(\d+)万/gi,
    ];

    for (const pattern of salaryPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return null;
  }

  /**
   * 获取公司常见别名
   */
  getCompanyAliases(companyName) {
    const aliases = {
      腾讯: ["鹅厂", "tx", "tencent"],
      阿里巴巴: ["阿里", "ali", "alibaba"],
      字节跳动: ["字节", "bd", "bytedance"],
      百度: ["百度", "baidu"],
      美团: ["美团", "meituan"],
      京东: ["京东", "jd"],
      亚马逊: ["亚麻", "amazon"],
      微软: ["微软", "microsoft", "ms"],
      谷歌: ["谷歌", "google"],
    };

    return aliases[companyName] || [];
  }
}

module.exports = MessageProcessor;
