/**
 * AI招聘助手系统 - 应用配置管理器
 * 
 * 核心职责：
 * - 系统配置管理
 * - 环境变量处理
 * - 配置验证
 * - 动态配置更新
 * 
 * 预计代码量：1000行
 */

const Joi = require('joi');

class AppConfig {
  constructor() {
    this.config = {};
    this.isInitialized = false;
  }

  /**
   * 初始化配置
   */
  async initialize() {
    try {
      // 加载环境变量
      this.loadEnvironmentVariables();
      
      // 验证配置
      this.validateConfiguration();
      
      // 设置默认值
      this.setDefaults();
      
      this.isInitialized = true;
      console.log('📋 应用配置初始化完成');
      
    } catch (error) {
      console.error('❌ 配置初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载环境变量
   */
  loadEnvironmentVariables() {
    this.config = {
      // 服务器配置
      server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || 'localhost',
        nodeEnv: process.env.NODE_ENV || 'development'
      },
      
      // 数据库配置
      database: {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 30000,
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS) || 10
      },
      
      // AI服务配置
      ai: {
        deepseekApiKey: process.env.DEEPSEEK_API_KEY,
        deepseekEndpoint: process.env.LLM_API_ENDPOINT || 'https://api.deepseek.com/v1',
        maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 2000,
        temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7,
        timeout: parseInt(process.env.AI_TIMEOUT) || 30000
      },
      
      // 缓存配置
      cache: {
        enabled: process.env.CACHE_ENABLED === 'true',
        ttl: parseInt(process.env.CACHE_TTL) || 3600,
        maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 1000
      },
      
      // 安全配置
      security: {
        jwtSecret: process.env.JWT_SECRET || 'default-secret-change-in-production',
        jwtExpiration: process.env.JWT_EXPIRATION || '24h',
        rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15分钟
        rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100,
        allowedOrigins: process.env.ALLOWED_ORIGINS ? 
          process.env.ALLOWED_ORIGINS.split(',') : 
          ['http://localhost:3000', 'http://localhost:3001']
      },
      
      // 性能配置
      performance: {
        requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000,
        maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
        compressionEnabled: process.env.COMPRESSION_ENABLED !== 'false'
      },
      
      // 日志配置
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined',
        enableConsole: process.env.LOG_CONSOLE !== 'false',
        enableFile: process.env.LOG_FILE === 'true',
        filePath: process.env.LOG_FILE_PATH || './logs/app.log'
      },
      
      // 业务配置
      business: {
        maxRecommendations: parseInt(process.env.MAX_RECOMMENDATIONS) || 10,
        sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 1800000, // 30分钟
        maxUploadSize: process.env.MAX_UPLOAD_SIZE || '5mb',
        supportedFileTypes: process.env.SUPPORTED_FILE_TYPES ? 
          process.env.SUPPORTED_FILE_TYPES.split(',') : 
          ['pdf', 'doc', 'docx', 'txt']
      }
    };
  }

  /**
   * 验证配置
   */
  validateConfiguration() {
    const schema = Joi.object({
      server: Joi.object({
        port: Joi.number().port().required(),
        host: Joi.string().required(),
        nodeEnv: Joi.string().valid('development', 'production', 'test').required()
      }).required(),
      
      database: Joi.object({
        supabaseUrl: Joi.string().uri().required(),
        supabaseKey: Joi.string().required(),
        connectionTimeout: Joi.number().positive(),
        maxConnections: Joi.number().positive()
      }).required(),
      
      ai: Joi.object({
        deepseekApiKey: Joi.string().required(),
        deepseekEndpoint: Joi.string().uri().required(),
        maxTokens: Joi.number().positive(),
        temperature: Joi.number().min(0).max(2),
        timeout: Joi.number().positive()
      }).required(),
      
      cache: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().positive(),
        maxSize: Joi.number().positive()
      }),
      
      security: Joi.object({
        jwtSecret: Joi.string().min(32).required(),
        jwtExpiration: Joi.string().required(),
        rateLimitWindow: Joi.number().positive(),
        rateLimitMax: Joi.number().positive(),
        allowedOrigins: Joi.array().items(Joi.string())
      }).required()
    });

    const { error } = schema.validate(this.config);
    if (error) {
      throw new Error(`配置验证失败: ${error.details[0].message}`);
    }
  }

  /**
   * 设置默认值
   */
  setDefaults() {
    // 开发环境特殊配置
    if (this.config.server.nodeEnv === 'development') {
      this.config.logging.level = 'debug';
      this.config.security.rateLimitMax = 1000; // 开发环境放宽限制
    }
    
    // 生产环境特殊配置
    if (this.config.server.nodeEnv === 'production') {
      this.config.logging.enableFile = true;
      this.config.cache.enabled = true;
    }
  }

  // Getter方法
  getServerPort() {
    return this.config.server.port;
  }

  getServerHost() {
    return this.config.server.host;
  }

  getNodeEnv() {
    return this.config.server.nodeEnv;
  }

  getDatabaseConfig() {
    return this.config.database;
  }

  getAIConfig() {
    return this.config.ai;
  }

  getCacheConfig() {
    return this.config.cache;
  }

  getSecurityConfig() {
    return this.config.security;
  }

  getPerformanceConfig() {
    return this.config.performance;
  }

  getLoggingConfig() {
    return this.config.logging;
  }

  getBusinessConfig() {
    return this.config.business;
  }

  getAllowedOrigins() {
    return this.config.security.allowedOrigins;
  }

  /**
   * 获取完整配置
   */
  getFullConfig() {
    return { ...this.config };
  }

  /**
   * 动态更新配置
   */
  updateConfig(path, value) {
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    console.log(`配置已更新: ${path} = ${value}`);
  }

  /**
   * 检查配置是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }
}

module.exports = AppConfig;
