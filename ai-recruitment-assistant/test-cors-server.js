// test-cors-server.js - 简单的CORS测试服务器
const express = require("express");
const cors = require("cors");

const app = express();
const PORT = 3000;

// CORS配置
app.use(
  cors({
    origin: [
      "http://localhost:8080",
      "http://localhost:3000",
      "http://localhost:3001",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json());

// 健康检查
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    message: "CORS测试服务器运行中",
    timestamp: new Date().toISOString(),
  });
});

// 聊天接口
app.post("/api/chat", (req, res) => {
  console.log("收到聊天请求 /api/chat:", req.body);

  res.json({
    success: true,
    sessionId: "test-session-123",
    response: {
      type: "first_greeting_full",
      content:
        "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。",
      metadata: {
        isFirstResponse: true,
        needsFollowUp: true,
        responseSource: "test_server",
      },
    },
  });
});

// 聊天消息接口 (前端实际请求的路径)
app.post("/api/chat/message", (req, res) => {
  console.log("收到聊天消息请求 /api/chat/message:", req.body);

  res.json({
    success: true,
    sessionId: "test-session-123",
    response: {
      type: "first_greeting_full",
      content:
        "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少。\n\n您可以直接告诉我您的技术栈、现在公司、职级、期望薪酬等信息，我来为您匹配合适的职位。\n\n如果您有简历，也可以直接上传，我会帮您分析。另外，如果需要发送职位详情，请提供您的邮箱地址。",
      metadata: {
        isFirstResponse: true,
        needsFollowUp: true,
        responseSource: "test_server",
      },
    },
  });
});

app.listen(PORT, () => {
  console.log(`🚀 CORS测试服务器启动成功！`);
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log(`🔧 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`💬 聊天接口: http://localhost:${PORT}/api/chat`);
});
