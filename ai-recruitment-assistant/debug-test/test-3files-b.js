
// test-3files-b.js - 缓存层
const MockDatabase = require('./test-3files-a');

class CacheLayer {
    constructor() {
        this.db = new MockDatabase();
        this.cache = new Map();
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
    
    async getValue(key) {
        console.log(`Cache: 查询 ${key}`);
        
        if (this.cache.has(key)) {
            this.cacheHits++;
            console.log(`Cache HIT: ${key}`);
            return this.cache.get(key);
        }
        
        this.cacheMisses++;
        console.log(`Cache MISS: ${key}`);
        
        const value = await this.db.get(key);
        if (value !== null) {
            this.cache.set(key, value);
        }
        
        return value;
    }
    
    async setValue(key, value) {
        console.log(`Cache: 设置 ${key} = ${value}`);
        
        await this.db.set(key, value);
        this.cache.set(key, value);
        
        return true;
    }
    
    getStats() {
        return {
            cacheHits: this.cacheHits,
            cacheMisses: this.cacheMisses,
            cacheSize: this.cache.size,
            dbStats: this.db.getStats()
        };
    }
}

module.exports = CacheLayer;
