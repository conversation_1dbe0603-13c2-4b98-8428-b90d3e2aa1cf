// test-10files-a.js - 基础组件
class BaseComponent {
    constructor(name) {
        this.name = name;
        this.id = `${name}-${Date.now()}`;
        this.status = 'initialized';
        this.dependencies = [];
        this.metrics = { calls: 0, errors: 0, lastCall: null };
    }
    
    addDependency(component) {
        this.dependencies.push(component);
        console.log(`BaseComponent: ${this.name} added dependency on ${component.name}`);
    }
    
    async call(method, data) {
        this.metrics.calls++;
        this.metrics.lastCall = Date.now();
        
        try {
            console.log(`BaseComponent: ${this.name}.${method}() called`);
            
            // Check dependencies
            for (const dep of this.dependencies) {
                if (dep.status !== 'running') {
                    throw new Error(`Dependency ${dep.name} is not running`);
                }
            }
            
            const result = await this.execute(method, data);
            return result;
            
        } catch (error) {
            this.metrics.errors++;
            console.error(`BaseComponent: ${this.name}.${method}() failed:`, error.message);
            throw error;
        }
    }
    
    async execute(method, data) {
        // Simulate work
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        return { success: true, method, data, component: this.name };
    }
    
    start() {
        this.status = 'running';
        console.log(`BaseComponent: ${this.name} started`);
    }
    
    stop() {
        this.status = 'stopped';
        console.log(`BaseComponent: ${this.name} stopped`);
    }
    
    getStats() {
        return {
            name: this.name,
            id: this.id,
            status: this.status,
            dependencies: this.dependencies.length,
            metrics: this.metrics
        };
    }
}

module.exports = BaseComponent;
