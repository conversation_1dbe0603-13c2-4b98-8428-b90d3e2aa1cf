// test-6files-a.js - 事件总线
class EventBus {
    constructor() {
        this.listeners = new Map();
        this.eventHistory = [];
        this.eventId = 0;
    }

    on(eventType, callback) {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }
        this.listeners.get(eventType).push(callback);
        console.log(`EventBus: Registered listener for ${eventType}`);
    }

    emit(eventType, data) {
        this.eventId++;
        const event = {
            id: this.eventId,
            type: eventType,
            data,
            timestamp: Date.now()
        };

        this.eventHistory.push(event);
        console.log(`EventBus: Emitting ${eventType} (ID: ${this.eventId})`);

        const listeners = this.listeners.get(eventType) || [];
        listeners.forEach(callback => {
            try {
                callback(event);
            } catch (error) {
                console.error(`EventBus: Error in listener for ${eventType}`, error);
            }
        });

        return event.id;
    }

    getStats() {
        return {
            totalEvents: this.eventHistory.length,
            listenerTypes: this.listeners.size,
            recentEvents: this.eventHistory.slice(-5)
        };
    }
}
module.exports = EventBus;