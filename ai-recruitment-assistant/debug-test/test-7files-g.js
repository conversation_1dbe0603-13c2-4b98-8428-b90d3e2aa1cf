// test-7files-g.js - 应用服务
const CommandHandler = require('./test-7files-f');

class ApplicationService {
    constructor() {
        this.commandHandler = new CommandHandler();
        this.requestLog = [];
        this.performanceMetrics = new Map();
    }

    async processRequest(request) {
        const startTime = Date.now();
        const requestId = Date.now();

        this.requestLog.push({
            id: requestId,
            type: request.type,
            timestamp: startTime,
            status: 'processing'
        });

        console.log(`ApplicationService: Processing request ${request.type} (ID: ${requestId})`);

        try {
            let result;

            switch (request.type) {
                case 'RegisterUser':
                    result = await this.registerUser(request.data);
                    break;
                case 'ActivateUser':
                    result = await this.activateUser(request.data);
                    break;
                case 'UserLogin':
                    result = await this.userLogin(request.data);
                    break;
                case 'CreateOrder':
                    result = await this.createOrder(request.data);
                    break;
                case 'AddItemToOrder':
                    result = await this.addItemToOrder(request.data);
                    break;
                case 'ProcessOrder':
                    result = await this.processOrder(request.data);
                    break;
                case 'GetUserInfo':
                    result = await this.getUserInfo(request.data);
                    break;
                case 'GetOrderInfo':
                    result = await this.getOrderInfo(request.data);
                    break;
                default:
                    throw new Error(`Unknown request type: ${request.type}`);
            }

            const endTime = Date.now();
            const duration = endTime - startTime;

            this.updatePerformanceMetrics(request.type, duration);
            this.updateRequestLog(requestId, 'completed', result);

            console.log(`ApplicationService: Request ${request.type} completed in ${duration}ms`);

            return {
                success: true,
                data: result,
                requestId,
                duration
            };

        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            this.updateRequestLog(requestId, 'failed', null, error.message);

            console.error(`ApplicationService: Request ${request.type} failed in ${duration}ms: ${error.message}`);

            return {
                success: false,
                error: error.message,
                requestId,
                duration
            };
        }
    }

    async registerUser(data) {
        const { email, name } = data;
        const userId = `user-${Date.now()}`;

        // Create user
        await this.commandHandler.executeCommand({
            type: 'CreateUser',
            data: { id: userId, email, name }
        });

        // Activate user
        await this.commandHandler.executeCommand({
            type: 'ActivateUser',
            data: { userId }
        });

        return { userId, email, name, status: 'active' };
    }

    async activateUser(data) {
        return await this.commandHandler.executeCommand({
            type: 'ActivateUser',
            data
        });
    }

    async userLogin(data) {
        return await this.commandHandler.executeCommand({
            type: 'LoginUser',
            data
        });
    }

    async createOrder(data) {
        const { userId } = data;
        const orderId = `order-${Date.now()}`;

        return await this.commandHandler.executeCommand({
            type: 'CreateOrder',
            data: { id: orderId, userId }
        });
    }

    async addItemToOrder(data) {
        return await this.commandHandler.executeCommand({
            type: 'AddOrderItem',
            data
        });
    }

    async processOrder(data) {
        const { orderId } = data;

        // Submit order
        await this.commandHandler.executeCommand({
            type: 'SubmitOrder',
            data: { orderId }
        });

        // Confirm order
        await this.commandHandler.executeCommand({
            type: 'ConfirmOrder',
            data: { orderId }
        });

        // Ship order
        await this.commandHandler.executeCommand({
            type: 'ShipOrder',
            data: { orderId }
        });

        return { orderId, status: 'shipped' };
    }

    async getUserInfo(data) {
        const { userId } = data;
        const projection = this.commandHandler.eventHandler.getProjection('user', userId);

        if (!projection) {
            throw new Error(`User ${userId} not found`);
        }

        return projection;
    }

    async getOrderInfo(data) {
        const { orderId } = data;
        const projection = this.commandHandler.eventHandler.getProjection('order', orderId);

        if (!projection) {
            throw new Error(`Order ${orderId} not found`);
        }

        return projection;
    }

    updatePerformanceMetrics(requestType, duration) {
        if (!this.performanceMetrics.has(requestType)) {
            this.performanceMetrics.set(requestType, {
                count: 0,
                totalDuration: 0,
                avgDuration: 0,
                minDuration: Infinity,
                maxDuration: 0
            });
        }

        const metrics = this.performanceMetrics.get(requestType);
        metrics.count++;
        metrics.totalDuration += duration;
        metrics.avgDuration = metrics.totalDuration / metrics.count;
        metrics.minDuration = Math.min(metrics.minDuration, duration);
        metrics.maxDuration = Math.max(metrics.maxDuration, duration);
    }

    updateRequestLog(requestId, status, result = null, error = null) {
        const logEntry = this.requestLog.find(r => r.id === requestId);
        if (logEntry) {
            logEntry.status = status;
            logEntry.result = result;
            logEntry.error = error;
        }
    }

    getSystemStats() {
        return {
            application: {
                totalRequests: this.requestLog.length,
                successfulRequests: this.requestLog.filter(r => r.status === 'completed').length,
                failedRequests: this.requestLog.filter(r => r.status === 'failed').length,
                performanceMetrics: Object.fromEntries(this.performanceMetrics)
            },
            commandHandler: this.commandHandler.getStats()
        };
    }
}
module.exports = ApplicationService;