// test-7files-e.js - 事件处理器
const EventStore = require('./test-7files-a');

class EventHandler {
    constructor() {
        this.eventStore = new EventStore();
        this.projections = new Map();
        this.processedEvents = new Set();

        this.setupSubscriptions();
    }

    setupSubscriptions() {
        // User event subscriptions
        this.eventStore.subscribe('UserCreated', this.handleUserCreated.bind(this));
        this.eventStore.subscribe('UserActivated', this.handleUserActivated.bind(this));
        this.eventStore.subscribe('UserLoggedIn', this.handleUserLoggedIn.bind(this));

        // Order event subscriptions
        this.eventStore.subscribe('OrderCreated', this.handleOrderCreated.bind(this));
        this.eventStore.subscribe('OrderSubmitted', this.handleOrderSubmitted.bind(this));
        this.eventStore.subscribe('OrderConfirmed', this.handleOrderConfirmed.bind(this));
        this.eventStore.subscribe('OrderShipped', this.handleOrderShipped.bind(this));

        console.log('EventHandler: Set up event subscriptions');
    }

    handleUserCreated(event) {
        if (this.processedEvents.has(event.id)) return;

        const userProjection = {
            id: event.streamId,
            email: event.data.email,
            name: event.data.name,
            status: 'inactive',
            loginCount: 0,
            createdAt: event.timestamp,
            lastUpdated: event.timestamp
        };

        this.projections.set(`user:${event.streamId}`, userProjection);
        console.log(`EventHandler: Created user projection for ${event.streamId}`);
        this.processedEvents.add(event.id);
    }

    handleUserActivated(event) {
        if (this.processedEvents.has(event.id)) return;

        const projection = this.projections.get(`user:${event.streamId}`);
        if (projection) {
            projection.status = 'active';
            projection.lastUpdated = event.timestamp;
            console.log(`EventHandler: Updated user projection - activated ${event.streamId}`);
        }
        this.processedEvents.add(event.id);
    }

    handleUserLoggedIn(event) {
        if (this.processedEvents.has(event.id)) return;

        const projection = this.projections.get(`user:${event.streamId}`);
        if (projection) {
            projection.loginCount++;
            projection.lastLoginAt = event.data.loginAt;
            projection.lastUpdated = event.timestamp;
            console.log(`EventHandler: Updated user projection - login count for ${event.streamId}: ${projection.loginCount}`);
        }
        this.processedEvents.add(event.id);
    }

    handleOrderCreated(event) {
        if (this.processedEvents.has(event.id)) return;

        const orderProjection = {
            id: event.streamId,
            userId: event.data.userId,
            status: 'draft',
            itemCount: 0,
            totalAmount: 0,
            createdAt: event.timestamp,
            lastUpdated: event.timestamp
        };

        this.projections.set(`order:${event.streamId}`, orderProjection);
        console.log(`EventHandler: Created order projection for ${event.streamId}`);
        this.processedEvents.add(event.id);
    }

    handleOrderSubmitted(event) {
        if (this.processedEvents.has(event.id)) return;

        const projection = this.projections.get(`order:${event.streamId}`);
        if (projection) {
            projection.status = 'submitted';
            projection.lastUpdated = event.timestamp;
            console.log(`EventHandler: Updated order projection - submitted ${event.streamId}`);
        }
        this.processedEvents.add(event.id);
    }

    handleOrderConfirmed(event) {
        if (this.processedEvents.has(event.id)) return;

        const projection = this.projections.get(`order:${event.streamId}`);
        if (projection) {
            projection.status = 'confirmed';
            projection.lastUpdated = event.timestamp;
            console.log(`EventHandler: Updated order projection - confirmed ${event.streamId}`);
        }
        this.processedEvents.add(event.id);
    }

    handleOrderShipped(event) {
        if (this.processedEvents.has(event.id)) return;

        const projection = this.projections.get(`order:${event.streamId}`);
        if (projection) {
            projection.status = 'shipped';
            projection.lastUpdated = event.timestamp;
            console.log(`EventHandler: Updated order projection - shipped ${event.streamId}`);
        }
        this.processedEvents.add(event.id);
    }

    getProjection(type, id) {
        return this.projections.get(`${type}:${id}`);
    }

    getAllProjections(type) {
        return Array.from(this.projections.entries())
            .filter(([key]) => key.startsWith(`${type}:`))
            .map(([key, value]) => value);
    }

    getStats() {
        return {
            totalProjections: this.projections.size,
            processedEvents: this.processedEvents.size,
            userProjections: this.getAllProjections('user').length,
            orderProjections: this.getAllProjections('order').length,
            eventStoreStats: this.eventStore.getStats()
        };
    }
}
module.exports = EventHandler;