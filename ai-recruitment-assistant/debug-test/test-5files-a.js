// test-5files-a.js - 日志服务
class LogService {
    constructor() {
        this.logs = [];
        this.logLevels = { ERROR: 0, WARN: 1, INFO: 2, DEBUG: 3 };
        this.currentLevel = this.logLevels.INFO;
    }
    
    log(level, message, metadata = {}) {
        if (this.logLevels[level] <= this.currentLevel) {
            const entry = {
                timestamp: Date.now(),
                level,
                message,
                metadata,
                id: this.logs.length + 1
            };
            this.logs.push(entry);
            console.log(`[${level}] ${message}`);
        }
    }
    
    error(msg, meta) { this.log('ERROR', msg, meta); }
    warn(msg, meta) { this.log('WARN', msg, meta); }
    info(msg, meta) { this.log('INFO', msg, meta); }
    debug(msg, meta) { this.log('DEBUG', msg, meta); }
    
    getStats() {
        return {
            totalLogs: this.logs.length,
            byLevel: Object.keys(this.logLevels).reduce((acc, level) => {
                acc[level] = this.logs.filter(log => log.level === level).length;
                return acc;
            }, {})
        };
    }
}
module.exports = LogService;