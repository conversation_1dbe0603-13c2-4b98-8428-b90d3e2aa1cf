const axios = require('axios');

// 测试真正的新会话
async function testNewSession() {
    console.log('🧪 测试真正的新会话...\n');

    const baseURL = 'http://localhost:3000';
    
    // 生成唯一的会话ID和邮箱
    const timestamp = Date.now();
    const uniqueSessionId = `test-new-${timestamp}`;
    const uniqueEmail = `test-${timestamp}@example.com`;
    
    console.log(`🆕 使用全新会话ID: ${uniqueSessionId}`);
    console.log(`📧 使用全新邮箱: ${uniqueEmail}`);
    
    try {
        const response = await axios.post(`${baseURL}/api/chat`, {
            message: '你好',
            sessionId: uniqueSessionId,
            userEmail: uniqueEmail
        });
        
        console.log('\n✅ API响应成功:');
        console.log('📊 完整响应:', JSON.stringify(response.data, null, 2));
        
        // 检查响应类型
        if (response.data.responseType) {
            console.log(`\n🎯 响应类型: ${response.data.responseType}`);
            if (response.data.responseType.includes('first')) {
                console.log('✅ 正确识别为第一句！');
            } else {
                console.log('❌ 未识别为第一句');
            }
        }
        
        // 检查实际回复内容
        if (response.data.response) {
            console.log(`\n💬 实际回复: "${response.data.response}"`);
            
            // 检查是否包含期望的第一句回复
            const expectedPhrases = [
                '我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少',
                '您有兴趣看看新机会吗？我这边合作的客户职位还挺多的'
            ];
            
            const isCorrectFirstReply = expectedPhrases.some(phrase => 
                response.data.response.includes(phrase)
            );
            
            if (isCorrectFirstReply) {
                console.log('✅ 回复内容正确！');
            } else {
                console.log('❌ 回复内容不是期望的第一句回复');
            }
        }
        
    } catch (error) {
        console.error('❌ API请求失败:', error.response?.data || error.message);
    }
}

// 运行测试
testNewSession().catch(console.error);
