
// test-2files-runner.js
const BusinessLogic = require('./test-2files-b');

async function test2Files() {
    console.log('🧪 测试2文件跨度...');
    
    const logic = new BusinessLogic();
    
    // 测试用例
    const testCases = ['hello', 'world', 'hello', 'test'];
    
    console.log('📊 执行测试用例:');
    testCases.forEach((testCase, index) => {
        const result = logic.handleRequest(testCase);
        console.log(`${index + 1}. 输入: "${testCase}" -> 结果: ${JSON.stringify(result)}`);
    });
    
    console.log('📈 最终统计:', logic.getStats());
    
    // 问题：预测下一次调用 logic.handleRequest('hello') 的结果
    console.log('\n❓ 问题: 如果再次调用 logic.handleRequest("hello")，结果会是什么？');
}

test2Files();
