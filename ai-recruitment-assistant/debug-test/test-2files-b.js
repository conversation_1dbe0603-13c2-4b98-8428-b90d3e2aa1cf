
// test-2files-b.js - 业务逻辑
const DataProcessor = require('./test-2files-a');

class BusinessLogic {
    constructor() {
        this.processor = new DataProcessor();
        this.results = [];
    }
    
    handleRequest(data) {
        console.log('BusinessLogic: 处理请求', data);
        
        const processed = this.processor.processData(data);
        this.results.push(processed);
        
        return {
            success: true,
            data: processed.result,
            cached: processed.fromCache,
            totalProcessed: this.results.length,
            cacheSize: this.processor.getCacheSize()
        };
    }
    
    getStats() {
        return {
            totalRequests: this.results.length,
            cacheHits: this.results.filter(r => r.fromCache).length,
            cacheSize: this.processor.getCacheSize()
        };
    }
}

module.exports = BusinessLogic;
