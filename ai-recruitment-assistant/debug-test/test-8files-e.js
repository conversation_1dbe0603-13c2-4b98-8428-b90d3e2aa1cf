// test-8files-e.js - 博客插件
class BlogPlugin {
    constructor() {
        this.name = 'blog';
        this.version = '1.0.0';
        this.description = 'Blog management plugin';
        this.dependencies = ['auth', 'database'];
        this.posts = new Map();
        this.comments = new Map();
    }

    async activate(context) {
        this.context = context;

        console.log('BlogPlugin: Activating blog plugin...');

        // Get dependencies
        this.auth = context.getPlugin('auth');
        this.db = context.getPlugin('database');

        if (!this.auth || !this.db) {
            throw new Error('BlogPlugin requires auth and database plugins');
        }

        // Register hooks
        context.registerHook('blog.create', this.createPost.bind(this));
        context.registerHook('blog.update', this.updatePost.bind(this));
        context.registerHook('blog.delete', this.deletePost.bind(this));
        context.registerHook('blog.comment', this.addComment.bind(this));

        // Register HTTP routes
        const http = context.registry.modules.get('http');
        if (http) {
            http.addRoute('/blog/posts', this.postsRoute.bind(this));
            http.addRoute('/blog/post', this.postRoute.bind(this));
            http.addRoute('/blog/comment', this.commentRoute.bind(this));
        }

        console.log('BlogPlugin: Blog plugin activated');
        return this;
    }

    async createPost(data) {
        const { title, content, authorId, status = 'draft' } = data;

        const post = await this.db.create({
            collection: 'posts',
            document: {
                title,
                content,
                authorId,
                status,
                views: 0,
                likes: 0,
                tags: data.tags || []
            }
        });

        console.log(`BlogPlugin: Created post ${post.id} by author ${authorId}`);
        return post;
    }

    async updatePost(data) {
        const { postId, updates, authorId } = data;

        // Check if post exists and user has permission
        const post = await this.db.read({ collection: 'posts', id: postId });

        if (post.authorId !== authorId) {
            throw new Error('Permission denied: not the author');
        }

        const updatedPost = await this.db.update({
            collection: 'posts',
            id: postId,
            updates
        });

        console.log(`BlogPlugin: Updated post ${postId}`);
        return updatedPost;
    }

    async deletePost(data) {
        const { postId, authorId } = data;

        const post = await this.db.read({ collection: 'posts', id: postId });

        if (post.authorId !== authorId) {
            throw new Error('Permission denied: not the author');
        }

        // Delete all comments for this post
        const comments = await this.db.query({
            collection: 'comments',
            filter: { postId }
        });

        for (const comment of comments.results) {
            await this.db.delete({ collection: 'comments', id: comment.id });
        }

        const result = await this.db.delete({ collection: 'posts', id: postId });

        console.log(`BlogPlugin: Deleted post ${postId} and its comments`);
        return result;
    }

    async addComment(data) {
        const { postId, content, authorId } = data;

        // Check if post exists
        await this.db.read({ collection: 'posts', id: postId });

        const comment = await this.db.create({
            collection: 'comments',
            document: {
                postId,
                content,
                authorId,
                likes: 0
            }
        });

        console.log(`BlogPlugin: Added comment ${comment.id} to post ${postId}`);
        return comment;
    }

    async postsRoute(data) {
        if (!data.auth?.authenticated) {
            // Public posts only
            return await this.db.query({
                collection: 'posts',
                filter: { status: 'published' },
                limit: data.limit || 10,
                offset: data.offset || 0
            });
        }

        // Authenticated users can see all posts
        return await this.db.query({
            collection: 'posts',
            filter: data.filter || {},
            limit: data.limit || 10,
            offset: data.offset || 0
        });
    }

    async postRoute(data) {
        const { postId } = data;

        const post = await this.db.read({ collection: 'posts', id: postId });

        // Increment view count
        await this.db.update({
            collection: 'posts',
            id: postId,
            updates: { views: post.views + 1 }
        });

        // Get comments
        const comments = await this.db.query({
            collection: 'comments',
            filter: { postId }
        });

        return {
            post: { ...post, views: post.views + 1 },
            comments: comments.results
        };
    }

    async commentRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required to comment');
        }

        return await this.addComment({
            postId: data.postId,
            content: data.content,
            authorId: data.auth.session.userId
        });
    }

    deactivate() {
        console.log('BlogPlugin: Deactivating blog plugin...');
    }

    getStats() {
        return {
            totalPosts: this.posts.size,
            totalComments: this.comments.size
        };
    }
}

module.exports = {
    name: 'blog',
    version: '1.0.0',
    description: 'Blog management plugin',
    dependencies: ['auth', 'database'],
    activate: async (context) => {
        const plugin = new BlogPlugin();
        return await plugin.activate(context);
    }
};