// test-8files-b.js - 核心系统
const PluginRegistry = require('./test-8files-a');

class CoreSystem {
    constructor() {
        this.registry = new PluginRegistry();
        this.modules = new Map();
        this.config = new Map();
        this.isInitialized = false;

        this.setupCoreHooks();
        this.setupEventListeners();
    }

    setupCoreHooks() {
        // Core system hooks
        this.registry.registerHook('core', 'system.init', async (data) => {
            console.log('CoreSystem: System initialization hook called');
            return data;
        });

        this.registry.registerHook('core', 'system.shutdown', async (data) => {
            console.log('CoreSystem: System shutdown hook called');
            return data;
        });
    }

    setupEventListeners() {
        this.registry.eventEmitter.on('plugin.loaded', (data) => {
            console.log(`CoreSystem: Plugin ${data.name} loaded event received`);
        });

        this.registry.eventEmitter.on('plugin.error', (data) => {
            console.error(`CoreSystem: Plugin ${data.name} error event received`);
        });
    }

    async initialize() {
        if (this.isInitialized) {
            return;
        }

        console.log('CoreSystem: Initializing core system...');

        // Load core configuration
        this.loadCoreConfig();

        // Initialize core modules
        await this.initializeCoreModules();

        // Call system init hook
        await this.registry.callHook('system.init', { timestamp: Date.now() });

        this.isInitialized = true;
        console.log('CoreSystem: Core system initialized');
    }

    loadCoreConfig() {
        this.config.set('system.name', 'Plugin System Demo');
        this.config.set('system.version', '1.0.0');
        this.config.set('plugin.autoload', true);
        this.config.set('plugin.timeout', 30000);

        console.log('CoreSystem: Core configuration loaded');
    }

    async initializeCoreModules() {
        // Logger module
        this.modules.set('logger', {
            log: (level, message, meta = {}) => {
                console.log(`[${level.toUpperCase()}] ${message}`, meta);
            },
            info: (msg, meta) => this.modules.get('logger').log('info', msg, meta),
            error: (msg, meta) => this.modules.get('logger').log('error', msg, meta),
            warn: (msg, meta) => this.modules.get('logger').log('warn', msg, meta)
        });

        // Storage module
        this.modules.set('storage', {
            data: new Map(),
            get: function(key) { return this.data.get(key); },
            set: function(key, value) { this.data.set(key, value); },
            delete: function(key) { return this.data.delete(key); },
            has: function(key) { return this.data.has(key); }
        });

        // HTTP module
        this.modules.set('http', {
            routes: new Map(),
            middleware: [],
            addRoute: function(path, handler) { this.routes.set(path, handler); },
            addMiddleware: function(middleware) { this.middleware.push(middleware); },
            handleRequest: async function(path, data) {
                // Apply middleware
                for (const mw of this.middleware) {
                    data = await mw(data) || data;
                }

                const handler = this.routes.get(path);
                if (handler) {
                    return await handler(data);
                }
                throw new Error(`Route ${path} not found`);
            }
        });

        console.log('CoreSystem: Core modules initialized');
    }

    getModule(name) {
        return this.modules.get(name);
    }

    getConfig(key) {
        return this.config.get(key);
    }

    setConfig(key, value) {
        this.config.set(key, value);
        console.log(`CoreSystem: Config ${key} set to ${value}`);
    }

    async loadPlugin(plugin) {
        await this.registry.register(plugin);
        return await this.registry.load(plugin.name);
    }

    async shutdown() {
        console.log('CoreSystem: Shutting down...');

        // Call shutdown hook
        await this.registry.callHook('system.shutdown', { timestamp: Date.now() });

        // Unload all plugins in reverse order
        const loadOrder = this.registry.loadOrder.slice().reverse();
        for (const pluginName of loadOrder) {
            this.registry.unload(pluginName);
        }

        this.isInitialized = false;
        console.log('CoreSystem: Shutdown complete');
    }

    getSystemStats() {
        return {
            initialized: this.isInitialized,
            modules: Array.from(this.modules.keys()),
            config: Object.fromEntries(this.config),
            plugins: this.registry.getStats()
        };
    }
}
module.exports = CoreSystem;