// test-first-message-fix.js - 测试第一句回复修复

async function testFirstMessageFix() {
  console.log("🧪 测试第一句回复修复...");

  try {
    // 直接测试修复后的回复内容，不需要实例化MessageProcessor
    console.log("📋 测试修复后的第一句回复内容...\n");

    // 模拟第一句消息的分析结果
    const testCases = [
      {
        name: "问候类消息 - '你好'",
        intent: {
          type: "greeting",
          messageCategory: "greeting",
          confidence: 0.9,
        },
        expectedContent:
          "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
      },
      {
        name: "身份确认类消息 - '你是谁'",
        intent: {
          type: "greeting",
          messageCategory: "identity_check",
          confidence: 0.8,
        },
        expectedContent:
          "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
      },
      {
        name: "职位询问类消息 - '有什么职位'",
        intent: {
          type: "job_search",
          messageCategory: "job_inquiry",
          confidence: 0.9,
        },
        expectedContent:
          "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
      },
      {
        name: "默认回复测试",
        intent: {
          type: "unknown",
          messageCategory: "other",
          confidence: 0.1,
        },
        expectedContent:
          "我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
      },
    ];

    // 模拟会话和消息数据
    const mockSession = { id: "test-session-123" };
    const mockMessageData = { message: "test", userEmail: "<EMAIL>" };

    console.log("📋 开始测试各种第一句回复场景...\n");

    for (const testCase of testCases) {
      console.log(`🔍 测试: ${testCase.name}`);

      let response;

      if (
        testCase.intent.messageCategory === "greeting" ||
        testCase.intent.messageCategory === "identity_check"
      ) {
        // 测试问候类和身份确认类
        response = await processor.handleFirstUserGreeting(
          mockMessageData,
          mockSession,
          testCase.intent
        );
      } else if (testCase.intent.messageCategory === "job_inquiry") {
        // 测试职位询问类
        response = await processor.handleFirstUserGreeting(
          mockMessageData,
          mockSession,
          testCase.intent
        );
      } else {
        // 测试默认回复
        response = processor.getDefaultFirstResponse();
      }

      // 检查回复内容
      const contentIncludesExpected = response.content.includes(
        testCase.expectedContent
      );
      const hasResumeUploadMention =
        response.content.includes("简历") && response.content.includes("上传");
      const hasEmailMention = response.content.includes("邮箱");

      console.log(`   ✅ 回复类型: ${response.type}`);
      console.log(
        `   ${contentIncludesExpected ? "✅" : "❌"} 包含正确开场白: "${testCase.expectedContent}"`
      );
      console.log(
        `   ${hasResumeUploadMention ? "✅" : "❌"} 包含简历上传说明`
      );
      console.log(`   ${hasEmailMention ? "✅" : "❌"} 包含邮箱说明`);
      console.log(`   📝 完整回复: "${response.content}"`);
      console.log(`   🎯 建议选项: ${JSON.stringify(response.suggestions)}`);
      console.log("");
    }

    // 测试关键词匹配逻辑
    console.log("🔍 测试关键词匹配逻辑...\n");

    const keywordTests = [
      { message: "你好", expectedCategory: "greeting" },
      { message: "hello", expectedCategory: "greeting" },
      { message: "你是谁", expectedCategory: "identity_check" },
      { message: "有什么职位", expectedCategory: "job_inquiry" },
      { message: "招聘", expectedCategory: "job_inquiry" },
      { message: "随便说点什么", expectedCategory: null },
    ];

    for (const test of keywordTests) {
      const result = processor.checkQuickPatterns(test.message);
      const actualCategory = result ? result.category : null;
      const isCorrect = actualCategory === test.expectedCategory;

      console.log(
        `   ${isCorrect ? "✅" : "❌"} "${test.message}" -> ${actualCategory || "null"} (期望: ${test.expectedCategory || "null"})`
      );
    }

    console.log("\n🎉 第一句回复修复测试完成！");
  } catch (error) {
    console.error("❌ 测试过程中出现错误:", error);
  }
}

testFirstMessageFix();
