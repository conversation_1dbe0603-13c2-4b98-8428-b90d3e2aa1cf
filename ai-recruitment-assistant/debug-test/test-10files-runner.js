// test-10files-runner.js
const BaseComponent = require('./test-10files-a');

// 创建10个相互依赖的组件来测试复杂的跨文件能力
class ComponentA extends BaseComponent {
    constructor() { super('ComponentA'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'A', processed: data, timestamp: Date.now() };
    }
}

class ComponentB extends BaseComponent {
    constructor() { super('ComponentB'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'B', transformed: data?.processed || data, count: 1 };
    }
}

class ComponentC extends BaseComponent {
    constructor() { super('ComponentC'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'C', aggregated: [data], total: 1 };
    }
}

class ComponentD extends BaseComponent {
    constructor() { super('ComponentD'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'D', validated: true, data };
    }
}

class ComponentE extends BaseComponent {
    constructor() { super('ComponentE'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'E', enriched: { ...data, metadata: { source: 'E' } } };
    }
}

class ComponentF extends BaseComponent {
    constructor() { super('ComponentF'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'F', filtered: data, passed: true };
    }
}

class ComponentG extends BaseComponent {
    constructor() { super('ComponentG'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'G', cached: data, cacheKey: `key-${Date.now()}` };
    }
}

class ComponentH extends BaseComponent {
    constructor() { super('ComponentH'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'H', persisted: data, id: Math.random().toString(36) };
    }
}

class ComponentI extends BaseComponent {
    constructor() { super('ComponentI'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'I', notified: true, recipients: ['admin', 'user'] };
    }
}

class ComponentJ extends BaseComponent {
    constructor() { super('ComponentJ'); }
    async execute(method, data) {
        await super.execute(method, data);
        return { component: 'J', finalized: data, status: 'complete' };
    }
}

async function test10Files() {
    console.log('🧪 测试10文件跨度 - 复杂组件依赖链...');
    
    // 创建10个组件
    const components = {
        A: new ComponentA(),
        B: new ComponentB(),
        C: new ComponentC(),
        D: new ComponentD(),
        E: new ComponentE(),
        F: new ComponentF(),
        G: new ComponentG(),
        H: new ComponentH(),
        I: new ComponentI(),
        J: new ComponentJ()
    };
    
    // 建立复杂的依赖关系
    // A -> B -> C -> D
    components.B.addDependency(components.A);
    components.C.addDependency(components.B);
    components.D.addDependency(components.C);
    
    // E -> F -> G
    components.F.addDependency(components.E);
    components.G.addDependency(components.F);
    
    // H -> I -> J
    components.I.addDependency(components.H);
    components.J.addDependency(components.I);
    
    // 交叉依赖
    components.D.addDependency(components.E);
    components.G.addDependency(components.D);
    components.J.addDependency(components.G);
    
    console.log('📊 组件依赖关系建立完成，启动所有组件...');
    
    // 启动所有组件
    Object.values(components).forEach(comp => comp.start());
    
    console.log('🔄 开始执行复杂的调用链...');
    
    try {
        // 执行复杂的调用链
        const results = [];
        
        // 1. 从A开始的链式调用
        const resultA = await components.A.call('process', { input: 'test-data-1' });
        results.push(resultA);
        
        const resultB = await components.B.call('transform', resultA);
        results.push(resultB);
        
        const resultC = await components.C.call('aggregate', resultB);
        results.push(resultC);
        
        // 2. 并行处理E和H
        const [resultE, resultH] = await Promise.all([
            components.E.call('enrich', { input: 'test-data-2' }),
            components.H.call('persist', { input: 'test-data-3' })
        ]);
        results.push(resultE, resultH);
        
        // 3. 继续链式调用
        const resultF = await components.F.call('filter', resultE);
        const resultI = await components.I.call('notify', resultH);
        results.push(resultF, resultI);
        
        // 4. 最终汇聚
        const resultD = await components.D.call('validate', resultC);
        const resultG = await components.G.call('cache', resultF);
        const resultJ = await components.J.call('finalize', resultI);
        results.push(resultD, resultG, resultJ);
        
        console.log('✅ 所有调用完成，收集统计信息...');
        
        // 收集所有组件的统计信息
        const allStats = Object.values(components).map(comp => comp.getStats());
        
        console.log('📈 完整系统统计:', JSON.stringify({
            totalComponents: Object.keys(components).length,
            totalCalls: allStats.reduce((sum, stat) => sum + stat.metrics.calls, 0),
            totalErrors: allStats.reduce((sum, stat) => sum + stat.metrics.errors, 0),
            componentStats: allStats
        }, null, 2));
        
        console.log('\n❓ 10文件跨度问题:');
        console.log('1. 总共有多少个组件？');
        console.log('2. 组件间的依赖关系有多复杂？');
        console.log('3. 总共执行了多少次方法调用？');
        console.log('4. 哪些组件有交叉依赖？');
        console.log('5. 并行处理的组件有哪些？');
        console.log('6. 最长的依赖链路径是什么？');
        console.log('7. 错误传播是如何工作的？');
        console.log('8. 组件的生命周期管理如何？');
        console.log('9. 数据在10个组件间是如何流转的？');
        console.log('10. 整个系统的性能瓶颈在哪里？');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        // 停止所有组件
        Object.values(components).forEach(comp => comp.stop());
    }
}

test10Files();
