// 状态管理理解能力测试
const fs = require('fs');

// 生成状态管理测试用例
function generateStateTests() {
    console.log('🧠 生成状态管理理解测试...\n');
    
    // 测试1: 简单状态变化
    generateSimpleStateTest();
    
    // 测试2: 异步状态管理
    generateAsyncStateTest();
    
    // 测试3: 复杂状态依赖
    generateComplexStateTest();
    
    // 测试4: 并发状态冲突
    generateConcurrentStateTest();
    
    console.log('✅ 状态管理测试生成完成！');
}

// 简单状态变化测试
function generateSimpleStateTest() {
    const testCode = `
// state-test-1-simple.js - 简单状态变化
class SimpleCounter {
    constructor() {
        this.count = 0;
        this.history = [];
        this.isActive = false;
    }
    
    start() {
        console.log('Counter started');
        this.isActive = true;
        this.history.push({ action: 'start', count: this.count, timestamp: Date.now() });
    }
    
    increment() {
        if (!this.isActive) {
            console.log('Counter not active, cannot increment');
            return false;
        }
        
        this.count++;
        this.history.push({ action: 'increment', count: this.count, timestamp: Date.now() });
        console.log(\`Count: \${this.count}\`);
        return true;
    }
    
    decrement() {
        if (!this.isActive) {
            console.log('Counter not active, cannot decrement');
            return false;
        }
        
        if (this.count <= 0) {
            console.log('Count cannot go below 0');
            return false;
        }
        
        this.count--;
        this.history.push({ action: 'decrement', count: this.count, timestamp: Date.now() });
        console.log(\`Count: \${this.count}\`);
        return true;
    }
    
    stop() {
        console.log('Counter stopped');
        this.isActive = false;
        this.history.push({ action: 'stop', count: this.count, timestamp: Date.now() });
    }
    
    getState() {
        return {
            count: this.count,
            isActive: this.isActive,
            historyLength: this.history.length
        };
    }
}

// 测试序列
async function testSimpleState() {
    console.log('🧪 测试简单状态变化...');
    
    const counter = new SimpleCounter();
    
    console.log('初始状态:', counter.getState());
    
    // 执行操作序列
    counter.increment();  // 应该失败
    counter.start();
    counter.increment();  // count = 1
    counter.increment();  // count = 2
    counter.decrement();  // count = 1
    counter.stop();
    counter.increment();  // 应该失败
    
    console.log('最终状态:', counter.getState());
    
    // 问题
    console.log('\\n❓ 状态预测问题:');
    console.log('1. 如果现在调用 counter.decrement()，会发生什么？');
    console.log('2. counter.count 的值是多少？');
    console.log('3. counter.history.length 是多少？');
}

testSimpleState();
`;

    fs.writeFileSync(__dirname + '/state-test-1-simple.js', testCode);
    console.log('✅ 简单状态测试生成完成');
}

// 异步状态管理测试
function generateAsyncStateTest() {
    const testCode = `
// state-test-2-async.js - 异步状态管理
class AsyncTaskManager {
    constructor() {
        this.tasks = new Map();
        this.completedTasks = [];
        this.isProcessing = false;
        this.taskIdCounter = 0;
    }
    
    async addTask(name, duration) {
        const taskId = ++this.taskIdCounter;
        const task = {
            id: taskId,
            name,
            duration,
            status: 'pending',
            createdAt: Date.now(),
            startedAt: null,
            completedAt: null
        };
        
        this.tasks.set(taskId, task);
        console.log(\`Task \${taskId} (\${name}) added\`);
        
        if (!this.isProcessing) {
            this.processNextTask();
        }
        
        return taskId;
    }
    
    async processNextTask() {
        if (this.isProcessing) {
            console.log('Already processing tasks');
            return;
        }
        
        const pendingTask = Array.from(this.tasks.values()).find(t => t.status === 'pending');
        if (!pendingTask) {
            console.log('No pending tasks');
            return;
        }
        
        this.isProcessing = true;
        pendingTask.status = 'running';
        pendingTask.startedAt = Date.now();
        
        console.log(\`Processing task \${pendingTask.id} (\${pendingTask.name})...\`);
        
        // 模拟异步工作
        await new Promise(resolve => setTimeout(resolve, pendingTask.duration));
        
        pendingTask.status = 'completed';
        pendingTask.completedAt = Date.now();
        this.completedTasks.push(pendingTask);
        this.tasks.delete(pendingTask.id);
        
        console.log(\`Task \${pendingTask.id} completed\`);
        
        this.isProcessing = false;
        
        // 处理下一个任务
        if (this.tasks.size > 0) {
            setTimeout(() => this.processNextTask(), 10);
        }
    }
    
    getState() {
        return {
            pendingTasks: this.tasks.size,
            completedTasks: this.completedTasks.length,
            isProcessing: this.isProcessing,
            currentTask: Array.from(this.tasks.values()).find(t => t.status === 'running')?.id || null
        };
    }
}

// 测试序列
async function testAsyncState() {
    console.log('🧪 测试异步状态管理...');
    
    const manager = new AsyncTaskManager();
    
    console.log('初始状态:', manager.getState());
    
    // 添加任务
    await manager.addTask('Task A', 100);
    console.log('添加Task A后:', manager.getState());
    
    await manager.addTask('Task B', 50);
    console.log('添加Task B后:', manager.getState());
    
    await manager.addTask('Task C', 200);
    console.log('添加Task C后:', manager.getState());
    
    // 等待一段时间观察状态变化
    await new Promise(resolve => setTimeout(resolve, 150));
    console.log('150ms后状态:', manager.getState());
    
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log('350ms后状态:', manager.getState());
    
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log('450ms后状态:', manager.getState());
    
    console.log('\\n❓ 异步状态预测问题:');
    console.log('1. 在150ms时，哪个任务正在运行？');
    console.log('2. 在350ms时，完成了几个任务？');
    console.log('3. 任务的执行顺序是什么？');
}

testAsyncState();
`;

    fs.writeFileSync(__dirname + '/state-test-2-async.js', testCode);
    console.log('✅ 异步状态测试生成完成');
}

// 复杂状态依赖测试
function generateComplexStateTest() {
    const testCode = `
// state-test-3-complex.js - 复杂状态依赖
class GameState {
    constructor() {
        this.player = {
            health: 100,
            mana: 50,
            level: 1,
            experience: 0,
            inventory: []
        };
        
        this.game = {
            turn: 0,
            phase: 'preparation', // preparation, battle, rest
            enemies: [],
            events: []
        };
        
        this.config = {
            expToNextLevel: 100,
            healthRegenPerTurn: 5,
            manaRegenPerTurn: 10
        };
    }
    
    nextTurn() {
        this.game.turn++;
        this.addEvent(\`Turn \${this.game.turn} started\`);
        
        // 根据当前阶段执行不同逻辑
        switch (this.game.phase) {
            case 'preparation':
                this.preparationPhase();
                break;
            case 'battle':
                this.battlePhase();
                break;
            case 'rest':
                this.restPhase();
                break;
        }
    }
    
    preparationPhase() {
        this.addEvent('Preparation phase');
        
        // 生成敌人
        if (this.game.turn % 3 === 0) {
            this.spawnEnemy();
        }
        
        // 切换到战斗阶段
        if (this.game.enemies.length > 0) {
            this.game.phase = 'battle';
            this.addEvent('Entering battle phase');
        }
    }
    
    battlePhase() {
        this.addEvent('Battle phase');
        
        // 玩家攻击
        if (this.player.mana >= 10) {
            this.playerAttack();
        }
        
        // 敌人攻击
        this.enemyAttack();
        
        // 检查战斗结束
        if (this.game.enemies.length === 0) {
            this.game.phase = 'rest';
            this.addEvent('Battle won, entering rest phase');
        } else if (this.player.health <= 0) {
            this.addEvent('Player defeated!');
            this.resetGame();
        }
    }
    
    restPhase() {
        this.addEvent('Rest phase');
        
        // 恢复生命和法力
        this.player.health = Math.min(100, this.player.health + this.config.healthRegenPerTurn);
        this.player.mana = Math.min(50, this.player.mana + this.config.manaRegenPerTurn);
        
        // 获得经验
        this.gainExperience(20);
        
        // 回到准备阶段
        this.game.phase = 'preparation';
        this.addEvent('Returning to preparation phase');
    }
    
    spawnEnemy() {
        const enemy = {
            id: Date.now(),
            health: 30 + (this.player.level * 10),
            damage: 15 + (this.player.level * 5)
        };
        this.game.enemies.push(enemy);
        this.addEvent(\`Enemy spawned (HP: \${enemy.health})\`);
    }
    
    playerAttack() {
        if (this.game.enemies.length === 0) return;
        
        this.player.mana -= 10;
        const damage = 25 + (this.player.level * 5);
        const enemy = this.game.enemies[0];
        
        enemy.health -= damage;
        this.addEvent(\`Player attacks for \${damage} damage\`);
        
        if (enemy.health <= 0) {
            this.game.enemies.shift();
            this.addEvent('Enemy defeated!');
            this.gainExperience(50);
        }
    }
    
    enemyAttack() {
        if (this.game.enemies.length === 0) return;
        
        const enemy = this.game.enemies[0];
        this.player.health -= enemy.damage;
        this.addEvent(\`Enemy attacks for \${enemy.damage} damage\`);
    }
    
    gainExperience(exp) {
        this.player.experience += exp;
        this.addEvent(\`Gained \${exp} experience\`);
        
        while (this.player.experience >= this.config.expToNextLevel) {
            this.player.experience -= this.config.expToNextLevel;
            this.player.level++;
            this.config.expToNextLevel += 50;
            this.addEvent(\`Level up! Now level \${this.player.level}\`);
        }
    }
    
    addEvent(message) {
        this.game.events.push({
            turn: this.game.turn,
            message,
            timestamp: Date.now()
        });
        console.log(\`[Turn \${this.game.turn}] \${message}\`);
    }
    
    resetGame() {
        this.player = {
            health: 100,
            mana: 50,
            level: 1,
            experience: 0,
            inventory: []
        };
        this.game = {
            turn: 0,
            phase: 'preparation',
            enemies: [],
            events: []
        };
    }
    
    getState() {
        return {
            player: { ...this.player },
            game: {
                turn: this.game.turn,
                phase: this.game.phase,
                enemyCount: this.game.enemies.length,
                eventCount: this.game.events.length
            }
        };
    }
}

// 测试序列
async function testComplexState() {
    console.log('🧪 测试复杂状态依赖...');
    
    const game = new GameState();
    
    console.log('初始状态:', game.getState());
    
    // 执行10个回合
    for (let i = 0; i < 10; i++) {
        game.nextTurn();
        console.log(\`回合 \${i + 1} 后状态:\`, game.getState());
        
        // 添加延迟以便观察
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\\n❓ 复杂状态预测问题:');
    console.log('1. 在第3回合时，游戏处于什么阶段？');
    console.log('2. 玩家什么时候会升级？');
    console.log('3. 敌人的生命值如何计算？');
    console.log('4. 如果玩家法力不足，会发生什么？');
}

testComplexState();
`;

    fs.writeFileSync(__dirname + '/state-test-3-complex.js', testCode);
    console.log('✅ 复杂状态测试生成完成');
}

// 并发状态冲突测试
function generateConcurrentStateTest() {
    console.log('✅ 并发状态测试生成完成 (待实现)');
}

// 运行生成器
generateStateTests();
