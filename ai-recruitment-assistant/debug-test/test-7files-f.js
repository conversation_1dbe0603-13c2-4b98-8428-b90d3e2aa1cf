// test-7files-f.js - 命令处理器
const User = require('./test-7files-c');
const Order = require('./test-7files-d');
const EventHandler = require('./test-7files-e');

class CommandHandler {
    constructor() {
        this.eventHandler = new EventHandler();
        this.commandHistory = [];
        this.commandId = 0;
    }

    async executeCommand(command) {
        this.commandId++;
        const commandExecution = {
            id: this.commandId,
            type: command.type,
            data: command.data,
            timestamp: Date.now(),
            status: 'executing'
        };

        this.commandHistory.push(commandExecution);
        console.log(`CommandHandler: Executing command ${command.type} (ID: ${this.commandId})`);

        try {
            let result;

            switch (command.type) {
                case 'CreateUser':
                    result = await this.handleCreateUser(command.data);
                    break;
                case 'ActivateUser':
                    result = await this.handleActivateUser(command.data);
                    break;
                case 'LoginUser':
                    result = await this.handleLoginUser(command.data);
                    break;
                case 'CreateOrder':
                    result = await this.handleCreateOrder(command.data);
                    break;
                case 'AddOrderItem':
                    result = await this.handleAddOrderItem(command.data);
                    break;
                case 'SubmitOrder':
                    result = await this.handleSubmitOrder(command.data);
                    break;
                case 'ConfirmOrder':
                    result = await this.handleConfirmOrder(command.data);
                    break;
                case 'ShipOrder':
                    result = await this.handleShipOrder(command.data);
                    break;
                default:
                    throw new Error(`Unknown command type: ${command.type}`);
            }

            commandExecution.status = 'completed';
            commandExecution.result = result;
            console.log(`CommandHandler: Command ${command.type} completed successfully`);

            return result;

        } catch (error) {
            commandExecution.status = 'failed';
            commandExecution.error = error.message;
            console.error(`CommandHandler: Command ${command.type} failed: ${error.message}`);
            throw error;
        }
    }

    async handleCreateUser(data) {
        const { id, email, name } = data;
        const user = await User.create(id, email, name, this.eventHandler.eventStore);
        await user.save();
        return user.getInfo();
    }

    async handleActivateUser(data) {
        const { userId } = data;
        const user = await User.load(userId, this.eventHandler.eventStore);
        user.activate();
        await user.save();
        return user.getInfo();
    }

    async handleLoginUser(data) {
        const { userId } = data;
        const user = await User.load(userId, this.eventHandler.eventStore);
        user.login();
        await user.save();
        return user.getInfo();
    }

    async handleCreateOrder(data) {
        const { id, userId } = data;
        const order = await Order.create(id, userId, this.eventHandler.eventStore);
        await order.save();
        return order.getInfo();
    }

    async handleAddOrderItem(data) {
        const { orderId, productId, quantity, price } = data;
        const order = await Order.load(orderId, this.eventHandler.eventStore);
        order.addItem(productId, quantity, price);
        await order.save();
        return order.getInfo();
    }

    async handleSubmitOrder(data) {
        const { orderId } = data;
        const order = await Order.load(orderId, this.eventHandler.eventStore);
        order.setShippingAddress(data.shippingAddress || { street: '123 Main St', city: 'City' });
        order.submit();
        await order.save();
        return order.getInfo();
    }

    async handleConfirmOrder(data) {
        const { orderId } = data;
        const order = await Order.load(orderId, this.eventHandler.eventStore);
        order.confirm();
        await order.save();
        return order.getInfo();
    }

    async handleShipOrder(data) {
        const { orderId } = data;
        const order = await Order.load(orderId, this.eventHandler.eventStore);
        order.ship();
        await order.save();
        return order.getInfo();
    }

    getCommandHistory() {
        return this.commandHistory.slice(-10); // Last 10 commands
    }

    getStats() {
        return {
            totalCommands: this.commandHistory.length,
            successfulCommands: this.commandHistory.filter(c => c.status === 'completed').length,
            failedCommands: this.commandHistory.filter(c => c.status === 'failed').length,
            eventHandler: this.eventHandler.getStats()
        };
    }
}
module.exports = CommandHandler;