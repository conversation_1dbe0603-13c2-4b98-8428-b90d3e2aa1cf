const { createClient } = require("@supabase/supabase-js");
require("dotenv").config({ path: "../.env.local" });

// 测试数据库状态检查
async function testDatabaseState() {
  console.log("🔍 测试数据库状态检查...");

  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  // 1. 检查会话表状态
  console.log("\n📊 检查会话表状态:");
  const { data: sessions, error: sessionsError } = await supabase
    .from("chat_sessions")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(5);

  if (sessionsError) {
    console.error("❌ 会话查询错误:", sessionsError);
  } else {
    console.log("✅ 最近5个会话:", sessions);
  }

  // 2. 检查消息表状态
  console.log("\n💬 检查消息表状态:");
  const { data: messages, error: messagesError } = await supabase
    .from("chat_messages")
    .select("*")
    .order("timestamp", { ascending: false })
    .limit(10);

  if (messagesError) {
    console.error("❌ 消息查询错误:", messagesError);
  } else {
    console.log("✅ 最近10条消息:", messages);
  }

  // 3. 测试特定会话的消息数量
  if (sessions && sessions.length > 0) {
    const testSessionId = sessions[0].id;
    console.log(`\n🎯 测试会话 ${testSessionId} 的消息数量:`);

    const { data: sessionMessages, error: sessionError } = await supabase
      .from("chat_messages")
      .select("*")
      .eq("session_id", testSessionId)
      .eq("message_type", "user");

    if (sessionError) {
      console.error("❌ 会话消息查询错误:", sessionError);
    } else {
      console.log(`✅ 用户消息数量: ${sessionMessages.length}`);
      console.log("📝 用户消息列表:", sessionMessages);
    }
  }
}

// 运行测试
testDatabaseState().catch(console.error);
