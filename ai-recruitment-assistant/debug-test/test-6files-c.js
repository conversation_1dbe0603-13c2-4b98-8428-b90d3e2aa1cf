// test-6files-c.js - 服务注册中心
const MessageQueue = require('./test-6files-b');

class ServiceRegistry {
    constructor() {
        this.messageQueue = new MessageQueue();
        this.services = new Map();
        this.healthChecks = new Map();
        this.serviceStats = new Map();

        this.messageQueue.createQueue('health-check', this.performHealthCheck.bind(this));
        this.messageQueue.createQueue('service-update', this.updateServiceStats.bind(this));

        this.messageQueue.eventBus.on('message.processed', (event) => {
            console.log(`ServiceRegistry: Message processed in queue ${event.data.queue}`);
        });
    }

    registerService(serviceName, endpoint, metadata = {}) {
        const service = {
            name: serviceName,
            endpoint,
            metadata,
            registeredAt: Date.now(),
            lastHealthCheck: null,
            status: 'unknown'
        };

        this.services.set(serviceName, service);
        this.serviceStats.set(serviceName, { requests: 0, errors: 0, uptime: 0 });

        console.log(`ServiceRegistry: Registered service '${serviceName}' at ${endpoint}`);

        // Schedule health check
        this.scheduleHealthCheck(serviceName);

        return service;
    }

    scheduleHealthCheck(serviceName) {
        this.messageQueue.enqueue('health-check', { serviceName, timestamp: Date.now() });
    }

    async performHealthCheck(message) {
        const { serviceName } = message.content;
        const service = this.services.get(serviceName);

        if (!service) return;

        // Simulate health check
        await new Promise(resolve => setTimeout(resolve, 50));

        const isHealthy = Math.random() > 0.1; // 90% success rate
        service.status = isHealthy ? 'healthy' : 'unhealthy';
        service.lastHealthCheck = Date.now();

        console.log(`ServiceRegistry: Health check for '${serviceName}': ${service.status}`);

        this.messageQueue.eventBus.emit('service.health-checked', { serviceName, status: service.status });
    }

    async updateServiceStats(message) {
        const { serviceName, type, value } = message.content;
        const stats = this.serviceStats.get(serviceName);

        if (stats) {
            stats[type] = (stats[type] || 0) + (value || 1);
            console.log(`ServiceRegistry: Updated ${type} for '${serviceName}'`);
        }
    }

    getServiceInfo(serviceName) {
        return {
            service: this.services.get(serviceName),
            stats: this.serviceStats.get(serviceName)
        };
    }

    getStats() {
        return {
            totalServices: this.services.size,
            healthyServices: Array.from(this.services.values()).filter(s => s.status === 'healthy').length,
            messageQueue: this.messageQueue.getStats()
        };
    }
}
module.exports = ServiceRegistry;