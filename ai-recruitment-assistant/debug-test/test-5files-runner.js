// test-5files-runner.js
const APIGateway = require('./test-5files-e');

async function test5Files() {
    console.log('🧪 测试5文件跨度 - 微服务架构...');
    
    const gateway = new APIGateway();
    
    // 测试请求序列
    const requests = [
        { userId: 'user1', action: 'profile', ip: '***********' },
        { userId: 'user1', action: 'profile', ip: '***********' }, // 应该命中缓存
        { userId: 'user2', action: 'orders', ip: '***********' },
        { userId: 'user1', action: 'settings', ip: '***********' },
        { userId: 'user3', action: 'invalid', ip: '***********' }, // 应该出错
        { userId: 'user2', action: 'profile', ip: '***********' }
    ];
    
    console.log('📊 执行API请求序列:');
    for (let i = 0; i < requests.length; i++) {
        const result = await gateway.handleAPIRequest(requests[i]);
        console.log(`${i + 1}. API请求结果:`, JSON.stringify(result, null, 2));
        
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('📈 完整系统统计:', JSON.stringify(gateway.getFullStats(), null, 2));
    
    console.log('\n❓ 5文件跨度问题:');
    console.log('1. 第二个user1的profile请求为什么更快？');
    console.log('2. 数据库连接池的最大连接数是多少？');
    console.log('3. 缓存命中率是多少？');
    console.log('4. 日志系统记录了多少条ERROR级别的日志？');
    console.log('5. API网关的速率限制机制如何工作？');
}

test5Files();