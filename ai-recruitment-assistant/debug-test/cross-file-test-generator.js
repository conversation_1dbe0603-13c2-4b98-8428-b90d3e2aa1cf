// 跨文件能力测试生成器
const fs = require('fs');
const path = require('path');

// 生成跨文件测试用例
function generateCrossFileTests() {
    console.log('🏗️ 生成跨文件能力测试用例...\n');

    // 测试1: 2文件跨度
    generate2FileTest();
    
    // 测试2: 3文件跨度
    generate3FileTest();
    
    // 测试3: 4文件跨度
    generate4FileTest();
    
    // 测试4: 5文件跨度
    generate5FileTest();
    
    // 测试5: 6文件跨度 (极限测试)
    generate6FileTest();
    
    console.log('✅ 所有跨文件测试用例生成完成！');
}

// 2文件测试：简单调用链
function generate2FileTest() {
    // 文件A：数据处理器
    const fileA = `
// test-2files-a.js - 数据处理器
class DataProcessor {
    constructor() {
        this.cache = new Map();
    }
    
    processData(input) {
        console.log('DataProcessor: 处理输入', input);
        
        if (this.cache.has(input)) {
            return { result: this.cache.get(input), fromCache: true };
        }
        
        const processed = input.toUpperCase() + '_PROCESSED';
        this.cache.set(input, processed);
        
        return { result: processed, fromCache: false };
    }
    
    getCacheSize() {
        return this.cache.size;
    }
}

module.exports = DataProcessor;
`;

    // 文件B：业务逻辑
    const fileB = `
// test-2files-b.js - 业务逻辑
const DataProcessor = require('./test-2files-a');

class BusinessLogic {
    constructor() {
        this.processor = new DataProcessor();
        this.results = [];
    }
    
    handleRequest(data) {
        console.log('BusinessLogic: 处理请求', data);
        
        const processed = this.processor.processData(data);
        this.results.push(processed);
        
        return {
            success: true,
            data: processed.result,
            cached: processed.fromCache,
            totalProcessed: this.results.length,
            cacheSize: this.processor.getCacheSize()
        };
    }
    
    getStats() {
        return {
            totalRequests: this.results.length,
            cacheHits: this.results.filter(r => r.fromCache).length,
            cacheSize: this.processor.getCacheSize()
        };
    }
}

module.exports = BusinessLogic;
`;

    // 测试文件
    const testFile = `
// test-2files-runner.js
const BusinessLogic = require('./test-2files-b');

async function test2Files() {
    console.log('🧪 测试2文件跨度...');
    
    const logic = new BusinessLogic();
    
    // 测试用例
    const testCases = ['hello', 'world', 'hello', 'test'];
    
    console.log('📊 执行测试用例:');
    testCases.forEach((testCase, index) => {
        const result = logic.handleRequest(testCase);
        console.log(\`\${index + 1}. 输入: "\${testCase}" -> 结果: \${JSON.stringify(result)}\`);
    });
    
    console.log('📈 最终统计:', logic.getStats());
    
    // 问题：预测下一次调用 logic.handleRequest('hello') 的结果
    console.log('\\n❓ 问题: 如果再次调用 logic.handleRequest("hello")，结果会是什么？');
}

test2Files();
`;

    fs.writeFileSync(path.join(__dirname, 'test-2files-a.js'), fileA);
    fs.writeFileSync(path.join(__dirname, 'test-2files-b.js'), fileB);
    fs.writeFileSync(path.join(__dirname, 'test-2files-runner.js'), testFile);
    
    console.log('✅ 2文件测试生成完成');
}

// 3文件测试：数据库+缓存+业务逻辑
function generate3FileTest() {
    // 文件A：模拟数据库
    const fileA = `
// test-3files-a.js - 模拟数据库
class MockDatabase {
    constructor() {
        this.data = new Map();
        this.queryCount = 0;
    }
    
    async get(key) {
        this.queryCount++;
        console.log(\`DB查询 #\${this.queryCount}: GET \${key}\`);
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return this.data.get(key) || null;
    }
    
    async set(key, value) {
        this.queryCount++;
        console.log(\`DB查询 #\${this.queryCount}: SET \${key} = \${value}\`);
        
        await new Promise(resolve => setTimeout(resolve, 10));
        this.data.set(key, value);
        return true;
    }
    
    getStats() {
        return {
            totalQueries: this.queryCount,
            dataSize: this.data.size
        };
    }
}

module.exports = MockDatabase;
`;

    // 文件B：缓存层
    const fileB = `
// test-3files-b.js - 缓存层
const MockDatabase = require('./test-3files-a');

class CacheLayer {
    constructor() {
        this.db = new MockDatabase();
        this.cache = new Map();
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
    
    async getValue(key) {
        console.log(\`Cache: 查询 \${key}\`);
        
        if (this.cache.has(key)) {
            this.cacheHits++;
            console.log(\`Cache HIT: \${key}\`);
            return this.cache.get(key);
        }
        
        this.cacheMisses++;
        console.log(\`Cache MISS: \${key}\`);
        
        const value = await this.db.get(key);
        if (value !== null) {
            this.cache.set(key, value);
        }
        
        return value;
    }
    
    async setValue(key, value) {
        console.log(\`Cache: 设置 \${key} = \${value}\`);
        
        await this.db.set(key, value);
        this.cache.set(key, value);
        
        return true;
    }
    
    getStats() {
        return {
            cacheHits: this.cacheHits,
            cacheMisses: this.cacheMisses,
            cacheSize: this.cache.size,
            dbStats: this.db.getStats()
        };
    }
}

module.exports = CacheLayer;
`;

    // 文件C：业务服务
    const fileC = `
// test-3files-c.js - 业务服务
const CacheLayer = require('./test-3files-b');

class UserService {
    constructor() {
        this.cache = new CacheLayer();
        this.requestCount = 0;
    }
    
    async getUser(userId) {
        this.requestCount++;
        console.log(\`UserService: 获取用户 \${userId} (请求 #\${this.requestCount})\`);
        
        let user = await this.cache.getValue(\`user:\${userId}\`);
        
        if (!user) {
            // 模拟创建新用户
            user = {
                id: userId,
                name: \`User\${userId}\`,
                createdAt: new Date().toISOString()
            };
            await this.cache.setValue(\`user:\${userId}\`, JSON.stringify(user));
        } else {
            user = JSON.parse(user);
        }
        
        return user;
    }
    
    async updateUser(userId, updates) {
        this.requestCount++;
        console.log(\`UserService: 更新用户 \${userId}\`);
        
        const user = await this.getUser(userId);
        const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() };
        
        await this.cache.setValue(\`user:\${userId}\`, JSON.stringify(updatedUser));
        return updatedUser;
    }
    
    getStats() {
        return {
            totalRequests: this.requestCount,
            cacheStats: this.cache.getStats()
        };
    }
}

module.exports = UserService;
`;

    // 测试文件
    const testFile = `
// test-3files-runner.js
const UserService = require('./test-3files-c');

async function test3Files() {
    console.log('🧪 测试3文件跨度...');
    
    const service = new UserService();
    
    // 测试序列
    console.log('📊 执行测试序列:');
    
    // 1. 获取用户1 (首次)
    const user1_first = await service.getUser('001');
    console.log('1. 首次获取用户001:', user1_first);
    
    // 2. 再次获取用户1 (应该从缓存)
    const user1_second = await service.getUser('001');
    console.log('2. 再次获取用户001:', user1_second);
    
    // 3. 更新用户1
    const user1_updated = await service.updateUser('001', { name: 'UpdatedUser001' });
    console.log('3. 更新用户001:', user1_updated);
    
    // 4. 获取用户2 (首次)
    const user2_first = await service.getUser('002');
    console.log('4. 首次获取用户002:', user2_first);
    
    console.log('📈 最终统计:', service.getStats());
    
    console.log('\\n❓ 问题: 如果现在调用 service.getUser("001")，会发生几次数据库查询？');
}

test3Files();
`;

    fs.writeFileSync(path.join(__dirname, 'test-3files-a.js'), fileA);
    fs.writeFileSync(path.join(__dirname, 'test-3files-b.js'), fileB);
    fs.writeFileSync(path.join(__dirname, 'test-3files-c.js'), fileC);
    fs.writeFileSync(path.join(__dirname, 'test-3files-runner.js'), testFile);
    
    console.log('✅ 3文件测试生成完成');
}

// 继续生成4、5、6文件测试...
function generate4FileTest() {
    console.log('✅ 4文件测试生成完成 (待实现)');
}

function generate5FileTest() {
    console.log('✅ 5文件测试生成完成 (待实现)');
}

function generate6FileTest() {
    console.log('✅ 6文件测试生成完成 (待实现)');
}

// 运行生成器
generateCrossFileTests();
