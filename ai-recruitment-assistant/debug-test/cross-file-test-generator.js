// 跨文件能力测试生成器
const fs = require("fs");
const path = require("path");

// 生成跨文件测试用例
function generateCrossFileTests() {
  console.log("🏗️ 生成跨文件能力测试用例...\n");

  // 测试1: 2文件跨度
  generate2FileTest();

  // 测试2: 3文件跨度
  generate3FileTest();

  // 测试3: 4文件跨度
  generate4FileTest();

  // 测试4: 5文件跨度
  generate5FileTest();

  // 测试5: 6文件跨度 (极限测试)
  generate6FileTest();

  console.log("✅ 所有跨文件测试用例生成完成！");
}

// 2文件测试：简单调用链
function generate2FileTest() {
  // 文件A：数据处理器
  const fileA = `
// test-2files-a.js - 数据处理器
class DataProcessor {
    constructor() {
        this.cache = new Map();
    }
    
    processData(input) {
        console.log('DataProcessor: 处理输入', input);
        
        if (this.cache.has(input)) {
            return { result: this.cache.get(input), fromCache: true };
        }
        
        const processed = input.toUpperCase() + '_PROCESSED';
        this.cache.set(input, processed);
        
        return { result: processed, fromCache: false };
    }
    
    getCacheSize() {
        return this.cache.size;
    }
}

module.exports = DataProcessor;
`;

  // 文件B：业务逻辑
  const fileB = `
// test-2files-b.js - 业务逻辑
const DataProcessor = require('./test-2files-a');

class BusinessLogic {
    constructor() {
        this.processor = new DataProcessor();
        this.results = [];
    }
    
    handleRequest(data) {
        console.log('BusinessLogic: 处理请求', data);
        
        const processed = this.processor.processData(data);
        this.results.push(processed);
        
        return {
            success: true,
            data: processed.result,
            cached: processed.fromCache,
            totalProcessed: this.results.length,
            cacheSize: this.processor.getCacheSize()
        };
    }
    
    getStats() {
        return {
            totalRequests: this.results.length,
            cacheHits: this.results.filter(r => r.fromCache).length,
            cacheSize: this.processor.getCacheSize()
        };
    }
}

module.exports = BusinessLogic;
`;

  // 测试文件
  const testFile = `
// test-2files-runner.js
const BusinessLogic = require('./test-2files-b');

async function test2Files() {
    console.log('🧪 测试2文件跨度...');
    
    const logic = new BusinessLogic();
    
    // 测试用例
    const testCases = ['hello', 'world', 'hello', 'test'];
    
    console.log('📊 执行测试用例:');
    testCases.forEach((testCase, index) => {
        const result = logic.handleRequest(testCase);
        console.log(\`\${index + 1}. 输入: "\${testCase}" -> 结果: \${JSON.stringify(result)}\`);
    });
    
    console.log('📈 最终统计:', logic.getStats());
    
    // 问题：预测下一次调用 logic.handleRequest('hello') 的结果
    console.log('\\n❓ 问题: 如果再次调用 logic.handleRequest("hello")，结果会是什么？');
}

test2Files();
`;

  fs.writeFileSync(path.join(__dirname, "test-2files-a.js"), fileA);
  fs.writeFileSync(path.join(__dirname, "test-2files-b.js"), fileB);
  fs.writeFileSync(path.join(__dirname, "test-2files-runner.js"), testFile);

  console.log("✅ 2文件测试生成完成");
}

// 3文件测试：数据库+缓存+业务逻辑
function generate3FileTest() {
  // 文件A：模拟数据库
  const fileA = `
// test-3files-a.js - 模拟数据库
class MockDatabase {
    constructor() {
        this.data = new Map();
        this.queryCount = 0;
    }
    
    async get(key) {
        this.queryCount++;
        console.log(\`DB查询 #\${this.queryCount}: GET \${key}\`);
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return this.data.get(key) || null;
    }
    
    async set(key, value) {
        this.queryCount++;
        console.log(\`DB查询 #\${this.queryCount}: SET \${key} = \${value}\`);
        
        await new Promise(resolve => setTimeout(resolve, 10));
        this.data.set(key, value);
        return true;
    }
    
    getStats() {
        return {
            totalQueries: this.queryCount,
            dataSize: this.data.size
        };
    }
}

module.exports = MockDatabase;
`;

  // 文件B：缓存层
  const fileB = `
// test-3files-b.js - 缓存层
const MockDatabase = require('./test-3files-a');

class CacheLayer {
    constructor() {
        this.db = new MockDatabase();
        this.cache = new Map();
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
    
    async getValue(key) {
        console.log(\`Cache: 查询 \${key}\`);
        
        if (this.cache.has(key)) {
            this.cacheHits++;
            console.log(\`Cache HIT: \${key}\`);
            return this.cache.get(key);
        }
        
        this.cacheMisses++;
        console.log(\`Cache MISS: \${key}\`);
        
        const value = await this.db.get(key);
        if (value !== null) {
            this.cache.set(key, value);
        }
        
        return value;
    }
    
    async setValue(key, value) {
        console.log(\`Cache: 设置 \${key} = \${value}\`);
        
        await this.db.set(key, value);
        this.cache.set(key, value);
        
        return true;
    }
    
    getStats() {
        return {
            cacheHits: this.cacheHits,
            cacheMisses: this.cacheMisses,
            cacheSize: this.cache.size,
            dbStats: this.db.getStats()
        };
    }
}

module.exports = CacheLayer;
`;

  // 文件C：业务服务
  const fileC = `
// test-3files-c.js - 业务服务
const CacheLayer = require('./test-3files-b');

class UserService {
    constructor() {
        this.cache = new CacheLayer();
        this.requestCount = 0;
    }
    
    async getUser(userId) {
        this.requestCount++;
        console.log(\`UserService: 获取用户 \${userId} (请求 #\${this.requestCount})\`);
        
        let user = await this.cache.getValue(\`user:\${userId}\`);
        
        if (!user) {
            // 模拟创建新用户
            user = {
                id: userId,
                name: \`User\${userId}\`,
                createdAt: new Date().toISOString()
            };
            await this.cache.setValue(\`user:\${userId}\`, JSON.stringify(user));
        } else {
            user = JSON.parse(user);
        }
        
        return user;
    }
    
    async updateUser(userId, updates) {
        this.requestCount++;
        console.log(\`UserService: 更新用户 \${userId}\`);
        
        const user = await this.getUser(userId);
        const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() };
        
        await this.cache.setValue(\`user:\${userId}\`, JSON.stringify(updatedUser));
        return updatedUser;
    }
    
    getStats() {
        return {
            totalRequests: this.requestCount,
            cacheStats: this.cache.getStats()
        };
    }
}

module.exports = UserService;
`;

  // 测试文件
  const testFile = `
// test-3files-runner.js
const UserService = require('./test-3files-c');

async function test3Files() {
    console.log('🧪 测试3文件跨度...');
    
    const service = new UserService();
    
    // 测试序列
    console.log('📊 执行测试序列:');
    
    // 1. 获取用户1 (首次)
    const user1_first = await service.getUser('001');
    console.log('1. 首次获取用户001:', user1_first);
    
    // 2. 再次获取用户1 (应该从缓存)
    const user1_second = await service.getUser('001');
    console.log('2. 再次获取用户001:', user1_second);
    
    // 3. 更新用户1
    const user1_updated = await service.updateUser('001', { name: 'UpdatedUser001' });
    console.log('3. 更新用户001:', user1_updated);
    
    // 4. 获取用户2 (首次)
    const user2_first = await service.getUser('002');
    console.log('4. 首次获取用户002:', user2_first);
    
    console.log('📈 最终统计:', service.getStats());
    
    console.log('\\n❓ 问题: 如果现在调用 service.getUser("001")，会发生几次数据库查询？');
}

test3Files();
`;

  fs.writeFileSync(path.join(__dirname, "test-3files-a.js"), fileA);
  fs.writeFileSync(path.join(__dirname, "test-3files-b.js"), fileB);
  fs.writeFileSync(path.join(__dirname, "test-3files-c.js"), fileC);
  fs.writeFileSync(path.join(__dirname, "test-3files-runner.js"), testFile);

  console.log("✅ 3文件测试生成完成");
}

// 4文件测试：HTTP请求处理链
function generate4FileTest() {
  // 文件A：配置管理器
  const fileA = `
// test-4files-a.js - 配置管理器
class ConfigManager {
    constructor() {
        this.config = {
            server: { port: 3000, timeout: 5000 },
            database: { maxConnections: 10, retryAttempts: 3 },
            cache: { ttl: 300, maxSize: 1000 },
            security: { rateLimitPerMinute: 100, tokenExpiry: 3600 }
        };
        this.overrides = new Map();
    }

    get(path) {
        if (this.overrides.has(path)) {
            return this.overrides.get(path);
        }

        const keys = path.split('.');
        let value = this.config;
        for (const key of keys) {
            value = value?.[key];
        }
        return value;
    }

    set(path, value) {
        this.overrides.set(path, value);
        console.log(\`Config override: \${path} = \${value}\`);
    }

    getStats() {
        return {
            overrideCount: this.overrides.size,
            configKeys: Object.keys(this.config).length
        };
    }
}

module.exports = ConfigManager;
`;

  // 文件B：请求验证器
  const fileB = `
// test-4files-b.js - 请求验证器
const ConfigManager = require('./test-4files-a');

class RequestValidator {
    constructor() {
        this.config = new ConfigManager();
        this.requestCounts = new Map();
        this.blockedIPs = new Set();
    }

    validateRequest(request) {
        const { ip, method, path, headers } = request;

        // 检查IP是否被阻止
        if (this.blockedIPs.has(ip)) {
            return { valid: false, reason: 'IP_BLOCKED', code: 403 };
        }

        // 速率限制检查
        const rateLimit = this.config.get('security.rateLimitPerMinute');
        const currentCount = this.requestCounts.get(ip) || 0;

        if (currentCount >= rateLimit) {
            this.blockedIPs.add(ip);
            return { valid: false, reason: 'RATE_LIMIT_EXCEEDED', code: 429 };
        }

        // 更新请求计数
        this.requestCounts.set(ip, currentCount + 1);

        // 验证必需的头部
        if (!headers.authorization && path.startsWith('/api/protected')) {
            return { valid: false, reason: 'MISSING_AUTH', code: 401 };
        }

        return { valid: true, ip, method, path };
    }

    resetRateLimit(ip) {
        this.requestCounts.delete(ip);
        console.log(\`Rate limit reset for IP: \${ip}\`);
    }

    getStats() {
        return {
            blockedIPs: this.blockedIPs.size,
            trackedIPs: this.requestCounts.size,
            configStats: this.config.getStats()
        };
    }
}

module.exports = RequestValidator;
`;

  // 文件C：数据处理器
  const fileC = `
// test-4files-c.js - 数据处理器
const RequestValidator = require('./test-4files-b');

class DataProcessor {
    constructor() {
        this.validator = new RequestValidator();
        this.cache = new Map();
        this.processingQueue = [];
        this.isProcessing = false;
    }

    async processRequest(request) {
        // 验证请求
        const validation = this.validator.validateRequest(request);
        if (!validation.valid) {
            return {
                success: false,
                error: validation.reason,
                code: validation.code
            };
        }

        // 检查缓存
        const cacheKey = \`\${request.method}:\${request.path}\`;
        if (this.cache.has(cacheKey)) {
            return {
                success: true,
                data: this.cache.get(cacheKey),
                fromCache: true,
                processedBy: 'cache'
            };
        }

        // 添加到处理队列
        return new Promise((resolve) => {
            this.processingQueue.push({ request, resolve });
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessing || this.processingQueue.length === 0) {
            return;
        }

        this.isProcessing = true;
        const { request, resolve } = this.processingQueue.shift();

        // 模拟数据处理
        await new Promise(r => setTimeout(r, 50));

        const result = {
            success: true,
            data: \`Processed: \${request.path}\`,
            fromCache: false,
            processedBy: 'queue',
            timestamp: Date.now()
        };

        // 缓存结果
        const cacheKey = \`\${request.method}:\${request.path}\`;
        this.cache.set(cacheKey, result.data);

        this.isProcessing = false;
        resolve(result);

        // 继续处理队列
        if (this.processingQueue.length > 0) {
            setTimeout(() => this.processQueue(), 10);
        }
    }

    getStats() {
        return {
            cacheSize: this.cache.size,
            queueLength: this.processingQueue.length,
            isProcessing: this.isProcessing,
            validatorStats: this.validator.getStats()
        };
    }
}

module.exports = DataProcessor;
`;

  // 文件D：HTTP服务器
  const fileD = `
// test-4files-d.js - HTTP服务器
const DataProcessor = require('./test-4files-c');

class HTTPServer {
    constructor() {
        this.processor = new DataProcessor();
        this.requestLog = [];
        this.serverStats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            startTime: Date.now()
        };
    }

    async handleRequest(rawRequest) {
        this.serverStats.totalRequests++;

        const request = {
            ip: rawRequest.ip || '127.0.0.1',
            method: rawRequest.method || 'GET',
            path: rawRequest.path || '/',
            headers: rawRequest.headers || {},
            timestamp: Date.now()
        };

        this.requestLog.push(request);
        console.log(\`[\${request.timestamp}] \${request.method} \${request.path} from \${request.ip}\`);

        try {
            const result = await this.processor.processRequest(request);

            if (result.success) {
                this.serverStats.successfulRequests++;
            } else {
                this.serverStats.failedRequests++;
            }

            return {
                ...result,
                requestId: this.serverStats.totalRequests,
                serverTime: Date.now() - request.timestamp
            };
        } catch (error) {
            this.serverStats.failedRequests++;
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                code: 500,
                requestId: this.serverStats.totalRequests
            };
        }
    }

    getFullStats() {
        return {
            server: this.serverStats,
            processor: this.processor.getStats(),
            recentRequests: this.requestLog.slice(-5)
        };
    }
}

module.exports = HTTPServer;
`;

  // 测试文件
  const testFile = `
// test-4files-runner.js
const HTTPServer = require('./test-4files-d');

async function test4Files() {
    console.log('🧪 测试4文件跨度 - HTTP请求处理链...');

    const server = new HTTPServer();

    // 测试请求序列
    const requests = [
        { ip: '***********', method: 'GET', path: '/api/users', headers: {} },
        { ip: '***********', method: 'GET', path: '/api/users', headers: {} }, // 重复请求，应该从缓存
        { ip: '***********', method: 'POST', path: '/api/protected/data', headers: {} }, // 缺少认证
        { ip: '***********', method: 'POST', path: '/api/protected/data', headers: { authorization: 'Bearer token' } },
        { ip: '***********', method: 'GET', path: '/api/products', headers: {} }
    ];

    console.log('📊 执行请求序列:');
    for (let i = 0; i < requests.length; i++) {
        const result = await server.handleRequest(requests[i]);
        console.log(\`\${i + 1}. 请求结果:\`, JSON.stringify(result, null, 2));

        // 添加延迟观察异步处理
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('📈 完整统计:', JSON.stringify(server.getFullStats(), null, 2));

    console.log('\\n❓ 4文件跨度问题:');
    console.log('1. 如果***********再发送100个请求，会发生什么？');
    console.log('2. 第二个GET /api/users请求为什么比第一个快？');
    console.log('3. 配置管理器中有几个override？');
    console.log('4. 数据处理队列的工作机制是什么？');
}

test4Files();
`;

  fs.writeFileSync(path.join(__dirname, "test-4files-a.js"), fileA);
  fs.writeFileSync(path.join(__dirname, "test-4files-b.js"), fileB);
  fs.writeFileSync(path.join(__dirname, "test-4files-c.js"), fileC);
  fs.writeFileSync(path.join(__dirname, "test-4files-d.js"), fileD);
  fs.writeFileSync(path.join(__dirname, "test-4files-runner.js"), testFile);

  console.log("✅ 4文件测试生成完成");
}

// 运行生成器
generateCrossFileTests();
