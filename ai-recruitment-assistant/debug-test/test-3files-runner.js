
// test-3files-runner.js
const UserService = require('./test-3files-c');

async function test3Files() {
    console.log('🧪 测试3文件跨度...');
    
    const service = new UserService();
    
    // 测试序列
    console.log('📊 执行测试序列:');
    
    // 1. 获取用户1 (首次)
    const user1_first = await service.getUser('001');
    console.log('1. 首次获取用户001:', user1_first);
    
    // 2. 再次获取用户1 (应该从缓存)
    const user1_second = await service.getUser('001');
    console.log('2. 再次获取用户001:', user1_second);
    
    // 3. 更新用户1
    const user1_updated = await service.updateUser('001', { name: 'UpdatedUser001' });
    console.log('3. 更新用户001:', user1_updated);
    
    // 4. 获取用户2 (首次)
    const user2_first = await service.getUser('002');
    console.log('4. 首次获取用户002:', user2_first);
    
    console.log('📈 最终统计:', service.getStats());
    
    console.log('\n❓ 问题: 如果现在调用 service.getUser("001")，会发生几次数据库查询？');
}

test3Files();
