
// test-4files-a.js - 配置管理器
class ConfigManager {
    constructor() {
        this.config = {
            server: { port: 3000, timeout: 5000 },
            database: { maxConnections: 10, retryAttempts: 3 },
            cache: { ttl: 300, maxSize: 1000 },
            security: { rateLimitPerMinute: 100, tokenExpiry: 3600 }
        };
        this.overrides = new Map();
    }

    get(path) {
        if (this.overrides.has(path)) {
            return this.overrides.get(path);
        }

        const keys = path.split('.');
        let value = this.config;
        for (const key of keys) {
            value = value?.[key];
        }
        return value;
    }

    set(path, value) {
        this.overrides.set(path, value);
        console.log(`Config override: ${path} = ${value}`);
    }

    getStats() {
        return {
            overrideCount: this.overrides.size,
            configKeys: Object.keys(this.config).length
        };
    }
}

module.exports = ConfigManager;
