// test-5files-b.js - 数据库连接池
const LogService = require('./test-5files-a');

class DatabasePool {
    constructor() {
        this.logger = new LogService();
        this.connections = [];
        this.maxConnections = 5;
        this.activeConnections = 0;
        this.waitingQueue = [];
    }
    
    async getConnection() {
        this.logger.info('Requesting database connection');
        
        if (this.activeConnections < this.maxConnections) {
            const conn = this.createConnection();
            this.activeConnections++;
            this.logger.info(`Connection created. Active: ${this.activeConnections}`);
            return conn;
        }
        
        this.logger.warn('Connection pool full, adding to queue');
        return new Promise((resolve) => {
            this.waitingQueue.push(resolve);
        });
    }
    
    createConnection() {
        return {
            id: Date.now(),
            query: async (sql) => {
                this.logger.debug(`Executing query: ${sql}`);
                await new Promise(r => setTimeout(r, 30));
                return { rows: [`Result for: ${sql}`] };
            },
            close: () => {
                this.activeConnections--;
                this.logger.info(`Connection closed. Active: ${this.activeConnections}`);
                if (this.waitingQueue.length > 0) {
                    const resolve = this.waitingQueue.shift();
                    const conn = this.createConnection();
                    this.activeConnections++;
                    resolve(conn);
                }
            }
        };
    }
    
    getStats() {
        return {
            activeConnections: this.activeConnections,
            queueLength: this.waitingQueue.length,
            maxConnections: this.maxConnections,
            logStats: this.logger.getStats()
        };
    }
}
module.exports = DatabasePool;