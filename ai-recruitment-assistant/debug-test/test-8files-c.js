// test-8files-c.js - 认证插件
class AuthPlugin {
    constructor() {
        this.name = 'auth';
        this.version = '1.0.0';
        this.description = 'Authentication and authorization plugin';
        this.dependencies = [];
        this.users = new Map();
        this.sessions = new Map();
        this.permissions = new Map();
    }

    async activate(context) {
        this.context = context;

        console.log('AuthPlugin: Activating authentication plugin...');

        // Register hooks
        context.registerHook('auth.login', this.handleLogin.bind(this));
        context.registerHook('auth.logout', this.handleLogout.bind(this));
        context.registerHook('auth.check', this.checkAuth.bind(this));
        context.registerHook('request.auth', this.authenticateRequest.bind(this));

        // Register HTTP routes
        const http = context.registry.modules.get('http');
        if (http) {
            http.addRoute('/auth/login', this.loginRoute.bind(this));
            http.addRoute('/auth/logout', this.logoutRoute.bind(this));
            http.addRoute('/auth/profile', this.profileRoute.bind(this));

            // Add auth middleware
            http.addMiddleware(this.authMiddleware.bind(this));
        }

        // Initialize default users
        this.initializeDefaultUsers();

        console.log('AuthPlugin: Authentication plugin activated');
        return this;
    }

    initializeDefaultUsers() {
        this.users.set('admin', {
            id: 'admin',
            username: 'admin',
            password: 'admin123', // In real app, this would be hashed
            email: '<EMAIL>',
            roles: ['admin'],
            createdAt: Date.now()
        });

        this.users.set('user1', {
            id: 'user1',
            username: 'user1',
            password: 'password123',
            email: '<EMAIL>',
            roles: ['user'],
            createdAt: Date.now()
        });

        // Set permissions
        this.permissions.set('admin', ['read', 'write', 'delete', 'admin']);
        this.permissions.set('user', ['read', 'write']);

        console.log('AuthPlugin: Default users initialized');
    }

    async handleLogin(data) {
        const { username, password } = data;

        const user = this.users.get(username);
        if (!user || user.password !== password) {
            throw new Error('Invalid credentials');
        }

        const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const session = {
            id: sessionId,
            userId: user.id,
            username: user.username,
            roles: user.roles,
            createdAt: Date.now(),
            lastActivity: Date.now()
        };

        this.sessions.set(sessionId, session);

        console.log(`AuthPlugin: User ${username} logged in with session ${sessionId}`);

        return {
            success: true,
            sessionId,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                roles: user.roles
            }
        };
    }

    async handleLogout(data) {
        const { sessionId } = data;

        const session = this.sessions.get(sessionId);
        if (session) {
            this.sessions.delete(sessionId);
            console.log(`AuthPlugin: Session ${sessionId} logged out`);
            return { success: true };
        }

        return { success: false, error: 'Session not found' };
    }

    async checkAuth(data) {
        const { sessionId } = data;

        const session = this.sessions.get(sessionId);
        if (!session) {
            return { authenticated: false, error: 'Invalid session' };
        }

        // Update last activity
        session.lastActivity = Date.now();

        return {
            authenticated: true,
            session,
            permissions: this.getUserPermissions(session.roles)
        };
    }

    getUserPermissions(roles) {
        const permissions = new Set();
        for (const role of roles) {
            const rolePerms = this.permissions.get(role) || [];
            rolePerms.forEach(perm => permissions.add(perm));
        }
        return Array.from(permissions);
    }

    async authMiddleware(data) {
        // Skip auth for login route
        if (data.path === '/auth/login') {
            return data;
        }

        const sessionId = data.headers?.authorization?.replace('Bearer ', '');
        if (sessionId) {
            const authResult = await this.checkAuth({ sessionId });
            data.auth = authResult;
        }

        return data;
    }

    async loginRoute(data) {
        return await this.handleLogin(data);
    }

    async logoutRoute(data) {
        const sessionId = data.auth?.session?.id;
        if (!sessionId) {
            throw new Error('Not authenticated');
        }
        return await this.handleLogout({ sessionId });
    }

    async profileRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required');
        }

        const user = this.users.get(data.auth.session.username);
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            roles: user.roles,
            permissions: data.auth.permissions
        };
    }

    deactivate() {
        console.log('AuthPlugin: Deactivating authentication plugin...');
        this.sessions.clear();
    }

    getStats() {
        return {
            totalUsers: this.users.size,
            activeSessions: this.sessions.size,
            totalRoles: this.permissions.size
        };
    }
}

module.exports = {
    name: 'auth',
    version: '1.0.0',
    description: 'Authentication and authorization plugin',
    dependencies: [],
    activate: async (context) => {
        const plugin = new AuthPlugin();
        return await plugin.activate(context);
    }
};