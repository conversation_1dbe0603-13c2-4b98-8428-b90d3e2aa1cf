// test-6files-d.js - 负载均衡器
const ServiceRegistry = require('./test-6files-c');

class LoadBalancer {
    constructor() {
        this.registry = new ServiceRegistry();
        this.routingTable = new Map();
        this.requestCount = 0;
        this.algorithms = {
            'round-robin': this.roundRobinSelect.bind(this),
            'least-connections': this.leastConnectionsSelect.bind(this),
            'random': this.randomSelect.bind(this)
        };

        this.registry.messageQueue.eventBus.on('service.health-checked', (event) => {
            this.handleServiceHealthUpdate(event.data);
        });
    }

    addRoute(path, serviceName, algorithm = 'round-robin') {
        this.routingTable.set(path, {
            serviceName,
            algorithm,
            currentIndex: 0,
            connections: new Map()
        });

        console.log(`LoadBalancer: Added route ${path} -> ${serviceName} (${algorithm})`);
    }

    async routeRequest(path, requestData) {
        this.requestCount++;
        const route = this.routingTable.get(path);

        if (!route) {
            throw new Error(`No route found for path: ${path}`);
        }

        const serviceInfo = this.registry.getServiceInfo(route.serviceName);
        if (!serviceInfo.service || serviceInfo.service.status !== 'healthy') {
            throw new Error(`Service '${route.serviceName}' is not available`);
        }

        // Select instance using algorithm
        const algorithm = this.algorithms[route.algorithm];
        const selectedInstance = await algorithm(route, serviceInfo);

        // Simulate request processing
        const startTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        const responseTime = Date.now() - startTime;

        // Update stats
        this.registry.messageQueue.enqueue('service-update', {
            serviceName: route.serviceName,
            type: 'requests',
            value: 1
        });

        console.log(`LoadBalancer: Routed request ${this.requestCount} to ${route.serviceName} (took ${responseTime}ms)`);

        return {
            requestId: this.requestCount,
            service: route.serviceName,
            instance: selectedInstance,
            responseTime,
            path
        };
    }

    roundRobinSelect(route, serviceInfo) {
        route.currentIndex = (route.currentIndex + 1) % 3; // Assume 3 instances
        return `instance-${route.currentIndex}`;
    }

    leastConnectionsSelect(route, serviceInfo) {
        // Simplified: return instance with least connections
        let minConnections = Infinity;
        let selectedInstance = 'instance-0';

        for (let i = 0; i < 3; i++) {
            const instanceId = `instance-${i}`;
            const connections = route.connections.get(instanceId) || 0;
            if (connections < minConnections) {
                minConnections = connections;
                selectedInstance = instanceId;
            }
        }

        route.connections.set(selectedInstance, minConnections + 1);
        return selectedInstance;
    }

    randomSelect(route, serviceInfo) {
        return `instance-${Math.floor(Math.random() * 3)}`;
    }

    handleServiceHealthUpdate(data) {
        console.log(`LoadBalancer: Service ${data.serviceName} health status: ${data.status}`);
    }

    getStats() {
        return {
            totalRequests: this.requestCount,
            routes: Array.from(this.routingTable.keys()),
            registry: this.registry.getStats()
        };
    }
}
module.exports = LoadBalancer;