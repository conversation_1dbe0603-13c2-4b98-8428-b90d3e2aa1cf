// test-7files-c.js - 用户聚合
const AggregateRoot = require('./test-7files-b');

class User extends AggregateRoot {
    constructor(id) {
        super(id);
        this.email = null;
        this.name = null;
        this.status = 'inactive';
        this.loginCount = 0;
        this.lastLoginAt = null;
        this.preferences = {};
    }

    static async create(id, email, name, eventStore) {
        const user = new User(id);
        user.eventStore = eventStore;
        user.raiseEvent('UserCreated', { email, name });
        return user;
    }

    activate() {
        if (this.status === 'active') {
            throw new Error('User is already active');
        }
        this.raiseEvent('UserActivated', { activatedAt: Date.now() });
    }

    deactivate() {
        if (this.status === 'inactive') {
            throw new Error('User is already inactive');
        }
        this.raiseEvent('UserDeactivated', { deactivatedAt: Date.now() });
    }

    login() {
        if (this.status !== 'active') {
            throw new Error('Cannot login: user is not active');
        }
        this.raiseEvent('UserLoggedIn', { loginAt: Date.now() });
    }

    updatePreferences(preferences) {
        this.raiseEvent('UserPreferencesUpdated', { preferences });
    }

    // Event handlers
    onUserCreated(data) {
        this.email = data.email;
        this.name = data.name;
        this.status = 'inactive';
        console.log(`User: Created user ${this.id} with email ${data.email}`);
    }

    onUserActivated(data) {
        this.status = 'active';
        console.log(`User: Activated user ${this.id}`);
    }

    onUserDeactivated(data) {
        this.status = 'inactive';
        console.log(`User: Deactivated user ${this.id}`);
    }

    onUserLoggedIn(data) {
        this.loginCount++;
        this.lastLoginAt = data.loginAt;
        console.log(`User: User ${this.id} logged in (count: ${this.loginCount})`);
    }

    onUserPreferencesUpdated(data) {
        this.preferences = { ...this.preferences, ...data.preferences };
        console.log(`User: Updated preferences for user ${this.id}`);
    }

    getInfo() {
        return {
            id: this.id,
            email: this.email,
            name: this.name,
            status: this.status,
            loginCount: this.loginCount,
            lastLoginAt: this.lastLoginAt,
            version: this.version
        };
    }
}
module.exports = User;