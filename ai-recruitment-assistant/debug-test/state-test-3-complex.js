
// state-test-3-complex.js - 复杂状态依赖
class GameState {
    constructor() {
        this.player = {
            health: 100,
            mana: 50,
            level: 1,
            experience: 0,
            inventory: []
        };
        
        this.game = {
            turn: 0,
            phase: 'preparation', // preparation, battle, rest
            enemies: [],
            events: []
        };
        
        this.config = {
            expToNextLevel: 100,
            healthRegenPerTurn: 5,
            manaRegenPerTurn: 10
        };
    }
    
    nextTurn() {
        this.game.turn++;
        this.addEvent(`Turn ${this.game.turn} started`);
        
        // 根据当前阶段执行不同逻辑
        switch (this.game.phase) {
            case 'preparation':
                this.preparationPhase();
                break;
            case 'battle':
                this.battlePhase();
                break;
            case 'rest':
                this.restPhase();
                break;
        }
    }
    
    preparationPhase() {
        this.addEvent('Preparation phase');
        
        // 生成敌人
        if (this.game.turn % 3 === 0) {
            this.spawnEnemy();
        }
        
        // 切换到战斗阶段
        if (this.game.enemies.length > 0) {
            this.game.phase = 'battle';
            this.addEvent('Entering battle phase');
        }
    }
    
    battlePhase() {
        this.addEvent('Battle phase');
        
        // 玩家攻击
        if (this.player.mana >= 10) {
            this.playerAttack();
        }
        
        // 敌人攻击
        this.enemyAttack();
        
        // 检查战斗结束
        if (this.game.enemies.length === 0) {
            this.game.phase = 'rest';
            this.addEvent('Battle won, entering rest phase');
        } else if (this.player.health <= 0) {
            this.addEvent('Player defeated!');
            this.resetGame();
        }
    }
    
    restPhase() {
        this.addEvent('Rest phase');
        
        // 恢复生命和法力
        this.player.health = Math.min(100, this.player.health + this.config.healthRegenPerTurn);
        this.player.mana = Math.min(50, this.player.mana + this.config.manaRegenPerTurn);
        
        // 获得经验
        this.gainExperience(20);
        
        // 回到准备阶段
        this.game.phase = 'preparation';
        this.addEvent('Returning to preparation phase');
    }
    
    spawnEnemy() {
        const enemy = {
            id: Date.now(),
            health: 30 + (this.player.level * 10),
            damage: 15 + (this.player.level * 5)
        };
        this.game.enemies.push(enemy);
        this.addEvent(`Enemy spawned (HP: ${enemy.health})`);
    }
    
    playerAttack() {
        if (this.game.enemies.length === 0) return;
        
        this.player.mana -= 10;
        const damage = 25 + (this.player.level * 5);
        const enemy = this.game.enemies[0];
        
        enemy.health -= damage;
        this.addEvent(`Player attacks for ${damage} damage`);
        
        if (enemy.health <= 0) {
            this.game.enemies.shift();
            this.addEvent('Enemy defeated!');
            this.gainExperience(50);
        }
    }
    
    enemyAttack() {
        if (this.game.enemies.length === 0) return;
        
        const enemy = this.game.enemies[0];
        this.player.health -= enemy.damage;
        this.addEvent(`Enemy attacks for ${enemy.damage} damage`);
    }
    
    gainExperience(exp) {
        this.player.experience += exp;
        this.addEvent(`Gained ${exp} experience`);
        
        while (this.player.experience >= this.config.expToNextLevel) {
            this.player.experience -= this.config.expToNextLevel;
            this.player.level++;
            this.config.expToNextLevel += 50;
            this.addEvent(`Level up! Now level ${this.player.level}`);
        }
    }
    
    addEvent(message) {
        this.game.events.push({
            turn: this.game.turn,
            message,
            timestamp: Date.now()
        });
        console.log(`[Turn ${this.game.turn}] ${message}`);
    }
    
    resetGame() {
        this.player = {
            health: 100,
            mana: 50,
            level: 1,
            experience: 0,
            inventory: []
        };
        this.game = {
            turn: 0,
            phase: 'preparation',
            enemies: [],
            events: []
        };
    }
    
    getState() {
        return {
            player: { ...this.player },
            game: {
                turn: this.game.turn,
                phase: this.game.phase,
                enemyCount: this.game.enemies.length,
                eventCount: this.game.events.length
            }
        };
    }
}

// 测试序列
async function testComplexState() {
    console.log('🧪 测试复杂状态依赖...');
    
    const game = new GameState();
    
    console.log('初始状态:', game.getState());
    
    // 执行10个回合
    for (let i = 0; i < 10; i++) {
        game.nextTurn();
        console.log(`回合 ${i + 1} 后状态:`, game.getState());
        
        // 添加延迟以便观察
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n❓ 复杂状态预测问题:');
    console.log('1. 在第3回合时，游戏处于什么阶段？');
    console.log('2. 玩家什么时候会升级？');
    console.log('3. 敌人的生命值如何计算？');
    console.log('4. 如果玩家法力不足，会发生什么？');
}

testComplexState();
