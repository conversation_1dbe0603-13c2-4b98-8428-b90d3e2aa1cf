
// test-2files-a.js - 数据处理器
class DataProcessor {
    constructor() {
        this.cache = new Map();
    }
    
    processData(input) {
        console.log('DataProcessor: 处理输入', input);
        
        if (this.cache.has(input)) {
            return { result: this.cache.get(input), fromCache: true };
        }
        
        const processed = input.toUpperCase() + '_PROCESSED';
        this.cache.set(input, processed);
        
        return { result: processed, fromCache: false };
    }
    
    getCacheSize() {
        return this.cache.size;
    }
}

module.exports = DataProcessor;
