
// state-test-2-async.js - 异步状态管理
class AsyncTaskManager {
    constructor() {
        this.tasks = new Map();
        this.completedTasks = [];
        this.isProcessing = false;
        this.taskIdCounter = 0;
    }
    
    async addTask(name, duration) {
        const taskId = ++this.taskIdCounter;
        const task = {
            id: taskId,
            name,
            duration,
            status: 'pending',
            createdAt: Date.now(),
            startedAt: null,
            completedAt: null
        };
        
        this.tasks.set(taskId, task);
        console.log(`Task ${taskId} (${name}) added`);
        
        if (!this.isProcessing) {
            this.processNextTask();
        }
        
        return taskId;
    }
    
    async processNextTask() {
        if (this.isProcessing) {
            console.log('Already processing tasks');
            return;
        }
        
        const pendingTask = Array.from(this.tasks.values()).find(t => t.status === 'pending');
        if (!pendingTask) {
            console.log('No pending tasks');
            return;
        }
        
        this.isProcessing = true;
        pendingTask.status = 'running';
        pendingTask.startedAt = Date.now();
        
        console.log(`Processing task ${pendingTask.id} (${pendingTask.name})...`);
        
        // 模拟异步工作
        await new Promise(resolve => setTimeout(resolve, pendingTask.duration));
        
        pendingTask.status = 'completed';
        pendingTask.completedAt = Date.now();
        this.completedTasks.push(pendingTask);
        this.tasks.delete(pendingTask.id);
        
        console.log(`Task ${pendingTask.id} completed`);
        
        this.isProcessing = false;
        
        // 处理下一个任务
        if (this.tasks.size > 0) {
            setTimeout(() => this.processNextTask(), 10);
        }
    }
    
    getState() {
        return {
            pendingTasks: this.tasks.size,
            completedTasks: this.completedTasks.length,
            isProcessing: this.isProcessing,
            currentTask: Array.from(this.tasks.values()).find(t => t.status === 'running')?.id || null
        };
    }
}

// 测试序列
async function testAsyncState() {
    console.log('🧪 测试异步状态管理...');
    
    const manager = new AsyncTaskManager();
    
    console.log('初始状态:', manager.getState());
    
    // 添加任务
    await manager.addTask('Task A', 100);
    console.log('添加Task A后:', manager.getState());
    
    await manager.addTask('Task B', 50);
    console.log('添加Task B后:', manager.getState());
    
    await manager.addTask('Task C', 200);
    console.log('添加Task C后:', manager.getState());
    
    // 等待一段时间观察状态变化
    await new Promise(resolve => setTimeout(resolve, 150));
    console.log('150ms后状态:', manager.getState());
    
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log('350ms后状态:', manager.getState());
    
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log('450ms后状态:', manager.getState());
    
    console.log('\n❓ 异步状态预测问题:');
    console.log('1. 在150ms时，哪个任务正在运行？');
    console.log('2. 在350ms时，完成了几个任务？');
    console.log('3. 任务的执行顺序是什么？');
}

testAsyncState();
