// test-6files-b.js - 消息队列
const EventBus = require('./test-6files-a');

class MessageQueue {
    constructor() {
        this.eventBus = new EventBus();
        this.queues = new Map();
        this.workers = new Map();
        this.processingStats = { processed: 0, failed: 0 };

        this.eventBus.on('message.queued', (event) => {
            this.processMessage(event.data.queue, event.data.message);
        });
    }

    createQueue(queueName, workerFunction) {
        this.queues.set(queueName, []);
        this.workers.set(queueName, workerFunction);
        console.log(`MessageQueue: Created queue '${queueName}'`);
    }

    enqueue(queueName, message) {
        if (!this.queues.has(queueName)) {
            throw new Error(`Queue '${queueName}' does not exist`);
        }

        const queuedMessage = {
            id: Date.now(),
            content: message,
            queuedAt: Date.now(),
            attempts: 0
        };

        this.queues.get(queueName).push(queuedMessage);
        this.eventBus.emit('message.queued', { queue: queueName, message: queuedMessage });

        return queuedMessage.id;
    }

    async processMessage(queueName, message) {
        const worker = this.workers.get(queueName);
        if (!worker) return;

        message.attempts++;
        console.log(`MessageQueue: Processing message ${message.id} (attempt ${message.attempts})`);

        try {
            await worker(message);
            this.processingStats.processed++;
            this.eventBus.emit('message.processed', { queue: queueName, message });
        } catch (error) {
            this.processingStats.failed++;
            this.eventBus.emit('message.failed', { queue: queueName, message, error });
        }

        // Remove from queue
        const queue = this.queues.get(queueName);
        const index = queue.findIndex(m => m.id === message.id);
        if (index !== -1) {
            queue.splice(index, 1);
        }
    }

    getStats() {
        return {
            queues: Array.from(this.queues.keys()).map(name => ({
                name,
                size: this.queues.get(name).length
            })),
            processing: this.processingStats,
            eventBus: this.eventBus.getStats()
        };
    }
}
module.exports = MessageQueue;