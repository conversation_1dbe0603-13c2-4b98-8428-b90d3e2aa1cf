// test-7files-d.js - 订单聚合
const AggregateRoot = require('./test-7files-b');

class Order extends AggregateRoot {
    constructor(id) {
        super(id);
        this.userId = null;
        this.items = [];
        this.status = 'draft';
        this.totalAmount = 0;
        this.createdAt = null;
        this.shippingAddress = null;
    }

    static async create(id, userId, eventStore) {
        const order = new Order(id);
        order.eventStore = eventStore;
        order.raiseEvent('OrderCreated', { userId, createdAt: Date.now() });
        return order;
    }

    addItem(productId, quantity, price) {
        if (this.status !== 'draft') {
            throw new Error('Cannot add items to non-draft order');
        }

        this.raiseEvent('OrderItemAdded', {
            productId,
            quantity,
            price,
            lineTotal: quantity * price
        });
    }

    removeItem(productId) {
        if (this.status !== 'draft') {
            throw new Error('Cannot remove items from non-draft order');
        }

        const item = this.items.find(i => i.productId === productId);
        if (!item) {
            throw new Error('Item not found in order');
        }

        this.raiseEvent('OrderItemRemoved', { productId });
    }

    setShippingAddress(address) {
        if (this.status !== 'draft') {
            throw new Error('Cannot change shipping address for non-draft order');
        }

        this.raiseEvent('OrderShippingAddressSet', { address });
    }

    submit() {
        if (this.status !== 'draft') {
            throw new Error('Order is not in draft status');
        }

        if (this.items.length === 0) {
            throw new Error('Cannot submit empty order');
        }

        if (!this.shippingAddress) {
            throw new Error('Shipping address is required');
        }

        this.raiseEvent('OrderSubmitted', { submittedAt: Date.now() });
    }

    confirm() {
        if (this.status !== 'submitted') {
            throw new Error('Order is not submitted');
        }

        this.raiseEvent('OrderConfirmed', { confirmedAt: Date.now() });
    }

    ship() {
        if (this.status !== 'confirmed') {
            throw new Error('Order is not confirmed');
        }

        this.raiseEvent('OrderShipped', { shippedAt: Date.now() });
    }

    // Event handlers
    onOrderCreated(data) {
        this.userId = data.userId;
        this.createdAt = data.createdAt;
        this.status = 'draft';
        console.log(`Order: Created order ${this.id} for user ${data.userId}`);
    }

    onOrderItemAdded(data) {
        const existingItem = this.items.find(i => i.productId === data.productId);

        if (existingItem) {
            existingItem.quantity += data.quantity;
            existingItem.lineTotal += data.lineTotal;
        } else {
            this.items.push({
                productId: data.productId,
                quantity: data.quantity,
                price: data.price,
                lineTotal: data.lineTotal
            });
        }

        this.recalculateTotal();
        console.log(`Order: Added item ${data.productId} to order ${this.id}`);
    }

    onOrderItemRemoved(data) {
        this.items = this.items.filter(i => i.productId !== data.productId);
        this.recalculateTotal();
        console.log(`Order: Removed item ${data.productId} from order ${this.id}`);
    }

    onOrderShippingAddressSet(data) {
        this.shippingAddress = data.address;
        console.log(`Order: Set shipping address for order ${this.id}`);
    }

    onOrderSubmitted(data) {
        this.status = 'submitted';
        console.log(`Order: Submitted order ${this.id}`);
    }

    onOrderConfirmed(data) {
        this.status = 'confirmed';
        console.log(`Order: Confirmed order ${this.id}`);
    }

    onOrderShipped(data) {
        this.status = 'shipped';
        console.log(`Order: Shipped order ${this.id}`);
    }

    recalculateTotal() {
        this.totalAmount = this.items.reduce((sum, item) => sum + item.lineTotal, 0);
    }

    getInfo() {
        return {
            id: this.id,
            userId: this.userId,
            status: this.status,
            itemCount: this.items.length,
            totalAmount: this.totalAmount,
            version: this.version
        };
    }
}
module.exports = Order;