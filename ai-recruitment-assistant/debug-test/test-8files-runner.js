// test-8files-runner.js
const PluginApplication = require('./test-8files-h');

async function test8Files() {
    console.log('🧪 测试8文件跨度 - 插件系统架构...');

    const app = new PluginApplication();

    try {
        // 启动应用
        await app.start();

        // 模拟用户工作流程
        await app.simulateUserWorkflow();

        // 等待一些异步操作完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('📈 完整系统统计:', JSON.stringify(app.getFullStats(), null, 2));

        console.log('\n❓ 8文件跨度问题:');
        console.log('1. 插件注册表管理了多少个插件？');
        console.log('2. 核心系统初始化了哪些模块？');
        console.log('3. 认证插件创建了多少个会话？');
        console.log('4. 数据库插件执行了多少次查询？');
        console.log('5. 博客插件的依赖关系是什么？');
        console.log('6. 缓存插件的命中率是多少？');
        console.log('7. 通知插件发送了多少条通知？');
        console.log('8. 整个插件系统的加载顺序是什么？');
        console.log('9. Hook机制是如何在插件间传递数据的？');
        console.log('10. 插件依赖解析是如何工作的？');

        // 停止应用
        await app.stop();

    } catch (error) {
        console.error('测试过程中出现错误:', error);
    }
}

test8Files();