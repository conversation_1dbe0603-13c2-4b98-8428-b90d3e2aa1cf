// test-8files-f.js - 缓存插件
class CachePlugin {
    constructor() {
        this.name = 'cache';
        this.version = '1.0.0';
        this.description = 'Caching plugin with TTL support';
        this.dependencies = [];
        this.cache = new Map();
        this.ttls = new Map();
        this.hitCount = 0;
        this.missCount = 0;
    }

    async activate(context) {
        this.context = context;

        console.log('CachePlugin: Activating cache plugin...');

        // Register hooks
        context.registerHook('cache.get', this.get.bind(this));
        context.registerHook('cache.set', this.set.bind(this));
        context.registerHook('cache.delete', this.delete.bind(this));
        context.registerHook('cache.clear', this.clear.bind(this));

        // Register HTTP routes
        const http = context.registry.modules.get('http');
        if (http) {
            http.addRoute('/cache/stats', this.statsRoute.bind(this));
            http.addRoute('/cache/clear', this.clearRoute.bind(this));
        }

        // Start cleanup interval
        this.startCleanup();

        console.log('CachePlugin: Cache plugin activated');
        return this;
    }

    async get(data) {
        const { key } = data;

        // Check if key exists and not expired
        if (this.cache.has(key)) {
            const ttl = this.ttls.get(key);
            if (!ttl || Date.now() < ttl) {
                this.hitCount++;
                const value = this.cache.get(key);
                console.log(`CachePlugin: Cache HIT for key ${key}`);
                return { hit: true, value };
            } else {
                // Expired
                this.cache.delete(key);
                this.ttls.delete(key);
            }
        }

        this.missCount++;
        console.log(`CachePlugin: Cache MISS for key ${key}`);
        return { hit: false, value: null };
    }

    async set(data) {
        const { key, value, ttl } = data;

        this.cache.set(key, value);

        if (ttl && ttl > 0) {
            this.ttls.set(key, Date.now() + ttl);
        }

        console.log(`CachePlugin: Set cache key ${key}${ttl ? ` with TTL ${ttl}ms` : ''}`);
        return true;
    }

    async delete(data) {
        const { key } = data;

        const deleted = this.cache.delete(key);
        this.ttls.delete(key);

        console.log(`CachePlugin: Deleted cache key ${key}: ${deleted}`);
        return deleted;
    }

    async clear(data) {
        const size = this.cache.size;
        this.cache.clear();
        this.ttls.clear();

        console.log(`CachePlugin: Cleared cache (${size} keys)`);
        return { cleared: size };
    }

    startCleanup() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpired();
        }, 60000); // Cleanup every minute
    }

    cleanupExpired() {
        const now = Date.now();
        let cleaned = 0;

        for (const [key, ttl] of this.ttls.entries()) {
            if (now >= ttl) {
                this.cache.delete(key);
                this.ttls.delete(key);
                cleaned++;
            }
        }

        if (cleaned > 0) {
            console.log(`CachePlugin: Cleaned up ${cleaned} expired keys`);
        }
    }

    async statsRoute(data) {
        return this.getStats();
    }

    async clearRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required');
        }

        const permissions = data.auth.permissions || [];
        if (!permissions.includes('admin')) {
            throw new Error('Admin permission required');
        }

        return await this.clear({});
    }

    deactivate() {
        console.log('CachePlugin: Deactivating cache plugin...');
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.cache.clear();
        this.ttls.clear();
    }

    getStats() {
        return {
            totalKeys: this.cache.size,
            hitCount: this.hitCount,
            missCount: this.missCount,
            hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
            keysWithTTL: this.ttls.size
        };
    }
}

module.exports = {
    name: 'cache',
    version: '1.0.0',
    description: 'Caching plugin with TTL support',
    dependencies: [],
    activate: async (context) => {
        const plugin = new CachePlugin();
        return await plugin.activate(context);
    }
};