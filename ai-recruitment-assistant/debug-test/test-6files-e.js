// test-6files-e.js - API网关
const LoadBalancer = require('./test-6files-d');

class APIGateway {
    constructor() {
        this.loadBalancer = new LoadBalancer();
        this.middleware = [];
        this.requestLog = [];
        this.rateLimits = new Map();
        this.circuitBreakers = new Map();

        this.setupServices();
        this.setupRoutes();
    }

    setupServices() {
        // Register services
        this.loadBalancer.registry.registerService('user-service', 'http://user-service:8080', { version: '1.0' });
        this.loadBalancer.registry.registerService('order-service', 'http://order-service:8080', { version: '1.2' });
        this.loadBalancer.registry.registerService('payment-service', 'http://payment-service:8080', { version: '2.0' });
    }

    setupRoutes() {
        this.loadBalancer.addRoute('/api/users', 'user-service', 'round-robin');
        this.loadBalancer.addRoute('/api/orders', 'order-service', 'least-connections');
        this.loadBalancer.addRoute('/api/payments', 'payment-service', 'random');
    }

    use(middleware) {
        this.middleware.push(middleware);
    }

    async handleRequest(request) {
        const { path, method, headers, body, clientId } = request;

        // Log request
        this.requestLog.push({
            timestamp: Date.now(),
            path,
            method,
            clientId
        });

        try {
            // Apply middleware
            for (const mw of this.middleware) {
                const result = await mw(request);
                if (!result.continue) {
                    return result.response;
                }
            }

            // Check rate limits
            if (!this.checkRateLimit(clientId)) {
                return { status: 429, error: 'Rate limit exceeded' };
            }

            // Check circuit breaker
            if (this.isCircuitOpen(path)) {
                return { status: 503, error: 'Service temporarily unavailable' };
            }

            // Route request
            const response = await this.loadBalancer.routeRequest(path, { method, headers, body });

            this.updateCircuitBreaker(path, true);
            return { status: 200, data: response };

        } catch (error) {
            this.updateCircuitBreaker(path, false);
            console.error(`APIGateway: Request failed`, error.message);
            return { status: 500, error: error.message };
        }
    }

    checkRateLimit(clientId) {
        const now = Date.now();
        const windowMs = 60000; // 1 minute
        const maxRequests = 100;

        if (!this.rateLimits.has(clientId)) {
            this.rateLimits.set(clientId, []);
        }

        const requests = this.rateLimits.get(clientId);
        const validRequests = requests.filter(time => now - time < windowMs);

        if (validRequests.length >= maxRequests) {
            return false;
        }

        validRequests.push(now);
        this.rateLimits.set(clientId, validRequests);
        return true;
    }

    isCircuitOpen(path) {
        const breaker = this.circuitBreakers.get(path);
        if (!breaker) return false;

        const now = Date.now();
        if (breaker.state === 'open' && now - breaker.lastFailure > 30000) {
            breaker.state = 'half-open';
        }

        return breaker.state === 'open';
    }

    updateCircuitBreaker(path, success) {
        if (!this.circuitBreakers.has(path)) {
            this.circuitBreakers.set(path, {
                failures: 0,
                state: 'closed',
                lastFailure: 0
            });
        }

        const breaker = this.circuitBreakers.get(path);

        if (success) {
            breaker.failures = 0;
            breaker.state = 'closed';
        } else {
            breaker.failures++;
            breaker.lastFailure = Date.now();
            if (breaker.failures >= 5) {
                breaker.state = 'open';
            }
        }
    }

    getStats() {
        return {
            totalRequests: this.requestLog.length,
            uniqueClients: new Set(this.requestLog.map(r => r.clientId)).size,
            circuitBreakers: Array.from(this.circuitBreakers.entries()),
            loadBalancer: this.loadBalancer.getStats()
        };
    }
}
module.exports = APIGateway;