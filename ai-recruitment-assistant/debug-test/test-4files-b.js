
// test-4files-b.js - 请求验证器
const ConfigManager = require('./test-4files-a');

class RequestValidator {
    constructor() {
        this.config = new ConfigManager();
        this.requestCounts = new Map();
        this.blockedIPs = new Set();
    }

    validateRequest(request) {
        const { ip, method, path, headers } = request;

        // 检查IP是否被阻止
        if (this.blockedIPs.has(ip)) {
            return { valid: false, reason: 'IP_BLOCKED', code: 403 };
        }

        // 速率限制检查
        const rateLimit = this.config.get('security.rateLimitPerMinute');
        const currentCount = this.requestCounts.get(ip) || 0;

        if (currentCount >= rateLimit) {
            this.blockedIPs.add(ip);
            return { valid: false, reason: 'RATE_LIMIT_EXCEEDED', code: 429 };
        }

        // 更新请求计数
        this.requestCounts.set(ip, currentCount + 1);

        // 验证必需的头部
        if (!headers.authorization && path.startsWith('/api/protected')) {
            return { valid: false, reason: 'MISSING_AUTH', code: 401 };
        }

        return { valid: true, ip, method, path };
    }

    resetRateLimit(ip) {
        this.requestCounts.delete(ip);
        console.log(`Rate limit reset for IP: ${ip}`);
    }

    getStats() {
        return {
            blockedIPs: this.blockedIPs.size,
            trackedIPs: this.requestCounts.size,
            configStats: this.config.getStats()
        };
    }
}

module.exports = RequestValidator;
