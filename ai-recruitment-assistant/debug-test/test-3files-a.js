
// test-3files-a.js - 模拟数据库
class MockDatabase {
    constructor() {
        this.data = new Map();
        this.queryCount = 0;
    }
    
    async get(key) {
        this.queryCount++;
        console.log(`DB查询 #${this.queryCount}: GET ${key}`);
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return this.data.get(key) || null;
    }
    
    async set(key, value) {
        this.queryCount++;
        console.log(`DB查询 #${this.queryCount}: SET ${key} = ${value}`);
        
        await new Promise(resolve => setTimeout(resolve, 10));
        this.data.set(key, value);
        return true;
    }
    
    getStats() {
        return {
            totalQueries: this.queryCount,
            dataSize: this.data.size
        };
    }
}

module.exports = MockDatabase;
