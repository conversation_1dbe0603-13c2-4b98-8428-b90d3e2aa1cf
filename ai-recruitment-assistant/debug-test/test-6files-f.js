// test-6files-f.js - 监控系统
const APIGateway = require('./test-6files-e');

class MonitoringSystem {
    constructor() {
        this.gateway = new APIGateway();
        this.metrics = new Map();
        this.alerts = [];
        this.thresholds = {
            responseTime: 1000,
            errorRate: 0.1,
            requestRate: 1000
        };

        this.setupMiddleware();
        this.startMetricsCollection();
    }

    setupMiddleware() {
        this.gateway.use(async (request) => {
            const startTime = Date.now();

            // Continue processing
            return {
                continue: true,
                metadata: { startTime }
            };
        });
    }

    startMetricsCollection() {
        setInterval(() => {
            this.collectMetrics();
            this.checkAlerts();
        }, 5000);
    }

    collectMetrics() {
        const stats = this.gateway.getStats();
        const timestamp = Date.now();

        const metric = {
            timestamp,
            requests: stats.totalRequests,
            clients: stats.uniqueClients,
            services: stats.loadBalancer.registry.totalServices,
            healthyServices: stats.loadBalancer.registry.healthyServices,
            events: stats.loadBalancer.registry.messageQueue.eventBus.totalEvents
        };

        this.metrics.set(timestamp, metric);
        console.log(`MonitoringSystem: Collected metrics at ${timestamp}`);

        // Keep only last 100 metrics
        if (this.metrics.size > 100) {
            const oldestKey = Math.min(...this.metrics.keys());
            this.metrics.delete(oldestKey);
        }
    }

    checkAlerts() {
        const recentMetrics = Array.from(this.metrics.values()).slice(-5);
        if (recentMetrics.length < 2) return;

        const latest = recentMetrics[recentMetrics.length - 1];
        const previous = recentMetrics[recentMetrics.length - 2];

        // Check for unhealthy services
        if (latest.healthyServices < latest.services) {
            this.createAlert('SERVICE_UNHEALTHY', `${latest.services - latest.healthyServices} services are unhealthy`);
        }

        // Check request rate
        const requestRate = latest.requests - previous.requests;
        if (requestRate > this.thresholds.requestRate) {
            this.createAlert('HIGH_REQUEST_RATE', `Request rate: ${requestRate}/5s`);
        }
    }

    createAlert(type, message) {
        const alert = {
            id: Date.now(),
            type,
            message,
            timestamp: Date.now(),
            severity: this.getAlertSeverity(type)
        };

        this.alerts.push(alert);
        console.log(`MonitoringSystem: ALERT [${alert.severity}] ${type}: ${message}`);

        // Keep only last 50 alerts
        if (this.alerts.length > 50) {
            this.alerts.shift();
        }
    }

    getAlertSeverity(type) {
        const severityMap = {
            'SERVICE_UNHEALTHY': 'HIGH',
            'HIGH_REQUEST_RATE': 'MEDIUM',
            'HIGH_ERROR_RATE': 'HIGH'
        };
        return severityMap[type] || 'LOW';
    }

    async simulateTraffic() {
        const paths = ['/api/users', '/api/orders', '/api/payments'];
        const clients = ['client-1', 'client-2', 'client-3', 'client-4'];

        for (let i = 0; i < 10; i++) {
            const request = {
                path: paths[Math.floor(Math.random() * paths.length)],
                method: 'GET',
                headers: { 'user-agent': 'test-client' },
                body: {},
                clientId: clients[Math.floor(Math.random() * clients.length)]
            };

            const response = await this.gateway.handleRequest(request);
            console.log(`MonitoringSystem: Simulated request ${i + 1}, response status: ${response.status}`);

            await new Promise(resolve => setTimeout(resolve, 200));
        }
    }

    getFullSystemStats() {
        return {
            monitoring: {
                metricsCount: this.metrics.size,
                alertsCount: this.alerts.length,
                recentAlerts: this.alerts.slice(-3)
            },
            gateway: this.gateway.getStats()
        };
    }
}
module.exports = MonitoringSystem;