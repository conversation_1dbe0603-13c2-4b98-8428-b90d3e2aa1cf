// test-8files-g.js - 通知插件
class NotificationPlugin {
    constructor() {
        this.name = 'notification';
        this.version = '1.0.0';
        this.description = 'Notification system plugin';
        this.dependencies = ['auth'];
        this.notifications = new Map();
        this.subscribers = new Map();
        this.channels = new Set(['email', 'push', 'sms']);
    }

    async activate(context) {
        this.context = context;

        console.log('NotificationPlugin: Activating notification plugin...');

        // Get dependencies
        this.auth = context.getPlugin('auth');

        // Register hooks
        context.registerHook('notification.send', this.send.bind(this));
        context.registerHook('notification.subscribe', this.subscribe.bind(this));
        context.registerHook('notification.unsubscribe', this.unsubscribe.bind(this));

        // Register HTTP routes
        const http = context.registry.modules.get('http');
        if (http) {
            http.addRoute('/notifications', this.notificationsRoute.bind(this));
            http.addRoute('/notifications/subscribe', this.subscribeRoute.bind(this));
            http.addRoute('/notifications/send', this.sendRoute.bind(this));
        }

        // Listen to system events
        context.on('plugin.loaded', this.onPluginLoaded.bind(this));
        context.on('plugin.error', this.onPluginError.bind(this));

        console.log('NotificationPlugin: Notification plugin activated');
        return this;
    }

    async send(data) {
        const { userId, title, message, channel = 'push', priority = 'normal' } = data;

        if (!this.channels.has(channel)) {
            throw new Error(`Invalid notification channel: ${channel}`);
        }

        const notification = {
            id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            userId,
            title,
            message,
            channel,
            priority,
            status: 'pending',
            createdAt: Date.now(),
            sentAt: null
        };

        // Check if user is subscribed to this channel
        const userSubscriptions = this.subscribers.get(userId) || new Set();
        if (!userSubscriptions.has(channel)) {
            notification.status = 'skipped';
            notification.reason = 'User not subscribed to channel';
        } else {
            // Simulate sending
            await this.deliverNotification(notification);
        }

        this.notifications.set(notification.id, notification);

        console.log(`NotificationPlugin: Notification ${notification.id} ${notification.status} for user ${userId}`);
        return notification;
    }

    async deliverNotification(notification) {
        // Simulate delivery delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));

        // Simulate delivery success/failure
        const success = Math.random() > 0.1; // 90% success rate

        if (success) {
            notification.status = 'sent';
            notification.sentAt = Date.now();
        } else {
            notification.status = 'failed';
            notification.error = 'Delivery failed';
        }

        console.log(`NotificationPlugin: Delivered notification ${notification.id} via ${notification.channel}: ${notification.status}`);
    }

    async subscribe(data) {
        const { userId, channel } = data;

        if (!this.channels.has(channel)) {
            throw new Error(`Invalid notification channel: ${channel}`);
        }

        if (!this.subscribers.has(userId)) {
            this.subscribers.set(userId, new Set());
        }

        this.subscribers.get(userId).add(channel);

        console.log(`NotificationPlugin: User ${userId} subscribed to ${channel}`);
        return { subscribed: true, channel };
    }

    async unsubscribe(data) {
        const { userId, channel } = data;

        const userSubscriptions = this.subscribers.get(userId);
        if (userSubscriptions) {
            userSubscriptions.delete(channel);
            if (userSubscriptions.size === 0) {
                this.subscribers.delete(userId);
            }
        }

        console.log(`NotificationPlugin: User ${userId} unsubscribed from ${channel}`);
        return { unsubscribed: true, channel };
    }

    async onPluginLoaded(data) {
        // Send notification to admins when a plugin is loaded
        const adminUsers = ['admin']; // In real app, get from user service

        for (const userId of adminUsers) {
            await this.send({
                userId,
                title: 'Plugin Loaded',
                message: `Plugin ${data.name} has been loaded successfully`,
                channel: 'push',
                priority: 'low'
            });
        }
    }

    async onPluginError(data) {
        // Send notification to admins when a plugin fails
        const adminUsers = ['admin'];

        for (const userId of adminUsers) {
            await this.send({
                userId,
                title: 'Plugin Error',
                message: `Plugin ${data.name} encountered an error`,
                channel: 'email',
                priority: 'high'
            });
        }
    }

    async notificationsRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required');
        }

        const userId = data.auth.session.userId;
        const userNotifications = Array.from(this.notifications.values())
            .filter(n => n.userId === userId)
            .sort((a, b) => b.createdAt - a.createdAt)
            .slice(0, data.limit || 20);

        return {
            notifications: userNotifications,
            total: userNotifications.length
        };
    }

    async subscribeRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required');
        }

        return await this.subscribe({
            userId: data.auth.session.userId,
            channel: data.channel
        });
    }

    async sendRoute(data) {
        if (!data.auth?.authenticated) {
            throw new Error('Authentication required');
        }

        const permissions = data.auth.permissions || [];
        if (!permissions.includes('admin')) {
            throw new Error('Admin permission required');
        }

        return await this.send(data);
    }

    deactivate() {
        console.log('NotificationPlugin: Deactivating notification plugin...');
        this.notifications.clear();
        this.subscribers.clear();
    }

    getStats() {
        return {
            totalNotifications: this.notifications.size,
            totalSubscribers: this.subscribers.size,
            channels: Array.from(this.channels),
            notificationsByStatus: this.getNotificationsByStatus()
        };
    }

    getNotificationsByStatus() {
        const stats = {};
        for (const notification of this.notifications.values()) {
            stats[notification.status] = (stats[notification.status] || 0) + 1;
        }
        return stats;
    }
}

module.exports = {
    name: 'notification',
    version: '1.0.0',
    description: 'Notification system plugin',
    dependencies: ['auth'],
    activate: async (context) => {
        const plugin = new NotificationPlugin();
        return await plugin.activate(context);
    }
};