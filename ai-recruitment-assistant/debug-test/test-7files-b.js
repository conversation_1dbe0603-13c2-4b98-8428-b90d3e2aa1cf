// test-7files-b.js - 聚合根基类
const EventStore = require('./test-7files-a');

class AggregateRoot {
    constructor(id) {
        this.id = id;
        this.version = 0;
        this.uncommittedEvents = [];
        this.eventStore = new EventStore();
    }

    static async load(id, eventStore) {
        const instance = new this(id);
        instance.eventStore = eventStore;

        const snapshot = eventStore.getSnapshot(id);
        let fromVersion = 0;

        if (snapshot) {
            instance.applySnapshot(snapshot);
            fromVersion = snapshot.version;
        }

        const events = eventStore.getEvents(id, fromVersion);
        events.forEach(event => {
            instance.applyEvent(event);
            instance.version = event.version;
        });

        console.log(`AggregateRoot: Loaded ${instance.constructor.name} ${id} at version ${instance.version}`);
        return instance;
    }

    applyEvent(event) {
        const methodName = `on${event.type}`;
        if (typeof this[methodName] === 'function') {
            this[methodName](event.data);
        }
    }

    applySnapshot(snapshot) {
        Object.assign(this, snapshot.data);
        this.version = snapshot.version;
    }

    raiseEvent(type, data, metadata = {}) {
        const event = {
            type,
            data,
            metadata: { ...metadata, aggregateId: this.id }
        };

        this.uncommittedEvents.push(event);
        this.applyEvent({ type, data });
        console.log(`AggregateRoot: Raised event ${type} for ${this.id}`);
    }

    async save() {
        if (this.uncommittedEvents.length === 0) {
            return;
        }

        const events = this.eventStore.append(this.id, this.uncommittedEvents, this.version);
        this.version += this.uncommittedEvents.length;
        this.uncommittedEvents = [];

        // Create snapshot every 10 events
        if (this.version % 10 === 0) {
            this.eventStore.createSnapshot(this.id, this.version, this.getSnapshotData());
        }

        console.log(`AggregateRoot: Saved ${this.constructor.name} ${this.id} at version ${this.version}`);
        return events;
    }

    getSnapshotData() {
        const { id, version, uncommittedEvents, eventStore, ...data } = this;
        return data;
    }
}
module.exports = AggregateRoot;