
// test-4files-d.js - HTTP服务器
const DataProcessor = require('./test-4files-c');

class HTTPServer {
    constructor() {
        this.processor = new DataProcessor();
        this.requestLog = [];
        this.serverStats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            startTime: Date.now()
        };
    }

    async handleRequest(rawRequest) {
        this.serverStats.totalRequests++;

        const request = {
            ip: rawRequest.ip || '127.0.0.1',
            method: rawRequest.method || 'GET',
            path: rawRequest.path || '/',
            headers: rawRequest.headers || {},
            timestamp: Date.now()
        };

        this.requestLog.push(request);
        console.log(`[${request.timestamp}] ${request.method} ${request.path} from ${request.ip}`);

        try {
            const result = await this.processor.processRequest(request);

            if (result.success) {
                this.serverStats.successfulRequests++;
            } else {
                this.serverStats.failedRequests++;
            }

            return {
                ...result,
                requestId: this.serverStats.totalRequests,
                serverTime: Date.now() - request.timestamp
            };
        } catch (error) {
            this.serverStats.failedRequests++;
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                code: 500,
                requestId: this.serverStats.totalRequests
            };
        }
    }

    getFullStats() {
        return {
            server: this.serverStats,
            processor: this.processor.getStats(),
            recentRequests: this.requestLog.slice(-5)
        };
    }
}

module.exports = HTTPServer;
