// test-5files-c.js - 缓存管理器
const DatabasePool = require('./test-5files-b');

class CacheManager {
    constructor() {
        this.dbPool = new DatabasePool();
        this.cache = new Map();
        this.hitCount = 0;
        this.missCount = 0;
        this.ttl = 5000; // 5秒TTL
    }
    
    async get(key) {
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < this.ttl) {
            this.hitCount++;
            this.dbPool.logger.debug(`Cache HIT for key: ${key}`);
            return cached.value;
        }
        
        this.missCount++;
        this.dbPool.logger.debug(`Cache MISS for key: ${key}`);
        
        // 从数据库获取
        const conn = await this.dbPool.getConnection();
        const result = await conn.query(`SELECT * FROM table WHERE key = '${key}'`);
        conn.close();
        
        const value = result.rows[0];
        this.cache.set(key, { value, timestamp: Date.now() });
        
        return value;
    }
    
    set(key, value) {
        this.cache.set(key, { value, timestamp: Date.now() });
        this.dbPool.logger.info(`Cache SET for key: ${key}`);
    }
    
    getStats() {
        return {
            cacheSize: this.cache.size,
            hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
            hits: this.hitCount,
            misses: this.missCount,
            dbStats: this.dbPool.getStats()
        };
    }
}
module.exports = CacheManager;