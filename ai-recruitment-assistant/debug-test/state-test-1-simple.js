
// state-test-1-simple.js - 简单状态变化
class SimpleCounter {
    constructor() {
        this.count = 0;
        this.history = [];
        this.isActive = false;
    }
    
    start() {
        console.log('Counter started');
        this.isActive = true;
        this.history.push({ action: 'start', count: this.count, timestamp: Date.now() });
    }
    
    increment() {
        if (!this.isActive) {
            console.log('Counter not active, cannot increment');
            return false;
        }
        
        this.count++;
        this.history.push({ action: 'increment', count: this.count, timestamp: Date.now() });
        console.log(`Count: ${this.count}`);
        return true;
    }
    
    decrement() {
        if (!this.isActive) {
            console.log('Counter not active, cannot decrement');
            return false;
        }
        
        if (this.count <= 0) {
            console.log('Count cannot go below 0');
            return false;
        }
        
        this.count--;
        this.history.push({ action: 'decrement', count: this.count, timestamp: Date.now() });
        console.log(`Count: ${this.count}`);
        return true;
    }
    
    stop() {
        console.log('Counter stopped');
        this.isActive = false;
        this.history.push({ action: 'stop', count: this.count, timestamp: Date.now() });
    }
    
    getState() {
        return {
            count: this.count,
            isActive: this.isActive,
            historyLength: this.history.length
        };
    }
}

// 测试序列
async function testSimpleState() {
    console.log('🧪 测试简单状态变化...');
    
    const counter = new SimpleCounter();
    
    console.log('初始状态:', counter.getState());
    
    // 执行操作序列
    counter.increment();  // 应该失败
    counter.start();
    counter.increment();  // count = 1
    counter.increment();  // count = 2
    counter.decrement();  // count = 1
    counter.stop();
    counter.increment();  // 应该失败
    
    console.log('最终状态:', counter.getState());
    
    // 问题
    console.log('\n❓ 状态预测问题:');
    console.log('1. 如果现在调用 counter.decrement()，会发生什么？');
    console.log('2. counter.count 的值是多少？');
    console.log('3. counter.history.length 是多少？');
}

testSimpleState();
