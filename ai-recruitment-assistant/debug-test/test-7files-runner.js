// test-7files-runner.js
const ApplicationService = require('./test-7files-g');

async function test7Files() {
    console.log('🧪 测试7文件跨度 - 事件驱动架构...');

    const app = new ApplicationService();

    console.log('📊 执行业务流程...');

    // 1. 注册用户
    const userResult = await app.processRequest({
        type: 'RegisterUser',
        data: { email: '<EMAIL>', name: '<PERSON>' }
    });
    console.log('1. 用户注册结果:', userResult);

    const userId = userResult.data.userId;

    // 2. 用户登录
    const loginResult = await app.processRequest({
        type: 'UserLogin',
        data: { userId }
    });
    console.log('2. 用户登录结果:', loginResult);

    // 3. 创建订单
    const orderResult = await app.processRequest({
        type: 'CreateOrder',
        data: { userId }
    });
    console.log('3. 创建订单结果:', orderResult);

    const orderId = orderResult.data.id;

    // 4. 添加商品到订单
    await app.processRequest({
        type: 'AddItemToOrder',
        data: { orderId, productId: 'product-1', quantity: 2, price: 99.99 }
    });

    await app.processRequest({
        type: 'AddItemToOrder',
        data: { orderId, productId: 'product-2', quantity: 1, price: 149.99 }
    });

    // 5. 处理订单（提交->确认->发货）
    const processResult = await app.processRequest({
        type: 'ProcessOrder',
        data: { orderId }
    });
    console.log('5. 订单处理结果:', processResult);

    // 6. 查询用户信息
    const userInfo = await app.processRequest({
        type: 'GetUserInfo',
        data: { userId }
    });
    console.log('6. 用户信息:', userInfo);

    // 7. 查询订单信息
    const orderInfo = await app.processRequest({
        type: 'GetOrderInfo',
        data: { orderId }
    });
    console.log('7. 订单信息:', orderInfo);

    console.log('📈 完整系统统计:', JSON.stringify(app.getSystemStats(), null, 2));

    console.log('\n❓ 7文件跨度问题:');
    console.log('1. 事件存储中总共有多少个事件？');
    console.log('2. 用户聚合的当前版本是多少？');
    console.log('3. 订单聚合经历了哪些状态变化？');
    console.log('4. 事件处理器创建了多少个投影？');
    console.log('5. 命令处理器执行了多少个命令？');
    console.log('6. 应用服务处理了多少个请求？');
    console.log('7. 整个CQRS+ES架构的数据流是如何工作的？');
    console.log('8. 快照机制在什么时候触发？');
}

test7Files();