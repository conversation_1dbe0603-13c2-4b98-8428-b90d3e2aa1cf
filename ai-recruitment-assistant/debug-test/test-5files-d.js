// test-5files-d.js - 业务逻辑层
const CacheManager = require('./test-5files-c');

class BusinessService {
    constructor() {
        this.cache = new CacheManager();
        this.requestCount = 0;
        this.errorCount = 0;
    }
    
    async processUserRequest(userId, action) {
        this.requestCount++;
        this.cache.dbPool.logger.info(`Processing request ${this.requestCount}: ${action} for user ${userId}`);
        
        try {
            // 获取用户数据
            const userData = await this.cache.get(`user:${userId}`);
            
            // 模拟业务逻辑
            let result;
            switch (action) {
                case 'profile':
                    result = { user: userData, profile: 'User profile data' };
                    break;
                case 'orders':
                    result = { user: userData, orders: ['Order 1', 'Order 2'] };
                    break;
                case 'settings':
                    result = { user: userData, settings: { theme: 'dark' } };
                    break;
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
            
            this.cache.dbPool.logger.info(`Request ${this.requestCount} completed successfully`);
            return { success: true, data: result, requestId: this.requestCount };
            
        } catch (error) {
            this.errorCount++;
            this.cache.dbPool.logger.error(`Request ${this.requestCount} failed`, { error: error.message });
            return { success: false, error: error.message, requestId: this.requestCount };
        }
    }
    
    getStats() {
        return {
            totalRequests: this.requestCount,
            errorCount: this.errorCount,
            successRate: (this.requestCount - this.errorCount) / this.requestCount || 0,
            cacheStats: this.cache.getStats()
        };
    }
}
module.exports = BusinessService;