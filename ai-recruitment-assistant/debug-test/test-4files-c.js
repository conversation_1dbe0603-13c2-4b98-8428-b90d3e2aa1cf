
// test-4files-c.js - 数据处理器
const RequestValidator = require('./test-4files-b');

class DataProcessor {
    constructor() {
        this.validator = new RequestValidator();
        this.cache = new Map();
        this.processingQueue = [];
        this.isProcessing = false;
    }

    async processRequest(request) {
        // 验证请求
        const validation = this.validator.validateRequest(request);
        if (!validation.valid) {
            return {
                success: false,
                error: validation.reason,
                code: validation.code
            };
        }

        // 检查缓存
        const cacheKey = `${request.method}:${request.path}`;
        if (this.cache.has(cacheKey)) {
            return {
                success: true,
                data: this.cache.get(cacheKey),
                fromCache: true,
                processedBy: 'cache'
            };
        }

        // 添加到处理队列
        return new Promise((resolve) => {
            this.processingQueue.push({ request, resolve });
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessing || this.processingQueue.length === 0) {
            return;
        }

        this.isProcessing = true;
        const { request, resolve } = this.processingQueue.shift();

        // 模拟数据处理
        await new Promise(r => setTimeout(r, 50));

        const result = {
            success: true,
            data: `Processed: ${request.path}`,
            fromCache: false,
            processedBy: 'queue',
            timestamp: Date.now()
        };

        // 缓存结果
        const cacheKey = `${request.method}:${request.path}`;
        this.cache.set(cacheKey, result.data);

        this.isProcessing = false;
        resolve(result);

        // 继续处理队列
        if (this.processingQueue.length > 0) {
            setTimeout(() => this.processQueue(), 10);
        }
    }

    getStats() {
        return {
            cacheSize: this.cache.size,
            queueLength: this.processingQueue.length,
            isProcessing: this.isProcessing,
            validatorStats: this.validator.getStats()
        };
    }
}

module.exports = DataProcessor;
