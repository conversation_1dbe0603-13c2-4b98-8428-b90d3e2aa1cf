// test-8files-h.js - 应用程序
const CoreSystem = require('./test-8files-b');
const authPlugin = require('./test-8files-c');
const databasePlugin = require('./test-8files-d');
const blogPlugin = require('./test-8files-e');
const cachePlugin = require('./test-8files-f');
const notificationPlugin = require('./test-8files-g');

class PluginApplication {
    constructor() {
        this.core = new CoreSystem();
        this.isRunning = false;
    }

    async start() {
        if (this.isRunning) {
            return;
        }

        console.log('PluginApplication: Starting application...');

        // Initialize core system
        await this.core.initialize();

        // Load plugins in dependency order
        await this.loadPlugins();

        // Setup application routes
        this.setupApplicationRoutes();

        this.isRunning = true;
        console.log('PluginApplication: Application started successfully');
    }

    async loadPlugins() {
        const plugins = [
            authPlugin,
            databasePlugin,
            cachePlugin,
            notificationPlugin,
            blogPlugin // Load blog last as it depends on auth and database
        ];

        for (const plugin of plugins) {
            try {
                await this.core.loadPlugin(plugin);
                console.log(`PluginApplication: Loaded plugin ${plugin.name}`);
            } catch (error) {
                console.error(`PluginApplication: Failed to load plugin ${plugin.name}:`, error);
            }
        }
    }

    setupApplicationRoutes() {
        const http = this.core.getModule('http');

        // Application info route
        http.addRoute('/app/info', async (data) => {
            return {
                name: this.core.getConfig('system.name'),
                version: this.core.getConfig('system.version'),
                uptime: Date.now() - this.startTime,
                plugins: this.core.registry.getStats()
            };
        });

        // Health check route
        http.addRoute('/app/health', async (data) => {
            return {
                status: 'healthy',
                timestamp: Date.now(),
                system: this.core.getSystemStats()
            };
        });

        console.log('PluginApplication: Application routes setup complete');
    }

    async handleRequest(path, data = {}) {
        if (!this.isRunning) {
            throw new Error('Application not running');
        }

        const http = this.core.getModule('http');
        return await http.handleRequest(path, data);
    }

    async simulateUserWorkflow() {
        console.log('PluginApplication: Simulating user workflow...');

        try {
            // 1. User login
            const loginResult = await this.handleRequest('/auth/login', {
                username: 'admin',
                password: 'admin123'
            });
            console.log('1. Login result:', loginResult);

            const sessionId = loginResult.sessionId;

            // 2. Subscribe to notifications
            await this.handleRequest('/notifications/subscribe', {
                channel: 'push',
                headers: { authorization: `Bearer ${sessionId}` }
            });

            // 3. Create a blog post
            const createPostResult = await this.core.registry.callHook('blog.create', {
                title: 'My First Post',
                content: 'This is the content of my first blog post.',
                authorId: 'admin',
                status: 'published',
                tags: ['tech', 'blog']
            });
            console.log('3. Created post:', createPostResult);

            // 4. Cache the post
            await this.core.registry.callHook('cache.set', {
                key: `post:${createPostResult.id}`,
                value: createPostResult,
                ttl: 300000 // 5 minutes
            });

            // 5. Get post from cache
            const cachedPost = await this.core.registry.callHook('cache.get', {
                key: `post:${createPostResult.id}`
            });
            console.log('5. Cached post:', cachedPost);

            // 6. Add a comment
            const commentResult = await this.core.registry.callHook('blog.comment', {
                postId: createPostResult.id,
                content: 'Great post!',
                authorId: 'admin'
            });
            console.log('6. Added comment:', commentResult);

            // 7. Send notification
            await this.core.registry.callHook('notification.send', {
                userId: 'admin',
                title: 'New Comment',
                message: 'Someone commented on your post',
                channel: 'push'
            });

            // 8. Get user notifications
            const notifications = await this.handleRequest('/notifications', {
                headers: { authorization: `Bearer ${sessionId}` }
            });
            console.log('8. User notifications:', notifications);

            // 9. Get application stats
            const appInfo = await this.handleRequest('/app/info');
            console.log('9. Application info:', appInfo);

        } catch (error) {
            console.error('PluginApplication: Workflow error:', error);
        }
    }

    async stop() {
        if (!this.isRunning) {
            return;
        }

        console.log('PluginApplication: Stopping application...');

        await this.core.shutdown();
        this.isRunning = false;

        console.log('PluginApplication: Application stopped');
    }

    getFullStats() {
        return {
            application: {
                running: this.isRunning,
                startTime: this.startTime
            },
            core: this.core.getSystemStats(),
            plugins: {
                auth: this.core.registry.getPlugin('auth')?.getStats(),
                database: this.core.registry.getPlugin('database')?.getStats(),
                blog: this.core.registry.getPlugin('blog')?.getStats(),
                cache: this.core.registry.getPlugin('cache')?.getStats(),
                notification: this.core.registry.getPlugin('notification')?.getStats()
            }
        };
    }
}

module.exports = PluginApplication;