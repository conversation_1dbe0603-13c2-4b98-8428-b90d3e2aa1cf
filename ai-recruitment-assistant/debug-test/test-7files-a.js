// test-7files-a.js - 事件存储
class EventStore {
    constructor() {
        this.events = [];
        this.snapshots = new Map();
        this.eventId = 0;
        this.subscribers = new Map();
    }

    append(streamId, events, expectedVersion = -1) {
        const streamEvents = this.events.filter(e => e.streamId === streamId);

        if (expectedVersion !== -1 && streamEvents.length !== expectedVersion) {
            throw new Error(`Concurrency conflict. Expected version ${expectedVersion}, got ${streamEvents.length}`);
        }

        const newEvents = events.map(event => ({
            id: ++this.eventId,
            streamId,
            type: event.type,
            data: event.data,
            metadata: event.metadata || {},
            timestamp: Date.now(),
            version: streamEvents.length + events.indexOf(event) + 1
        }));

        this.events.push(...newEvents);

        // Notify subscribers
        newEvents.forEach(event => {
            this.notifySubscribers(event);
        });

        console.log(`EventStore: Appended ${newEvents.length} events to stream ${streamId}`);
        return newEvents;
    }

    getEvents(streamId, fromVersion = 0) {
        return this.events
            .filter(e => e.streamId === streamId && e.version > fromVersion)
            .sort((a, b) => a.version - b.version);
    }

    subscribe(eventType, callback) {
        if (!this.subscribers.has(eventType)) {
            this.subscribers.set(eventType, []);
        }
        this.subscribers.get(eventType).push(callback);
        console.log(`EventStore: Subscribed to ${eventType}`);
    }

    notifySubscribers(event) {
        const callbacks = this.subscribers.get(event.type) || [];
        callbacks.forEach(callback => {
            try {
                callback(event);
            } catch (error) {
                console.error(`EventStore: Error in subscriber for ${event.type}`, error);
            }
        });
    }

    createSnapshot(streamId, version, data) {
        this.snapshots.set(`${streamId}:${version}`, {
            streamId,
            version,
            data,
            timestamp: Date.now()
        });
        console.log(`EventStore: Created snapshot for ${streamId} at version ${version}`);
    }

    getSnapshot(streamId) {
        const snapshots = Array.from(this.snapshots.values())
            .filter(s => s.streamId === streamId)
            .sort((a, b) => b.version - a.version);
        return snapshots[0] || null;
    }

    getStats() {
        return {
            totalEvents: this.events.length,
            totalStreams: new Set(this.events.map(e => e.streamId)).size,
            totalSnapshots: this.snapshots.size,
            subscriberTypes: this.subscribers.size
        };
    }
}
module.exports = EventStore;