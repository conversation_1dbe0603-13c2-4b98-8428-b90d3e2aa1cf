// test-9files-runner.js
const ServiceMesh = require('./test-9files-c');

async function test9Files() {
    console.log('🧪 测试9文件跨度 - 微服务编排系统...');
    
    const serviceMesh = new ServiceMesh();
    
    try {
        // 注册服务
        serviceMesh.registerService('user-service', [
            { id: 'user-1', host: '*********', port: 8080, weight: 1 },
            { id: 'user-2', host: '*********', port: 8080, weight: 1 },
            { id: 'user-3', host: '*********', port: 8080, weight: 2 }
        ], { algorithm: 'weighted', failureThreshold: 3 });
        
        serviceMesh.registerService('order-service', [
            { id: 'order-1', host: '*********', port: 8080, weight: 1 },
            { id: 'order-2', host: '*********', port: 8080, weight: 1 }
        ], { algorithm: 'least-connections', failureThreshold: 5 });
        
        serviceMesh.registerService('payment-service', [
            { id: 'payment-1', host: '*********', port: 8080, weight: 1 }
        ], { algorithm: 'round-robin', failureThreshold: 2 });
        
        console.log('📊 服务注册完成，开始测试调用...');
        
        // 模拟服务调用
        const services = ['user-service', 'order-service', 'payment-service'];
        const results = [];
        
        for (let i = 0; i < 15; i++) {
            const serviceName = services[Math.floor(Math.random() * services.length)];
            
            try {
                const result = await serviceMesh.call(serviceName, {
                    method: 'GET',
                    path: '/api/test',
                    data: { requestId: i + 1 }
                });
                
                results.push({ service: serviceName, success: true, result });
                console.log(`${i + 1}. 调用 ${serviceName} 成功`);
                
            } catch (error) {
                results.push({ service: serviceName, success: false, error: error.message });
                console.log(`${i + 1}. 调用 ${serviceName} 失败: ${error.message}`);
            }
            
            // 添加延迟
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        console.log('📈 完整系统统计:', JSON.stringify(serviceMesh.getStats(), null, 2));
        
        console.log('\n❓ 9文件跨度问题:');
        console.log('1. 服务发现注册了多少个服务实例？');
        console.log('2. 负载均衡器使用了哪些算法？');
        console.log('3. 服务网格的熔断器状态如何？');
        console.log('4. 总共进行了多少次服务调用？');
        console.log('5. 各个服务的成功率是多少？');
        console.log('6. 重试机制是如何工作的？');
        console.log('7. 健康检查的频率和结果如何？');
        console.log('8. 服务实例的权重如何影响负载分配？');
        console.log('9. 整个微服务网格的数据流是如何工作的？');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    }
}

test9Files();
