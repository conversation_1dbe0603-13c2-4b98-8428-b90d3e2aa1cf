// test-6files-runner.js
const MonitoringSystem = require('./test-6files-f');

async function test6Files() {
    console.log('🧪 测试6文件跨度 - 分布式系统架构...');

    const monitoring = new MonitoringSystem();

    console.log('📊 启动系统并模拟流量...');

    // 模拟流量
    await monitoring.simulateTraffic();

    // 等待一些指标收集
    await new Promise(resolve => setTimeout(resolve, 6000));

    console.log('📈 完整系统统计:', JSON.stringify(monitoring.getFullSystemStats(), null, 2));

    console.log('\n❓ 6文件跨度问题:');
    console.log('1. 事件总线处理了多少个事件？');
    console.log('2. 消息队列中有哪些队列？');
    console.log('3. 服务注册中心注册了几个服务？');
    console.log('4. 负载均衡器使用了哪些算法？');
    console.log('5. API网关的熔断器状态如何？');
    console.log('6. 监控系统产生了哪些告警？');
    console.log('7. 整个系统的数据流是如何传递的？');
}

test6Files();