// test-5files-e.js - API网关
const BusinessService = require('./test-5files-d');

class APIGateway {
    constructor() {
        this.businessService = new BusinessService();
        this.rateLimits = new Map();
        this.requestHistory = [];
    }
    
    async handleAPIRequest(request) {
        const { userId, action, ip } = request;
        
        // 速率限制检查
        if (!this.checkRateLimit(ip)) {
            this.businessService.cache.dbPool.logger.warn(`Rate limit exceeded for IP: ${ip}`);
            return { success: false, error: 'RATE_LIMIT_EXCEEDED', code: 429 };
        }
        
        // 记录请求
        this.requestHistory.push({
            timestamp: Date.now(),
            userId,
            action,
            ip
        });
        
        // 处理业务请求
        const result = await this.businessService.processUserRequest(userId, action);
        
        return {
            ...result,
            gateway: {
                timestamp: Date.now(),
                ip,
                rateLimitRemaining: this.getRateLimitRemaining(ip)
            }
        };
    }
    
    checkRateLimit(ip) {
        const now = Date.now();
        const windowMs = 60000; // 1分钟窗口
        const maxRequests = 10;
        
        if (!this.rateLimits.has(ip)) {
            this.rateLimits.set(ip, []);
        }
        
        const requests = this.rateLimits.get(ip);
        const validRequests = requests.filter(time => now - time < windowMs);
        
        if (validRequests.length >= maxRequests) {
            return false;
        }
        
        validRequests.push(now);
        this.rateLimits.set(ip, validRequests);
        return true;
    }
    
    getRateLimitRemaining(ip) {
        const requests = this.rateLimits.get(ip) || [];
        const now = Date.now();
        const validRequests = requests.filter(time => now - time < 60000);
        return Math.max(0, 10 - validRequests.length);
    }
    
    getFullStats() {
        return {
            gateway: {
                totalRequests: this.requestHistory.length,
                uniqueIPs: new Set(this.requestHistory.map(r => r.ip)).size,
                recentRequests: this.requestHistory.slice(-5)
            },
            business: this.businessService.getStats()
        };
    }
}
module.exports = APIGateway;