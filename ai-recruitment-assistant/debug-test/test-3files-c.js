
// test-3files-c.js - 业务服务
const CacheLayer = require('./test-3files-b');

class UserService {
    constructor() {
        this.cache = new CacheLayer();
        this.requestCount = 0;
    }
    
    async getUser(userId) {
        this.requestCount++;
        console.log(`UserService: 获取用户 ${userId} (请求 #${this.requestCount})`);
        
        let user = await this.cache.getValue(`user:${userId}`);
        
        if (!user) {
            // 模拟创建新用户
            user = {
                id: userId,
                name: `User${userId}`,
                createdAt: new Date().toISOString()
            };
            await this.cache.setValue(`user:${userId}`, JSON.stringify(user));
        } else {
            user = JSON.parse(user);
        }
        
        return user;
    }
    
    async updateUser(userId, updates) {
        this.requestCount++;
        console.log(`UserService: 更新用户 ${userId}`);
        
        const user = await this.getUser(userId);
        const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() };
        
        await this.cache.setValue(`user:${userId}`, JSON.stringify(updatedUser));
        return updatedUser;
    }
    
    getStats() {
        return {
            totalRequests: this.requestCount,
            cacheStats: this.cache.getStats()
        };
    }
}

module.exports = UserService;
