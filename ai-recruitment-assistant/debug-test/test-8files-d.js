// test-8files-d.js - 数据库插件
class DatabasePlugin {
    constructor() {
        this.name = 'database';
        this.version = '1.0.0';
        this.description = 'Simple in-memory database plugin';
        this.dependencies = [];
        this.collections = new Map();
        this.indexes = new Map();
        this.queryCount = 0;
    }

    async activate(context) {
        this.context = context;

        console.log('DatabasePlugin: Activating database plugin...');

        // Register hooks
        context.registerHook('db.create', this.create.bind(this));
        context.registerHook('db.read', this.read.bind(this));
        context.registerHook('db.update', this.update.bind(this));
        context.registerHook('db.delete', this.delete.bind(this));
        context.registerHook('db.query', this.query.bind(this));

        // Register HTTP routes
        const http = context.registry.modules.get('http');
        if (http) {
            http.addRoute('/db/collections', this.collectionsRoute.bind(this));
            http.addRoute('/db/query', this.queryRoute.bind(this));
            http.addRoute('/db/stats', this.statsRoute.bind(this));
        }

        // Initialize default collections
        this.initializeCollections();

        console.log('DatabasePlugin: Database plugin activated');
        return this;
    }

    initializeCollections() {
        // Users collection
        this.createCollection('users');
        this.createIndex('users', 'username');
        this.createIndex('users', 'email');

        // Posts collection
        this.createCollection('posts');
        this.createIndex('posts', 'authorId');
        this.createIndex('posts', 'status');

        // Comments collection
        this.createCollection('comments');
        this.createIndex('comments', 'postId');
        this.createIndex('comments', 'authorId');

        console.log('DatabasePlugin: Default collections initialized');
    }

    createCollection(name) {
        if (this.collections.has(name)) {
            throw new Error(`Collection ${name} already exists`);
        }

        this.collections.set(name, new Map());
        this.indexes.set(name, new Map());

        console.log(`DatabasePlugin: Created collection ${name}`);
        return true;
    }

    createIndex(collection, field) {
        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        const collectionIndexes = this.indexes.get(collection);
        collectionIndexes.set(field, new Map());

        console.log(`DatabasePlugin: Created index on ${collection}.${field}`);
    }

    async create(data) {
        const { collection, document } = data;
        this.queryCount++;

        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        const id = document.id || `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const doc = {
            ...document,
            id,
            createdAt: Date.now(),
            updatedAt: Date.now()
        };

        this.collections.get(collection).set(id, doc);
        this.updateIndexes(collection, doc);

        console.log(`DatabasePlugin: Created document ${id} in ${collection}`);
        return doc;
    }

    async read(data) {
        const { collection, id } = data;
        this.queryCount++;

        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        const doc = this.collections.get(collection).get(id);
        if (!doc) {
            throw new Error(`Document ${id} not found in ${collection}`);
        }

        console.log(`DatabasePlugin: Read document ${id} from ${collection}`);
        return doc;
    }

    async update(data) {
        const { collection, id, updates } = data;
        this.queryCount++;

        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        const existingDoc = this.collections.get(collection).get(id);
        if (!existingDoc) {
            throw new Error(`Document ${id} not found in ${collection}`);
        }

        const updatedDoc = {
            ...existingDoc,
            ...updates,
            updatedAt: Date.now()
        };

        this.collections.get(collection).set(id, updatedDoc);
        this.updateIndexes(collection, updatedDoc);

        console.log(`DatabasePlugin: Updated document ${id} in ${collection}`);
        return updatedDoc;
    }

    async delete(data) {
        const { collection, id } = data;
        this.queryCount++;

        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        const doc = this.collections.get(collection).get(id);
        if (!doc) {
            throw new Error(`Document ${id} not found in ${collection}`);
        }

        this.collections.get(collection).delete(id);
        this.removeFromIndexes(collection, doc);

        console.log(`DatabasePlugin: Deleted document ${id} from ${collection}`);
        return { deleted: true, id };
    }

    async query(data) {
        const { collection, filter = {}, limit = 100, offset = 0 } = data;
        this.queryCount++;

        if (!this.collections.has(collection)) {
            throw new Error(`Collection ${collection} does not exist`);
        }

        let results = Array.from(this.collections.get(collection).values());

        // Apply filters
        for (const [field, value] of Object.entries(filter)) {
            results = results.filter(doc => doc[field] === value);
        }

        // Apply pagination
        const total = results.length;
        results = results.slice(offset, offset + limit);

        console.log(`DatabasePlugin: Queried ${collection}, found ${total} results, returned ${results.length}`);

        return {
            results,
            total,
            limit,
            offset
        };
    }

    updateIndexes(collection, document) {
        const collectionIndexes = this.indexes.get(collection);

        for (const [field, index] of collectionIndexes.entries()) {
            const value = document[field];
            if (value !== undefined) {
                if (!index.has(value)) {
                    index.set(value, new Set());
                }
                index.get(value).add(document.id);
            }
        }
    }

    removeFromIndexes(collection, document) {
        const collectionIndexes = this.indexes.get(collection);

        for (const [field, index] of collectionIndexes.entries()) {
            const value = document[field];
            if (value !== undefined && index.has(value)) {
                index.get(value).delete(document.id);
                if (index.get(value).size === 0) {
                    index.delete(value);
                }
            }
        }
    }

    async collectionsRoute(data) {
        return {
            collections: Array.from(this.collections.keys()).map(name => ({
                name,
                documentCount: this.collections.get(name).size,
                indexes: Array.from(this.indexes.get(name).keys())
            }))
        };
    }

    async queryRoute(data) {
        const { collection, filter, limit, offset } = data;
        return await this.query({ collection, filter, limit, offset });
    }

    async statsRoute(data) {
        return this.getStats();
    }

    deactivate() {
        console.log('DatabasePlugin: Deactivating database plugin...');
        this.collections.clear();
        this.indexes.clear();
    }

    getStats() {
        return {
            totalCollections: this.collections.size,
            totalDocuments: Array.from(this.collections.values()).reduce((sum, coll) => sum + coll.size, 0),
            totalQueries: this.queryCount,
            collections: Array.from(this.collections.keys())
        };
    }
}

module.exports = {
    name: 'database',
    version: '1.0.0',
    description: 'Simple in-memory database plugin',
    dependencies: [],
    activate: async (context) => {
        const plugin = new DatabasePlugin();
        return await plugin.activate(context);
    }
};