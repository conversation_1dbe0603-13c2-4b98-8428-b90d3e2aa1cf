// test-8files-a.js - 插件注册表
class PluginRegistry {
    constructor() {
        this.plugins = new Map();
        this.hooks = new Map();
        this.dependencies = new Map();
        this.loadOrder = [];
        this.eventEmitter = {
            listeners: new Map(),
            emit: (event, data) => {
                const callbacks = this.eventEmitter.listeners.get(event) || [];
                callbacks.forEach(callback => callback(data));
            },
            on: (event, callback) => {
                if (!this.eventEmitter.listeners.has(event)) {
                    this.eventEmitter.listeners.set(event, []);
                }
                this.eventEmitter.listeners.get(event).push(callback);
            }
        };
    }

    register(plugin) {
        if (this.plugins.has(plugin.name)) {
            throw new Error(`Plugin ${plugin.name} is already registered`);
        }

        // Validate plugin structure
        this.validatePlugin(plugin);

        this.plugins.set(plugin.name, {
            ...plugin,
            status: 'registered',
            loadedAt: null,
            instance: null
        });

        this.dependencies.set(plugin.name, plugin.dependencies || []);

        console.log(`PluginRegistry: Registered plugin ${plugin.name} v${plugin.version}`);
        this.eventEmitter.emit('plugin.registered', { name: plugin.name, plugin });

        return true;
    }

    validatePlugin(plugin) {
        const required = ['name', 'version', 'main', 'activate'];
        for (const field of required) {
            if (!plugin[field]) {
                throw new Error(`Plugin missing required field: ${field}`);
            }
        }

        if (typeof plugin.activate !== 'function') {
            throw new Error('Plugin activate must be a function');
        }
    }

    async load(pluginName) {
        const plugin = this.plugins.get(pluginName);
        if (!plugin) {
            throw new Error(`Plugin ${pluginName} not found`);
        }

        if (plugin.status === 'loaded') {
            return plugin.instance;
        }

        // Load dependencies first
        const deps = this.dependencies.get(pluginName) || [];
        for (const dep of deps) {
            if (!this.plugins.has(dep)) {
                throw new Error(`Dependency ${dep} not found for plugin ${pluginName}`);
            }
            await this.load(dep);
        }

        console.log(`PluginRegistry: Loading plugin ${pluginName}...`);

        try {
            // Create plugin context
            const context = this.createPluginContext(pluginName);

            // Activate plugin
            const instance = await plugin.activate(context);

            plugin.instance = instance;
            plugin.status = 'loaded';
            plugin.loadedAt = Date.now();

            this.loadOrder.push(pluginName);

            console.log(`PluginRegistry: Plugin ${pluginName} loaded successfully`);
            this.eventEmitter.emit('plugin.loaded', { name: pluginName, instance });

            return instance;

        } catch (error) {
            plugin.status = 'error';
            console.error(`PluginRegistry: Failed to load plugin ${pluginName}:`, error);
            this.eventEmitter.emit('plugin.error', { name: pluginName, error });
            throw error;
        }
    }

    createPluginContext(pluginName) {
        return {
            pluginName,
            registry: this,
            registerHook: (hookName, callback) => this.registerHook(pluginName, hookName, callback),
            callHook: (hookName, data) => this.callHook(hookName, data),
            getPlugin: (name) => this.getPlugin(name),
            emit: (event, data) => this.eventEmitter.emit(event, data),
            on: (event, callback) => this.eventEmitter.on(event, callback)
        };
    }

    registerHook(pluginName, hookName, callback) {
        if (!this.hooks.has(hookName)) {
            this.hooks.set(hookName, []);
        }

        this.hooks.get(hookName).push({
            plugin: pluginName,
            callback,
            priority: callback.priority || 10
        });

        // Sort by priority
        this.hooks.get(hookName).sort((a, b) => a.priority - b.priority);

        console.log(`PluginRegistry: Plugin ${pluginName} registered hook ${hookName}`);
    }

    async callHook(hookName, data) {
        const hooks = this.hooks.get(hookName) || [];
        let result = data;

        for (const hook of hooks) {
            try {
                const hookResult = await hook.callback(result);
                if (hookResult !== undefined) {
                    result = hookResult;
                }
            } catch (error) {
                console.error(`PluginRegistry: Error in hook ${hookName} from plugin ${hook.plugin}:`, error);
            }
        }

        return result;
    }

    getPlugin(name) {
        const plugin = this.plugins.get(name);
        return plugin?.status === 'loaded' ? plugin.instance : null;
    }

    unload(pluginName) {
        const plugin = this.plugins.get(pluginName);
        if (!plugin || plugin.status !== 'loaded') {
            return false;
        }

        // Call deactivate if available
        if (plugin.instance && typeof plugin.instance.deactivate === 'function') {
            plugin.instance.deactivate();
        }

        // Remove hooks
        for (const [hookName, hooks] of this.hooks.entries()) {
            this.hooks.set(hookName, hooks.filter(h => h.plugin !== pluginName));
        }

        plugin.status = 'unloaded';
        plugin.instance = null;

        this.loadOrder = this.loadOrder.filter(name => name !== pluginName);

        console.log(`PluginRegistry: Plugin ${pluginName} unloaded`);
        this.eventEmitter.emit('plugin.unloaded', { name: pluginName });

        return true;
    }

    getStats() {
        return {
            totalPlugins: this.plugins.size,
            loadedPlugins: Array.from(this.plugins.values()).filter(p => p.status === 'loaded').length,
            totalHooks: this.hooks.size,
            loadOrder: this.loadOrder.slice()
        };
    }
}
module.exports = PluginRegistry;