
// test-4files-runner.js
const HTTPServer = require('./test-4files-d');

async function test4Files() {
    console.log('🧪 测试4文件跨度 - HTTP请求处理链...');

    const server = new HTTPServer();

    // 测试请求序列
    const requests = [
        { ip: '***********', method: 'GET', path: '/api/users', headers: {} },
        { ip: '***********', method: 'GET', path: '/api/users', headers: {} }, // 重复请求，应该从缓存
        { ip: '***********', method: 'POST', path: '/api/protected/data', headers: {} }, // 缺少认证
        { ip: '***********', method: 'POST', path: '/api/protected/data', headers: { authorization: 'Bearer token' } },
        { ip: '***********', method: 'GET', path: '/api/products', headers: {} }
    ];

    console.log('📊 执行请求序列:');
    for (let i = 0; i < requests.length; i++) {
        const result = await server.handleRequest(requests[i]);
        console.log(`${i + 1}. 请求结果:`, JSON.stringify(result, null, 2));

        // 添加延迟观察异步处理
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('📈 完整统计:', JSON.stringify(server.getFullStats(), null, 2));

    console.log('\n❓ 4文件跨度问题:');
    console.log('1. 如果***********再发送100个请求，会发生什么？');
    console.log('2. 第二个GET /api/users请求为什么比第一个快？');
    console.log('3. 配置管理器中有几个override？');
    console.log('4. 数据处理队列的工作机制是什么？');
}

test4Files();
