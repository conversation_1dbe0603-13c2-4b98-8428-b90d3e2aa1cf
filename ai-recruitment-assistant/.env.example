# AI招聘助手系统 - 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# ==================== 服务器配置 ====================
PORT=3000
HOST=localhost
NODE_ENV=development

# ==================== 数据库配置 ====================
# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 数据库连接配置
DB_CONNECTION_TIMEOUT=30000
DB_MAX_CONNECTIONS=10

# ==================== AI服务配置 ====================
# DeepSeek API 配置
DEEPSEEK_API_KEY=your-deepseek-api-key
LLM_API_ENDPOINT=https://api.deepseek.com/v1

# AI 参数配置
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.7
AI_TIMEOUT=30000

# ==================== 缓存配置 ====================
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ==================== 安全配置 ====================
# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION=24h

# 速率限制配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# ==================== 性能配置 ====================
REQUEST_TIMEOUT=30000
MAX_REQUEST_SIZE=10mb
COMPRESSION_ENABLED=true

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_CONSOLE=true
LOG_FILE=false
LOG_FILE_PATH=./logs/app.log

# ==================== 业务配置 ====================
MAX_RECOMMENDATIONS=10
SESSION_TIMEOUT=1800000
MAX_UPLOAD_SIZE=5mb
SUPPORTED_FILE_TYPES=pdf,doc,docx,txt

# ==================== 开发配置 ====================
# 开发环境特殊配置
DEBUG=false
MOCK_AI_RESPONSES=false
ENABLE_API_DOCS=true
